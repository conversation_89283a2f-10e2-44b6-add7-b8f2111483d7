package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "检查端口占用请求", description = "检查端口占用请求")
public class AdminBaseCheckPortReqVO {
    @ApiModelProperty(value = "ecsServerId")
    private Long ecsServerId;
    @ApiModelProperty(value = "port")
    private Integer port;
}
