<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="javaFileEncoding" value="UTF-8"/>

        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="tk.mybatis.mapper.common.Mapper"/>
            <property name="caseSensitive" value="true"/>
            <property name="forceAnnotation" value="true"/>
            <property name="beginningDelimiter" value=""/>
            <property name="endingDelimiter" value=""/>
            <property name="lombok" value="Getter,Setter,Accessors"/>
        </plugin>

        <plugin type="tk.mybatis.mapper.generator.TemplateFilePlugin">
            <property name="targetProject" value="../mega-cloud-common/src/main/java"/>
            <property name="targetPackage" value="com.mega.platform.cloud.common.mapper"/>
            <property name="templatePath" value="mapper.ftl"/>
            <property name="mapperSuffix" value="Mapper"/>
            <property name="fileName" value="${tableClass.shortClassName}${mapperSuffix}.java"/>
        </plugin>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="************************************************"
                        userId="admin"
                        password="Mangosteen0!">
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.mega.platform.cloud.data.entity"
                            targetProject="../mega-cloud-data/src/main/java">
        </javaModelGenerator>

        <table tableName="services">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="push_app_config">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="push_record">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="payment_product_config">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="payment_callback_log">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>

        <table tableName="payment_app_config">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>

        <table tableName="project">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>

        <table tableName="project_app">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        
        <!-- admin模块 -->
        <table tableName="admin_user_operate_log">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>

        <table tableName="admin_role">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="admin_role_project_binding">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="admin_role_router_binding">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>

        <table tableName="admin_router">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>

        <table tableName="admin_user">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="admin_user_project_binding">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="admin_user_role_binding">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="admin_user_router_binding">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="project_url_pattern">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="project_app_permission">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="services_group">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="services_group_tag_relation">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="project_app_template">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="project_app_permission_template">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>

        <!--↓↓↓ ecs管理 ↓↓↓-->
        <table tableName="ecs_server">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="ecs_server_command">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="ecs_server_config">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="ecs_server_image">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="ecs_server_log">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="ecs_server_region">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="ecs_server_tag_relation">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="ecs_server_image_tag_relation">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <!--↑↑↑ eecs管理 ↑↑↑-->
        
        


        <!--↓↓↓ microservices ↓↓↓-->
        <table tableName="services_group">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="services">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="ecs_server">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="jenkins_user">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="jenkins_ssh_server">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="jenkins_services">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="jenkins_view">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="jenkins_job_template">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="jenkins_job">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="jenkins_job_template_param_value">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="jenkins_task_group">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="jenkins_task">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="jenkins_task_log">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <!--↑↑↑ microservices ↑↑↑-->
        <!--↓↓↓ monitor ↓↓↓-->
        <table tableName="monitor_metrics">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="monitor_rule">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="monitor_alarm">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="monitor_notify">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="monitor_data">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <!--↑↑↑ monitor ↑↑↑-->

    </context>
</generatorConfiguration>
