package com.mega.platform.cloud.admin.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mega.platform.cloud.admin.service.AdminAppAuthService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.entity.AuthAppConfig;
import com.mega.platform.cloud.data.entity.AuthSmsTemplateConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/api/{projectId}/third/auth")
@Api(tags = "第三方Auth配置管理")
@Slf4j
@RequiredArgsConstructor
public class AdminThirdAuthController {
    private final AdminAppAuthService adminAppService;
    @ApiOperation("第三方Auth配置管理 创建三方登录配置")
    @PostMapping("/config/create")
    public Result<?> authConfigCreate(@PathVariable Long projectId, @RequestBody AdminAppAuthConfigReqVO reqVO) throws JsonProcessingException {
        try {
            adminAppService.authConfigCreate(projectId, reqVO);
            return Results.success();
        } catch (Exception e) {
            return Results.error(0, "请勿重复创建", null);
        }

    }
    @ApiOperation("第三方Auth配置管理 删除三方登录配置")
    @PostMapping("/config/delete")
    public Result<?> authConfigDelete(@PathVariable Long projectId, @RequestBody AdminAppAuthHandleConfigReqVO reqVO) {
        adminAppService.authConfigDelete(reqVO.getProjectAppId(), reqVO.getThirdPlatformId());
        return Results.success();
    }

    @ApiOperation("第三方Auth配置管理 更新三方登录配置")
    @PostMapping("/config/edit")
    public Result<?> authConfigUpdate(@PathVariable Long projectId, @RequestBody AdminAppAuthConfigReqVO reqVO) throws JsonProcessingException {
        adminAppService.authConfigUpdate(projectId, reqVO);
        return Results.success();
    }
    @ApiOperation("第三方Auth配置管理 查询三方登录配置列表")
    @PostMapping("/config/list")
    public Result<List<AdminAppAuthConfigListReqVO>> authConfigList(@PathVariable Long projectId, @RequestBody AdminAppAuthHandleConfigReqVO reqVO) {
        List<AdminAppAuthConfigListReqVO> configs = adminAppService.authConfigList(projectId, reqVO);
        return Results.success(configs);
    }

    @PostMapping("/sms/config/create")
    @ApiOperation("短信模板管理 新增短信模板配置")
    public Result<?> create(@RequestBody AdminAppAuthSmsReqVO reqVO) {
        adminAppService.save(reqVO);
        return Results.success();
    }

    @PostMapping("/sms/config/edit")
    @ApiOperation("短信模板管理 更新短信模板配置")
    public Result<?> update(@RequestBody AdminAppAuthSmsReqVO reqVO) {
        adminAppService.updateById(reqVO);
        return Results.success();
    }

    @PostMapping("/sms/config/delete")
    @ApiOperation("短信模板管理 删除短信模板配置")
    public Result<?> delete(@RequestBody AdminAppAuthSmsDeleteReqVO reqVO) {
        adminAppService.removeById(reqVO.getConfigId());
        return Results.success();
    }

    @PostMapping("/sms/config/list")
    @ApiOperation("短信模板管理 查询所有短信模板配置")
    public Result<List<AuthSmsTemplateConfig>> list(@RequestBody AdminAppAuthSmsReqVO reqVO, @PathVariable String projectId) {
        return Results.success(adminAppService.smsConfigList(reqVO));
    }
}
