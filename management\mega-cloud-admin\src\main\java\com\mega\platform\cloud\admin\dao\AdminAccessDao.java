package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.admin.dto.AdminRoleProjectBindingDTO;
import com.mega.platform.cloud.admin.dto.AdminRoleRouterBindingDTO;
import com.mega.platform.cloud.admin.dto.AdminUserProjectBindingDTO;
import com.mega.platform.cloud.admin.dto.AdminUserRoleBindingDTO;
import com.mega.platform.cloud.data.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限管理数据访问层
 */
@Mapper
public interface AdminAccessDao {

    // ==================== 用户管理 ====================    
    /**
     * 查询用户列表
     */
    List<AdminUser> selectUserList(@Param("username") String username,
                                  @Param("delsign") Integer delsign);
    
    /**
     * 插入用户
     */
    int insertUser(AdminUser adminUser);
    
    /**
     * 删除用户（逻辑删除）
     */
    int deleteUser(@Param("userId") Long userId);
    
    /**
     * 更新用户密码
     */
    int updateUserPassword(@Param("userId") Long userId, 
                          @Param("password") String password);

    // ==================== 用户项目绑定管理 ====================    
    /**
     * 查询用户项目绑定列表
     */
    List<AdminUserProjectBindingDTO> selectUserProjectBindingList(@Param("userId") Long userId,
                                                               @Param("delsign") Integer delsign);
    
    /**
     * 删除用户项目绑定
     */
//    int deleteUserProjectBinding(@Param("userId") Long userId);
    
    /**
     * 批量插入用户项目绑定
     */
    int batchInsertUserProjectBinding(@Param("bindings") List<AdminUserProjectBinding> bindings);
    
    /**
     * 插入或更新用户项目绑定（冲突时更新delsign）
     */
    int insertOrUpdateUserProjectBinding(AdminUserProjectBinding binding);

    // ==================== 用户路由绑定管理 ====================
    
    /**
     * 查询用户路由绑定列表
     */
    List<AdminUserRouterBinding> selectUserRouteBindingList(@Param("userId") Long userId,
                                                            @Param("routerId") Long routerId,
                                                            @Param("delsign") Integer delsign);
    
    /**
     * 删除用户路由绑定
     */
    int deleteUserRouteBinding(@Param("userId") Long userId);
    
    /**
     * 批量插入用户路由绑定
     */
    int batchInsertUserRouteBinding(@Param("bindings") List<AdminUserRouterBinding> bindings);
    
    /**
     * 插入或更新用户路由绑定（冲突时更新delsign）
     */
    int insertOrUpdateUserRouterBinding(AdminUserRouterBinding binding);

    // ==================== 角色管理 ====================
    
    /**
     * 查询角色列表
     */
    List<AdminRole> selectRoleList(@Param("name") String name,
                                  @Param("delsign") Integer delsign);
    
    /**
     * 插入角色
     */
    int insertRole(AdminRole adminRole);
    
    /**
     * 删除角色（逻辑删除）
     */
    int deleteRole(@Param("roleId") Long roleId);

    // ==================== 角色路由绑定管理 ====================
    
    /**
     * 查询角色路由绑定列表
     */
    List<AdminRoleRouterBindingDTO> selectRoleRouteBindingList(@Param("roleId") Long roleId,
                                                            @Param("routerId") Long routerId,
                                                            @Param("delsign") Integer delsign);
    
    /**
     * 删除角色路由绑定
     */
    int deleteRoleRouteBinding(@Param("roleId") Long roleId);
    
    /**
     * 批量插入角色路由绑定
     */
    int batchInsertRoleRouteBinding(@Param("bindings") List<AdminRoleRouterBinding> bindings);
    
    /**
     * 插入或更新角色路由绑定（冲突时更新delsign）
     */
    int insertOrUpdateRoleRouterBinding(@Param("binding") AdminRoleRouterBinding binding);

    // ==================== 角色项目绑定管理 ====================
    
    /**
     * 查询角色项目绑定列表
     */
    List<AdminRoleProjectBindingDTO> selectRoleProjectBindingList(@Param("roleId") Long roleId,
                                                               @Param("projectId") Long projectId,
                                                               @Param("delsign") Integer delsign);
    
    /**
     * 删除角色项目绑定
     */
    int deleteRoleProjectBinding(@Param("roleId") Long roleId);
    
    /**
     * 插入或更新角色项目绑定（冲突时更新delsign）
     */
    int insertOrUpdateRoleProjectBinding(AdminRoleProjectBinding binding);

    // ==================== 角色用户绑定管理 ====================
    
    /**
     * 查询角色用户绑定列表
     */
    List<AdminUserRoleBindingDTO> selectRoleUserBindingList(@Param("roleId") Long roleId,
                                                         @Param("userId") Long userId,
                                                         @Param("delsign") Integer delsign);
    
    /**
     * 删除角色用户绑定
     */
    int deleteRoleUserBinding(@Param("roleId") Long roleId);
    
    /**
     * 插入或更新角色用户绑定（冲突时更新delsign）
     */
    int insertOrUpdateRoleUserBinding(AdminUserRoleBinding binding);

    // ==================== 路由管理 ====================
    
    /**
     * 查询路由列表
     */
    List<AdminRouter> selectRouterList(@Param("backendPath") String backendPath,
                                      @Param("frontendPath") String frontendPath,
                                      @Param("frontendName") String frontendName,
                                      @Param("description") String description,
                                      @Param("parentAdminRouterId") Long parentAdminRouterId,
                                      @Param("delsign") Integer delsign);

    /**
     * 查询路由详情
     */
    AdminRouter selectRouterById(@Param("routerId") Long routerId);

}