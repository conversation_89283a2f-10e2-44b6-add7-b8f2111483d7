package com.mega.platform.cloud.monitor.controller;

import com.mega.platform.cloud.client.monitor.MonitorMetricsClient;
import com.mega.platform.cloud.common.utils.RequestUtils;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.dto.monitor.MetricsCollectDTO;
import com.mega.platform.cloud.data.vo.monitor.MetricsEcsCollectReqVO;
import com.mega.platform.cloud.data.vo.monitor.MetricsServicesCollectReqVO;
import com.mega.platform.cloud.monitor.service.metrics.MetricsCollectService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import static com.mega.platform.cloud.common.constant.MonitorConstants.METRICS_SOURCE_TYPE_ECS;
import static com.mega.platform.cloud.common.constant.MonitorConstants.METRICS_SOURCE_TYPE_SERVICES;

@Api(tags = "指标接口")
@Slf4j
@RestController
@RequiredArgsConstructor
public class MonitorMetricsController implements MonitorMetricsClient {

    private final MetricsCollectService metricsCollectService;

    @Override
    public Result<?> metricsEcsCollect(HttpServletRequest request, MetricsEcsCollectReqVO reqVO) throws Exception {
        MetricsCollectDTO dto = new MetricsCollectDTO();
        BeanUtils.copyProperties(reqVO, dto);
        dto.setClientIp(RequestUtils.getClientIp(request));
        dto.setSourceType(METRICS_SOURCE_TYPE_ECS);
        dto.setCollectTime(reqVO.getCollectTime());
        metricsCollectService.metricsCollect(dto);
        return Results.success();
    }

    @Override
    public Result<?> metricsServicesCollect(HttpServletRequest request, MetricsServicesCollectReqVO reqVO) throws Exception {
        MetricsCollectDTO dto = new MetricsCollectDTO();
        BeanUtils.copyProperties(reqVO, dto);
        dto.setSourceType(METRICS_SOURCE_TYPE_SERVICES);
        dto.setCollectTime(reqVO.getCollectTime());
        metricsCollectService.metricsCollect(dto);
        return Results.success();
    }
}
