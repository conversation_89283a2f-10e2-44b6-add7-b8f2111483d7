package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.admin.vo.AdminEcsConfigListReqVO;
import com.mega.platform.cloud.admin.vo.AdminEcsConfigListRespVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AdminEcsConfigDao {
    
    /**
     * 根据条件查询ECS配置列表
     * @param reqVO 查询条件
     * @return ECS配置列表
     */
    List<AdminEcsConfigListRespVO.EcsConfigItemVO> selectEcsConfigListByCondition(@Param("req") AdminEcsConfigListReqVO reqVO);
    
    /**
     * 根据条件统计ECS配置数量
     * @param reqVO 查询条件
     * @return ECS配置数量
     */
    int countEcsConfigListByCondition(@Param("req") AdminEcsConfigListReqVO reqVO);

}