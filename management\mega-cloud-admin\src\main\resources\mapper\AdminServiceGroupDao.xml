<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminServiceGroupDao">
        
    <!-- 基础字段 -->
    <sql id="BaseColumns">
        id, name, project_id, project_app_id, services_update_type, services_env, 
        services_log_format_id, services_alive_num, jenkins_services_id, jenkins_template_id, 
        is_self, admin_user_id, remark, status, running_status, real_running_status, 
        check_alive_type, use_jenkins, create_time, update_time, delsign
    </sql>
    
    <!-- 根据项目ID和服务组名查询服务组 -->
    <select id="selectServicesGroupByProjectIdAndName" resultType="com.mega.platform.cloud.data.entity.ServicesGroup">
        SELECT <include refid="BaseColumns"/>
        FROM services_group
        WHERE project_id = #{projectId} AND name = #{name} AND delsign = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>
    
    <!-- 根据ID查询服务组 -->
    <select id="selectServicesGroupById" resultType="com.mega.platform.cloud.data.entity.ServicesGroup">
        SELECT <include refid="BaseColumns"/>
        FROM services_group
        WHERE id = #{id} AND delsign = 0
    </select>
    
    <!-- 根据项目ID查询服务组列表 -->
    <select id="selectServicesGroupListByProjectId" resultType="com.mega.platform.cloud.data.entity.ServicesGroup">
        SELECT <include refid="BaseColumns"/>
        FROM services_group
        WHERE project_id = #{projectId} AND delsign = 0
        ORDER BY create_time DESC
    </select>
    
    <!-- 检查服务组下是否有运行中的服务 -->
    <select id="countOnlineServicesByServicesGroupId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM services
        WHERE services_group_id = #{servicesGroupId}
          AND `status` = 1
          AND delsign = 0
    </select>
    
    <!-- 检查服务组下是否有服务 -->
    <select id="countServicesByServicesGroupId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM services
        WHERE services_group_id = #{servicesGroupId}
          AND delsign = 0
    </select>
    
    <!-- 更新服务组状态 -->
    <update id="updateServicesGroupStatus">
        UPDATE services_group
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id} AND delsign = 0
    </update>
    
    <!-- 逻辑删除服务组 -->
    <update id="deleteServicesGroupById">
        UPDATE services_group
        SET delsign = 1, update_time = NOW()
        WHERE id = #{id} AND delsign = 0
    </update>
    
    <!-- 更新服务组信息（编辑时使用） -->
    <update id="updateServicesGroupSelective">
        UPDATE services_group
        <set>
            <if test="servicesGroup.name != null and servicesGroup.name != ''">
                name = #{servicesGroup.name},
            </if>
            <if test="servicesGroup.servicesUpdateType != null">
                services_update_type = #{servicesGroup.servicesUpdateType},
            </if>
            <if test="servicesGroup.servicesEnv != null and servicesGroup.servicesEnv != ''">
                services_env = #{servicesGroup.servicesEnv},
            </if>
            <if test="servicesGroup.servicesLogFormatId != null">
                services_log_format_id = #{servicesGroup.servicesLogFormatId},
            </if>
            <if test="servicesGroup.servicesAliveNum != null">
                services_alive_num = #{servicesGroup.servicesAliveNum},
            </if>
            <if test="servicesGroup.jenkinsServicesId != null">
                jenkins_services_id = #{servicesGroup.jenkinsServicesId},
            </if>
            <if test="servicesGroup.jenkinsTemplateId != null">
                jenkins_template_id = #{servicesGroup.jenkinsTemplateId},
            </if>
            <if test="servicesGroup.isSelf != null">
                is_self = #{servicesGroup.isSelf},
            </if>
            <if test="servicesGroup.adminUserId != null">
                admin_user_id = #{servicesGroup.adminUserId},
            </if>
            <if test="servicesGroup.remark != null">
                remark = #{servicesGroup.remark},
            </if>
            <if test="servicesGroup.checkAliveType != null">
                check_alive_type = #{servicesGroup.checkAliveType},
            </if>
            <if test="servicesGroup.useJenkins != null">
                use_jenkins = #{servicesGroup.useJenkins},
            </if>
            <if test="servicesGroup.sort != null">
                sort = #{servicesGroup.sort},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{servicesGroup.id} AND delsign = 0
    </update>

    <!-- insertServicesGroupTagRelation -->
    <insert id="insertServicesGroupTagRelation">
        <if test="tagRelations != null and tagRelations.size() > 0">
            INSERT INTO services_group_tag_relation (service_group_id, service_tag_id, create_time, update_time, delsign)
            VALUES
            <foreach collection="tagRelations" item="relation" separator=",">
                (#{relation.serviceGroupId}, #{relation.serviceTagId}, #{relation.createTime}, #{relation.updateTime}, #{relation.delsign})
            </foreach>
            ON DUPLICATE KEY UPDATE
                update_time = VALUES(update_time),
                delsign = VALUES(delsign)
        </if>
    </insert>

    <!-- selectServicesGroupTagRelation --> 
    <select id="selectServicesGroupTagRelation" resultType="com.mega.platform.cloud.data.entity.ServicesGroupTagRelation">
        SELECT id, service_group_id, service_tag_id, create_time, update_time, delsign
        FROM services_group_tag_relation
        WHERE service_group_id = #{servicesGroupId} AND delsign = 0
    </select>

    <select id="selectServicesGroups" resultType="com.mega.platform.cloud.admin.vo.AdminServicesGroupRespVO">
        SELECT
        g.*,
        GROUP_CONCAT(DISTINCT CASE WHEN d_tag.dic_cate_id = 2007 THEN r.service_tag_id END) AS tagIds,
        GROUP_CONCAT(DISTINCT CASE WHEN d_tag.dic_cate_id = 2009 THEN r.service_tag_id END) AS customizeTagIds,
        COUNT(DISTINCT s.id) AS serviceCount,
        COUNT(DISTINCT CASE WHEN s.running_status = 1 AND s.real_running_status = 1 THEN s.id END) AS runningServiceCount,
        d.id AS envId,
        a.username AS adminUserName,
        pa.name AS projectAppName,
        p.name AS projectName
        FROM services_group g
        LEFT JOIN services_group_tag_relation r
        ON g.id = r.service_group_id AND r.delsign = 0
        LEFT JOIN services s
        ON g.id = s.services_group_id AND s.delsign = 0
        LEFT JOIN dic d ON g.services_env = d.value AND d.dic_cate_id = 2005 AND d.delsign = 0
        LEFT JOIN dic d_tag ON r.service_tag_id = d_tag.id AND d_tag.delsign = 0
        LEFT JOIN admin_user a ON a.`id` = g.admin_user_id
        LEFT JOIN project_app pa ON pa.`id` = g.project_app_id
        LEFT JOIN project p ON p.`id` = g.project_id
        WHERE g.delsign = 0
        <if test="projectId != null">
            AND g.project_id = #{projectId}
        </if>

        <if test="projectId == null and reqVO.projectId != null">
            AND g.project_id = #{reqVO.projectId}
        </if>

        <if test="reqVO.projectAppId != null">
            AND g.project_app_id = #{reqVO.projectAppId}
        </if>
        <if test="reqVO.keyword != null and reqVO.keyword != ''">
            AND (g.name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR g.remark LIKE CONCAT('%', #{reqVO.keyword}, '%'))
        </if>
        <if test="reqVO.adminUserId != null">
            AND g.admin_user_id = #{reqVO.adminUserId}
        </if>
        <if test="reqVO.isSelf != null">
            AND g.is_self = #{reqVO.isSelf}
        </if>
        <if test="reqVO.onlineStatus != null">
            AND g.status = #{reqVO.onlineStatus}
        </if>
        <if test="reqVO.runStatus != null">
            AND g.running_status = #{reqVO.runStatus}
        </if>
        <if test="reqVO.realRunStatus != null">
            AND g.real_running_status = #{reqVO.realRunStatus}
        </if>
        <if test="reqVO.tagIds != null and reqVO.tagIds != ''">
            AND EXISTS (
            SELECT 1 FROM services_group_tag_relation r2
            WHERE r2.service_group_id = g.id
            AND r2.delsign = 0
            AND r2.service_tag_id IN (#{reqVO.tagIds})
            )
        </if>
        <if test="reqVO.customizeTagIds != null and reqVO.customizeTagIds != ''">
            AND EXISTS (
            SELECT 1 FROM services_group_tag_relation r2
            WHERE r2.service_group_id = g.id
            AND r2.delsign = 0
            AND r2.service_tag_id IN (#{reqVO.customizeTagIds})
            )
        </if>
        GROUP BY g.id
        ORDER BY g.sort ASC;
    </select>
    <select id="selectListServicesJobGroups"
            resultType="com.mega.platform.cloud.admin.vo.AdminServicesGroupRunningJobRespVO">
        SELECT
            sg.id AS services_group_id,
            sg.name AS services_group_name,
            sg.project_id,
            sg.project_app_id,
            sg.services_env,
            sg.admin_user_id,
            jtg.id AS jenkins_task_group_id,
            jtg.action,
            jtg.is_success,
            jtg.failed_reason,
            jtg.complete_time
        FROM
            services_group sg
                LEFT JOIN (
                SELECT jt1.*
                FROM jenkins_task_group jt1
                         INNER JOIN (
                    SELECT services_group_id, MAX(create_time) AS max_create_time
                    FROM jenkins_task_group
                    WHERE delsign = 0
                    GROUP BY services_group_id
                ) jt2 ON jt1.services_group_id = jt2.services_group_id AND jt1.create_time = jt2.max_create_time
                WHERE jt1.delsign = 0
            ) jtg ON sg.id = jtg.services_group_id
        WHERE
            sg.running_status = 1
          AND sg.delsign = 0
        <if test="projectId != null">
            AND sg.project_id = #{projectId}
        </if>
            ORDER BY
            sg.id;
    </select>
    <select id="selectListServicesJobHistoryGroups"
            resultType="com.mega.platform.cloud.admin.vo.AdminServicesGroupRunningJobRespVO">
        SELECT
        sg.id AS services_group_id,
        sg.name AS services_group_name,
        sg.project_id,
        sg.project_app_id,
        sg.services_env,
        sg.admin_user_id,
        jtg.id AS jenkins_task_group_id,
        jtg.action,
        jtg.is_success,
        jtg.failed_reason,
        jtg.complete_time
        FROM
        services_group sg
        LEFT JOIN (
        SELECT jt1.*
        FROM jenkins_task_group jt1
        INNER JOIN (
        SELECT services_group_id, MAX(create_time) AS max_create_time
        FROM jenkins_task_group
        WHERE delsign = 0
        GROUP BY services_group_id
        ) jt2 ON jt1.services_group_id = jt2.services_group_id AND jt1.create_time = jt2.max_create_time
        WHERE jt1.delsign = 0
        ) jtg ON sg.id = jtg.services_group_id
        WHERE
        sg.running_status = 1
        AND sg.delsign = 0
        AND sg.id IN
        <foreach collection="servicesGroupIds" item="servicesGroupId" open="(" separator="," close=")">
            #{servicesGroupId}
        </foreach>
        ORDER BY
        sg.id;
    </select>

    <!-- updateJenkinsTemplateParams -->
    <insert id="updateJenkinsTemplateParams">
        INSERT INTO jenkins_job_template_param_value
        (jenkins_job_templete_param_id, services_data_id, services_data_type,
        param_value, create_time, update_time, delsign)
        VALUES
        <foreach collection="jenkinsParams" item="param" separator=",">
            (#{param.jenkinsTemplateParamId}, #{servicesGroupId}, 1,
            #{param.paramValue},NOW(), NOW(), 0)
        </foreach>
        ON DUPLICATE KEY UPDATE
        param_value = VALUES(param_value),
        update_time = NOW(),
        delsign = 0
    </insert>
</mapper>