package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "project")
public class Project {
    /**
     * 项目ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 项目名称 例：快鸟
     */
    @Column(name = "name")
    private String name;

    /**
     * swagger文档
     */
    @Column(name = "api_docs")
    private String apiDocs;

    /**
     * 状态 0：不可用 1：正常 2：挂起 3：审核中
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 描述
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}