package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 查询服务最后一次任务请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "查询服务最后一次任务请求参数", description = "查询服务最后一次任务请求参数")
public class AdminServicesLastTaskReqVO {

    @NotNull(message = "服务ID不能为空")
    @ApiModelProperty(value = "服务ID", required = true, example = "25")
    private Long servicesId;
}
