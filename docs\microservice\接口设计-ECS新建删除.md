# 接口设计-ECS新建删除

# =================================================================
# ECS增加删除
# =================================================================

mega-cloud-admin模组的controller目录下已经新建了AdminEcsController.java文件，作为ECS相关接口的统一入口。
新建AdminEcsService.java，作为ECS相关接口的业务处理类。
如果设计到数据库操作，则新建AdminEcsDao.java和对应的AdminEcsDao.xml，作为ECS相关的数据库操作类。

## 涉及的表
* ecs_server
* ecs_server_config
* ecs_server_region
* ecs_server_image
* ecs_services_command
* ecs_server_log



提供如下接口：

## 1 ECS查询 查询所有ECS列表
### 1.1 接口 POST
`/admin/api/{projectId}/microservice/ecs/list`
`/admin/api/system/microservice/ecs/list`
### 1.2 入参 AdminEcsListReqVO 
```java
public class AdminEcsListReqVO {
    private String projectId; // 可能没有
    private String name;      // 名称（模糊查询）
    private String remark;    // 备注（模糊查询）
    private String adminUserId; // 创建的管理员
    private Integer ecsType;    // ECS类型(1 mysql, 2 redis, 3 services)
    private List<String> tagList;        // ECS标签选择(多选)
    private List<String> customTagList;  // ECS自定义标签选择(多选)
    private Integer status;     // 状态
    private String version;    // 版本
}
```

### 1.3 返回 AdminEcsListRespVO

```java
public class AdminEcsListRespVO {

    private List<EcsServerVO> ecsList; // ECS列表

    public static class EcsServerVO {
        private String id;          // ECS ID
        private String name;        // 名称
        private String projectId;   // 项目ID
        private String projectName; // 项目名称
        private Integer ecsType;    // ECS类型(1 mysql, 2 redis, 3 services)
        private String ecsTypeName; // ECS类型名称
        private String configId;    // 配置ID
        private String configName;  // 配置名称
        private String version;     // 版本
        private String ipAddress;   // IP地址
        private Integer port;       // 端口
        private List<String> tagList;       // ECS标签选择(多选)
        private List<String> customTagList; // ECS自定义标签选择(多选)
        private Integer status;     // 状态
        private String statusName;  // 状态名称
        private String adminUserId; // 创建的管理员ID
        private String adminUserName; // 创建的管理员名称
        private String remark;      // 备注
        private Date createTime;    // 创建时间
        private Date updateTime;    // 更新时间

        // 其他ECS指标metric信息，如CPU使用率、内存使用率等，可以根据需要添加字段
    }
}
```
## 2 Ecs配置列表查询

### 2.1 接口
`/admin/api/{projectId}/microservice/ecs/config/list`
`/admin/api/system/microservice/ecs/config/list`

### 2.2 入参 AdminEcsConfigListReqVO

```java
public class AdminEcsConfigListReqVO {
    private String projectId; // 可能没有
    private String name;      // 名称（模糊查询）
    private Integer ecsType;  // ECS类型(1 mysql, 2 redis, 3 services)
}
``` 

### 2.3 返回 AdminEcsConfigListRespVO


```java
public class AdminEcsConfigListRespVO {

    private List<EcsConfigItemVO> configList; // ECS配置列表

    public static class EcsConfigItemVO {
        private Long id;                    // 配置ID
        private String name;                // 名称
        private Long projectId;             // 项目ID
        private Long projectAppId;          // 项目应用ID
        private Long ecsServerRegionId;     // ECS区域ID
        private Long ecsServerImageId;      // 镜像配置ID
        private String securityGroupId;     // 安全组ID
        private String subnetId;            // 子网ID
        private String ipPrefix;            // IP地址前缀
        private String flavorRef;           // 系统规格
        private String diskCategory;        // 磁盘类型
        private String diskPerformanceLevel; // 磁盘级别
        private String creditSpecification; // 突发性能实例
        private String vpcId;               // VPC ID
        private String chargingMode;        // 计费模式
        private String periodType;          // 周期类型
        private String internetChargeType;  // 网络计费类型
        private Integer bandwidth;          // 带宽
        private Long ecsServicesCommandId;  // 执行脚本ID
        private String version;             // 版本
        private String remark;              // 备注
        private Date createTime;            // 创建时间
        private Date updateTime;            // 更新时间
        private Integer delsign;            // 删除标志
    }
}
```



## 2 ECS创建
### 2.1 接口
`/admin/api/{projectId}/microservice/ecs/create`
`/admin/api/system/microservice/ecs/create` 

### 2.3 入参 AdminEcsCreateReqVO
```java
public class AdminEcsCreateReqVO {
    private String projectId;           // 项目ID（优先级: 低, Feign）
    private String name;                // 名称
    private String ecsConfigId;         // ECS配置ID
    private Integer ecsType;            // ECS类型 (1: mysql, 2: redis, 3: services)
    private List<String> tagList;       // ECS标签选择(多选)
    private List<String> customTagList; // ECS自定义标签选择(多选)
    private String remark;              // 备注

    // getter 和 setter 省略
}
```

返回无

## 3 ECS删除(退订)
`/admin/api/{projectId}/microservice/ecs/delete`
`/admin/api/system/microservice/ecs/delete`
### 3.2 入参 AdminEcsDeleteReqVO
```java
public class AdminEcsDeleteReqVO {
    private String ecsServerId;      // ECS ID
    private String projectId;  // 项目ID（优先级: 低, Feign）
    // getter 和 setter 省略
}
```

> **注意**: ECS重启、删除、关闭、升级时需要确保当前ECS上面无微服务运行