package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@ApiModel("ECS操作请求参数")
public class AdminEcsReqVO {

    @ApiModelProperty("ECS服务器ID")
    @NotNull(message = "ECS ID不能为空")
    private Long ecsServerId;

    @ApiModelProperty("项目ID")
    private Long projectId;
}
