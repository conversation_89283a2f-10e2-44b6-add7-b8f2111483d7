package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("ECS镜像列表查询请求")
public class AdminEcsImageListReqVO {

    @ApiModelProperty("项目ID（可能没有）")
    private Long projectId;

    @ApiModelProperty("名称（模糊查询）")
    private String name;

    @ApiModelProperty("备注（模糊查询）")
    private String remark;

    @ApiModelProperty("创建的管理员ID")
    private String adminUserId;

    @ApiModelProperty("ECS类型(1 mysql, 2 redis, 3 services)")
    private Integer ecsType;

    @ApiModelProperty("ECS标签选择(多选，逗号分隔)")
    private String tagList;

    @ApiModelProperty("ECS自定义标签选择(多选，逗号分隔)")
    private String customTagList;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("版本")
    private String version;
}
