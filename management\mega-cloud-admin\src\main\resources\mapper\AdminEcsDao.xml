<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminEcsDao">

    <!-- 通用筛选条件 -->
    <sql id="EcsFilterWhere">
        <where>
            es.delsign = 0
            <if test="req.projectId != null and req.projectId != ''">
                AND es.project_id = #{req.projectId}
            </if>
            <if test="req.name != null and req.name != ''">
                AND es.name LIKE CONCAT('%', #{req.name}, '%')
            </if>
            <if test="req.remark != null and req.remark != ''">
                AND es.remark LIKE CONCAT('%', #{req.remark}, '%')
            </if>
            <if test="req.adminUserId != null and req.adminUserId != ''">
                AND es.admin_user_id = #{req.adminUserId}
            </if>
            <if test="req.ecsType != null">
                AND es.ecs_type = #{req.ecsType}
            </if>
            <if test="req.status != null">
                AND es.status = #{req.status}
            </if>
            <if test="req.version != null and req.version != ''">
                AND esi.version = #{req.version}
            </if>

            <if test="req.ecsServerId != null">
                AND es.id = #{req.ecsServerId}
            </if>
        </where>
    </sql>

    <!-- 根据条件查询ECS列表 -->
    <select id="selectEcsListByCondition" resultType="com.mega.platform.cloud.admin.vo.AdminEcsListRespVO$EcsServerVO">
         SELECT
            es.id,
            es.name,
            es.project_id,
            p.name as project_name,
            es.ecs_type,
            dic.`name` as ecs_type_name,
            es.ecs_server_config_id as config_id,
            esc.name as config_name,
            esi.version,
            es.instance_id,
            es.public_ip,
            es.private_ip,
            a.tag_list as tag_list,
            b.tag_list as custom_tag_list,
            es.status,
            CASE es.status
                WHEN 0 THEN '异常'
                WHEN 1 THEN '正常'
                WHEN 2 THEN '正在创建'
                WHEN 3 THEN '正在初始化'
                WHEN 4 THEN '待关闭'
                WHEN 5 THEN '已退订'
                WHEN 6 THEN '待升级'
                WHEN 7 THEN '升级中'
                ELSE '未知'
            END as status_name,
            es.remark,
            es.create_time,
            esr.name as regionStr,
            esr.zone as regionZone,
            es.update_time
        FROM ecs_server es
        LEFT JOIN project p ON es.project_id = p.id
        LEFT JOIN ecs_server_config esc ON es.ecs_server_config_id = esc.id
        LEFT JOIN ecs_server_image esi ON es.ecs_server_image_id = esi.id
        LEFT JOIN ecs_server_region esr ON esc.ecs_server_region_id = esr.id
        LEFT JOIN dic ON dic.dic_cate_id = 2013 AND dic.`value` = es.ecs_type
        LEFT JOIN (
            SELECT esrr.ecs_server_id, GROUP_CONCAT(DISTINCT dic.id) as tag_list FROM ecs_server_tag_relation AS esrr
            INNER JOIN dic ON esrr.ecs_tag_id = dic.id AND dic.dic_cate_id = 2011
        ) as a ON a.ecs_server_id = es.id
        LEFT JOIN (
            SELECT esrr.ecs_server_id, GROUP_CONCAT(DISTINCT dic.id) as tag_list FROM ecs_server_tag_relation AS esrr
            INNER JOIN dic ON esrr.ecs_tag_id = dic.id AND dic.dic_cate_id = 2012
        ) as b ON b.ecs_server_id = es.id
        <include refid="EcsFilterWhere"/>
        GROUP BY es.id
        ORDER BY es.create_time DESC
    </select>

    <!-- 检查ECS上是否有运行中的微服务 -->
    <select id="countRunningServicesByEcsId" resultType="int">
        SELECT COUNT(*)
        FROM services s
        WHERE s.ecs_server_id = #{ecsServerId}
          AND s.status = 1
          AND s.running_status = 1
          AND s.delsign = 0
    </select>

        <!-- 阿里云ECS实例查询的公共字段 -->
        <sql id="AliEcsServerColumns">
            SELECT
                a.id,
                a.`name`,
                a.ecs_server_config_id AS ecsConfigId,
                a.instance_id AS instanceId,
                esr.`name` AS regionStr,
                esr.zone   AS regionZone,
                esr.id AS ecsServerRegionId,
                a.`status`,
                a.public_ip  AS publicIp,
                a.private_ip AS privateIp,
                esi.ref AS imageRef
            FROM
                ecs_server AS a
            LEFT JOIN project p ON a.project_id = p.id
            LEFT JOIN ecs_server_config esc ON a.ecs_server_config_id = esc.id
            LEFT JOIN ecs_server_region esr ON esc.ecs_server_region_id = esr.id
            LEFT JOIN ecs_server_image esi ON a.ecs_server_image_id = esi.id
            WHERE
                a.delsign = 0
        </sql>

        <!-- 获取阿里云ECS实例列表 -->
        <select id="getAliActiveEcsServer" resultType="com.mega.platform.cloud.admin.dto.AdminAliEcsInstanceDTO">
            <include refid="AliEcsServerColumns"/>
            AND a.status IN (1, 2, 7)
        </select>

        <!-- 根据ECS实例状态获取ECS实例列表 -->
        <select id="getAliEcsServerByStatus" resultType="com.mega.platform.cloud.admin.dto.AdminAliEcsInstanceDTO">
            <include refid="AliEcsServerColumns"/>
            AND a.status = #{status}
        </select>

        <!-- 根据ID获取ECS实例 -->
        <select id="getAliEcsServerById" resultType="com.mega.platform.cloud.admin.dto.AdminAliEcsInstanceDTO">
            <include refid="AliEcsServerColumns"/>
            AND a.id = #{ecsServerId}
        </select>


    <!-- 根据ID更新ECS状态 -->
    <update id="updateEcsStatusById">
        UPDATE ecs_server 
        SET status = #{status}, update_time = NOW()
        WHERE id = #{ecsServerId} AND delsign = 0
    </update>


    <!-- 根据ID更新ECS状态、IP、计费类型 -->
    <update id="updateEcsStatusIp">
        UPDATE ecs_server
        SET status = #{status},
            public_ip = #{publicIp},
            private_ip = #{privateIp},
            charging_mode = #{instanceChargeType},
            update_time = NOW()
        WHERE id = #{ecsServerId} AND delsign = 0
    </update>

    <!-- insertEcsTags --> 

    <insert id="insertEcsTags">
        INSERT INTO ecs_server_tag_relation (ecs_server_id, ecs_tag_id, create_time, update_time)
        VALUES
        <foreach collection="tagIdList" item="tagId" separator=",">
            (#{ecsServerId}, #{tagId}, NOW(), NOW())
        </foreach>
    </insert>   

    <!-- updateEcsChargeType --> 
    <update id="updateEcsChargeType">
        UPDATE ecs_server
        SET charging_mode = #{chargeType},  update_time = NOW(), period = #{period}
        WHERE id = #{ecsServerId} AND delsign = 0
    </update>
</mapper>