package com.mega.platform.cloud.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Collections;

/**
 * @Description:
 * @Author: sys
 * @Date: 2025/7/26 13:34
 * @Version: 1.0
 */
@Profile({"dev", "test", "prod"})
@EnableOpenApi
@Configuration
public class DocketConfig {

    private final ApiDocsProperties apiDocsProperties;

    public DocketConfig(ApiDocsProperties apiDocsProperties) {
        this.apiDocsProperties = apiDocsProperties;
    }

    @Bean
    public Docket boxApi() {

        return new Docket(DocumentationType.OAS_30)
                .useDefaultResponseMessages(false)
                .apiInfo(new ApiInfoBuilder()
                        .title(apiDocsProperties.getTitle())
                        .version(apiDocsProperties.getVersion())
                        .description(apiDocsProperties.getDescription())
                        .contact(new Contact("Mega Platform", "", ""))
                        .build())
                .select()
                .apis(RequestHandlerSelectors.basePackage(apiDocsProperties.getApisPackage()))
                .paths(PathSelectors.any())
                .build()
                .securitySchemes(Collections.singletonList(bearerToken()))
                .securityContexts(Collections.singletonList(securityContext()));
    }

    private HttpAuthenticationScheme bearerToken() {
        return HttpAuthenticationScheme.JWT_BEARER_BUILDER
                .name("Authorization")
                .build();
    }

    private SecurityContext securityContext() {
        return SecurityContext.builder()
                .securityReferences(Collections.singletonList(
                        SecurityReference.builder()
                                .reference("Authorization")
                                .scopes(new AuthorizationScope[0])
                                .build()))
                .build();
    }
}
