package com.mega.platform.cloud.admin.service;

import com.aliyun.sdk.service.ecs20140526.models.DescribeInstancesResponseBody;
import com.google.gson.Gson;
import com.mega.platform.cloud.admin.dao.AdminEcsDao;
import com.mega.platform.cloud.admin.dto.AdminAliEcsInstanceDTO;
import com.mega.platform.cloud.admin.util.AdminAliEcsSdkTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AdminAliEcsCheckService {
    private  AdminAliEcsSdkTool adminAliEcsSdkTool;
    @Value("${ali.ecs.access-key-id}")
    private String accessKeyId;

    @Value("${ali.ecs.access-key-secret}")
    private String accessKeySecret;
    private final AdminEcsDao adminAliEcsDao;


    public AdminAliEcsCheckService(AdminEcsDao adminAliEcsDao) {
        this.adminAliEcsDao = adminAliEcsDao;

    }

      @PostConstruct
    public void init() {
        this.adminAliEcsSdkTool = new AdminAliEcsSdkTool(accessKeyId, accessKeySecret);
    }


    /***************************************************************** 创建ecs instance *****************************************************************/
    /***
     * 检测阿里云ecs实例创建状态
     */
    public void checkEcsCreateStatus(List<AdminAliEcsInstanceDTO> ecsList, String regionStr) {
        Map<String, AdminAliEcsInstanceDTO> ecsServerIdMap = ecsList.stream()
                .collect(Collectors.toMap(
                        AdminAliEcsInstanceDTO::getInstanceId,
                        Function.identity()
                ));
        List<String> instanceIdList = new ArrayList<>(ecsServerIdMap.keySet());
        String instanceIds = new Gson().toJson(instanceIdList);

        List<DescribeInstancesResponseBody.Instance> instances = adminAliEcsSdkTool.fetchDescribeInstance(instanceIds, regionStr);
        if (instances == null || instances.isEmpty()) {
            // 实例不存在了，更新为已退订
            ecsServerIdMap.values().forEach(ecsInstanceDTO -> {
                log.error("--------阿里云 实例不存在了，更新为已退订! instanceIds: {}-----------", instanceIds);
                adminAliEcsDao.updateEcsStatusById(ecsInstanceDTO.getId(), 5);
            });
            return;
        }


        for (DescribeInstancesResponseBody.Instance instance : instances) {
            AdminAliEcsInstanceDTO ecsInstanceDTO = ecsServerIdMap.get(instance.getInstanceId());
            if (ecsInstanceDTO == null) {
                log.error("-----------阿里云 instanceId与返回instanceId不一致, instanceId: {}-----------", instance.getInstanceId());
                continue;
            }
            log.info("--------阿里云 instanceId: {}, 状态: {}-----------", instance.getInstanceId(), instance.getStatus());
            Integer status = 2;
            // Pending：创建中。
            if (instance.getStatus().equals("Pending")) {
                continue;
            }
            // 升级时停止，触发启动
            if (instance.getStatus().equals("Stopped") && ecsInstanceDTO.getStatus() == 7) {
//                this.startAfterSwitchImage(ecsInstanceDTO);
                continue;
            }

            // Running：运行中。
            if (instance.getStatus().equals("Running")) {
                status = 3; //0.异常 1.正常 2.正在创建 3.正在初始化 4.待关闭 5.已退
                String publicIp = getPublicIp(instance, ecsInstanceDTO);
                // 更新innerIp
                String privateIp = instance.getVpcAttributes().getPrivateIpAddress().getIpAddress() != null && !instance.getVpcAttributes().getPrivateIpAddress().getIpAddress().isEmpty() ?
                        instance.getVpcAttributes().getPrivateIpAddress().getIpAddress().get(0) : null;
                // 镜像版本
//                String imageRef = instance.getImageId();
//                Float imageVersion = adminServerDao.getImageVersionByImageRef(imageRef);
                Float imageVersion = null;
                String instanceChargeType = instance.getInstanceChargeType();
                try {
                    adminAliEcsDao.updateEcsStatusIp(ecsInstanceDTO.getId(), status, publicIp, privateIp, instanceChargeType);
                } catch (Exception e) {
                    log.error("--------阿里云 instanceId: {}, 更新状态error: {} -----------", instance.getInstanceId(), e.getMessage());
                }
                continue;
            }
        }
    }
    /***
     * 获取公网ip
     */
    private String getPublicIp(DescribeInstancesResponseBody.Instance instance, AdminAliEcsInstanceDTO ecsInstanceDTO){
        //status = 1; //0.异常 1.正常 2.正在创建 3.正在初始化 4.待关闭 5.已退
        String publicIp = ecsInstanceDTO.getPublicIp(); // old publicIp
        // 存在静态公网 更新publicIp
        if(instance.getPublicIpAddress().getIpAddress() != null && !instance.getPublicIpAddress().getIpAddress().isEmpty()){
            publicIp = instance.getPublicIpAddress().getIpAddress().get(0);
        }
        // 存在弹性公网 更新publicIp
        else if(instance.getEipAddress().getIpAddress() != null && !instance.getEipAddress().getIpAddress().isEmpty()){
            log.info("--------阿里云 instanceId: {}, 存在弹性公网, publicIp: {}-----------",  instance.getInstanceId(), instance.getEipAddress().getIpAddress());
            publicIp = instance.getEipAddress().getIpAddress();
        }else{
            log.error("--------阿里云 instanceId: {}, 不存在公网ip, resp: {} -----------",  instance.getInstanceId(), new Gson().toJson(instance));
        }
        return publicIp;
    }


}
