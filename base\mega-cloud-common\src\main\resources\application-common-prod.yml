logging:
  file:
    path: /opt/log/${spring.cloud.client.hostname}/
  logback:
    rollingpolicy:
      max-file-size: 128MB
spring:
  data:
    mongodb:
      uri: mongodb://werewolf-cloud:Mangosteen0!@*************:27017,*************:27018/werewolf?maxPoolSize=500&minPoolSize=10
  cloud:
    consul:
      enabled: true
      host: ***********
      port: 8500
      discovery:
        # 动态传入 ip-address
        ip-address:
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${spring.cloud.consul.discovery.ip-address}-${server.port}
        service-name: ${spring.application.name}
        health-check-critical-timeout: 1m
  datasource:
    master:
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 16
    slave:
      enabled: true
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 8
  redis:
    db0:
      sentinel:
        master: mymaster
        nodes:
          - ***********:25537
          - ***********:25537
          - ***********:25537
      database: 0
      password: LA1954b!
    db3:
      sentinel:
        master: mymaster
        nodes:
          - ***********:25537
          - ***********:25537
          - ***********:25537
      database: 3
      password: LA1954b!
    db8:
      sentinel:
        master: mymaster
        nodes:
          - ***********:25537
          - ***********:25537
          - ***********:25537
      database: 8
      password: LA1954b!
    db14:
      sentinel:
        master: mymaster
        nodes:
          - ***********:25537
          - ***********:25537
          - ***********:25537
      database: 14
      password: LA1954b!
  kafka:
    bootstrap-servers:
      - **************:9092
      - **************:9092
      - **************:9092
    producer:
      acks: 0
      retries: 0
      properties:
        linger.ms: 1000
  mail:
    host: smtp.exmail.qq.com
    port: 465
    username: <EMAIL>
    password: NLAP8pDWhR9Bw5WB
    protocol: smtp
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
          socket-factory:
            class: javax.net.ssl.SSLSocketFactory
            port: 465

feishu:
  url:
    common-url: https://open.feishu.cn/open-apis/bot/v2/hook/b8b79630-c88b-467a-83ae-a43d6e79d130
    monitor-alarm-url: https://open.feishu.cn/open-apis/bot/v2/hook/b8b79630-c88b-467a-83ae-a43d6e79d130
    slow-sql-url: https://open.feishu.cn/open-apis/bot/v2/hook/b8b79630-c88b-467a-83ae-a43d6e79d130
    service-running-status-url: https://open.feishu.cn/open-apis/bot/v2/hook/b8b79630-c88b-467a-83ae-a43d6e79d130
mega:
  platform:
    services-id: -1