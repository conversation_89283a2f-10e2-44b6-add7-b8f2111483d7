package com.mega.platform.cloud.data.dto.jenkins;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CleanBuildHistoryDTO extends JenkinsGroovyDTO{
    private final static String groovyScriptFormat = "def jobName = \"%s\"\n" +
            "def job = Jenkins.instance.getItemByFullName(jobName)\n" +
            "if (job != null) {\n" +
            "    job.builds.each { build ->\n" +
            "        println \"正在删除构建 #${build.number}\"\n" +
            "        build.delete()    // 删除该构建\n" + "    }\n" +
            "    println \"全部构建删除完成\"\n" +
            "} else {\n" +
            "    println \"Job 不存在: ${jobName}\"\n" +
            "}";

    private String jobName;

    public CleanBuildHistoryDTO(String jobName) {
        this.jobName = jobName;
    }

    @Override
    public String getGroovyScriptStr() {
        return String.format(groovyScriptFormat, jobName);
    }
}
