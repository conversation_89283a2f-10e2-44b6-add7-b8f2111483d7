package com.mega.platform.cloud.auth.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.auth.cache.AuthAppConfigCache;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.data.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@RequiredArgsConstructor
@Slf4j
public class WeChatVerifierClient {
    private final AuthAppConfigCache configCache;

    // 缓存 access_token
    private final Map<Long, WeChatTokenCache> tokenCacheMap = new ConcurrentHashMap<>();

    private static final String ACCESS_TOKEN_URL =
            "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code";

    private static final String MINI_CODE_TO_SESSION_URL =
            "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";

    private static final String USER_INFO_URL =
            "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s";

    private static final String GENERAL_ACCESS_TOKEN_URL =
            "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";

    private static final String MINI_CHECK_SESSION_URL = "https://api.weixin.qq.com/wxa/checksession?access_token=%s&signature=%s&openid=%s&sig_method=%s";

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取全局 access_token（多 appId 支持，带缓存）
     */
    public String getGeneralAccessToken(Long appId) {
        WeChatTokenCache cache = tokenCacheMap.get(appId);
        long now = System.currentTimeMillis();
        if (cache != null && now < cache.getExpireTime()) {
            return cache.getAccessToken();
        }
        return refreshAccessToken(appId);
    }

    /**
     * 强制刷新某个 appId 的 access_token
     */
    public synchronized String refreshAccessToken(Long appId) {
        AuthWeChatConfig config = configCache.getTypedConfig(
                appId,
                ThirdPlatformEnum.WECHAT.getCode(),
                AuthWeChatConfig.class
        );

        String url = String.format(GENERAL_ACCESS_TOKEN_URL, config.getAppId(), config.getAppSecret());
        String response = new RestTemplate().getForObject(url, String.class);
        log.info("refresh access_token appId={}, resp={}", appId, response);

        try {
            AuthWeChatGeneralAccessTokenResp tokenResp = objectMapper.readValue(response, AuthWeChatGeneralAccessTokenResp.class);
            if (tokenResp.getAccessToken() == null) {
                throw new RuntimeException("获取全局access_token失败: " + response);
            }

            long expireAt = System.currentTimeMillis() + (tokenResp.getExpiresIn() - 200) * 1000L;
            tokenCacheMap.put(appId, new WeChatTokenCache(tokenResp.getAccessToken(), expireAt));

            return tokenResp.getAccessToken();
        } catch (Exception e) {
            throw new RuntimeException("解析全局access_token响应失败：" + response, e);
        }
    }

    /**
     * 获取微信 access_token
     */
    public AuthWeChatAccessTokenResp getAccessToken(Long appId, String code) {
        AuthWeChatConfig config = configCache.getTypedConfig(appId, ThirdPlatformEnum.WECHAT.getCode(), AuthWeChatConfig.class);

        String url = String.format(ACCESS_TOKEN_URL, config.getAppId(), config.getAppSecret(), code);

        String response = new RestTemplate().getForObject(url, String.class);
        log.info("response:{}", response);
        try {
            return objectMapper.readValue(response, AuthWeChatAccessTokenResp.class);
        } catch (Exception e) {
            throw new RuntimeException("解析微信 access_token 响应失败：" + response, e);
        }
    }

    /**
     * 获取微信用户信息
     */
    public AuthWeChatUserInfoResp getUserInfo(String accessToken, String openid) {
        String url = String.format(USER_INFO_URL, accessToken, openid);

        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getMessageConverters().stream()
                .filter(converter -> converter instanceof StringHttpMessageConverter)
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));

        String response = restTemplate.getForObject(url, String.class);
        try {
            return objectMapper.readValue(response, AuthWeChatUserInfoResp.class);
        } catch (Exception e) {
            throw new RuntimeException("解析微信用户信息响应失败：" + response, e);
        }
    }

    /**
     * 获取微信小程序用户信息
     */
    public AuthWeChatMiniProgramSessionResp getMiniProgramSession(Long appId, String code) {
        AuthWeChatConfig config = configCache.getTypedConfig(appId, ThirdPlatformEnum.WECHAT.getCode(), AuthWeChatConfig.class);
        String url = String.format(MINI_CODE_TO_SESSION_URL, config.getMiniAppId(), config.getMiniAppSecret(), code);
        log.info("jscode2session url = {}", url);
        String response = new RestTemplate().getForObject(url, String.class);
        log.info("mini session response:{}", response);
        try {
            return objectMapper.readValue(response, AuthWeChatMiniProgramSessionResp.class);
        } catch (Exception e) {
            throw new RuntimeException("解析微信小程序获取session响应失败：" + response, e);
        }
    }

    /**
     * 校验小程序用户登录态
     * https://api.weixin.qq.com/wxa/checksession
     */
    public Boolean checkSession(Long appId, String openid, String sessionKey) {
        String accessToken = getGeneralAccessToken(appId);

        try {
            String signature = hmacSha256(sessionKey, "");
            String url = String.format(MINI_CHECK_SESSION_URL, accessToken, signature, openid, "hmac_sha256");
            String response = new RestTemplate().getForObject(url, String.class);
            log.info("checkSession resp:{}", response);

            JsonNode node = objectMapper.readTree(response);
            int errCode = node.path("errcode").asInt(-1);
            return errCode == 0;
        } catch (Exception e) {
            throw new RuntimeException("解析微信 checkSession 响应失败：", e);
        }
    }


    /**
     * HMAC-SHA256 签名计算
     */
    private String hmacSha256(String key, String data) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Hex.encodeHexString(hash);
    }

}
