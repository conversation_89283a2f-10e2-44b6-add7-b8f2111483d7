package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.admin.dto.AdminAliEcsInstanceDTO;
import com.mega.platform.cloud.admin.vo.AdminEcsListReqVO;
import com.mega.platform.cloud.admin.vo.AdminEcsListRespVO;
import com.mega.platform.cloud.data.entity.EcsServer;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AdminEcsDao {
    
    /**
     * 根据条件查询ECS列表（支持复杂查询）
     * @param reqVO 查询条件
     * @return ECS列表
     */
    List<AdminEcsListRespVO.EcsServerVO> selectEcsListByCondition(@Param("req") AdminEcsListReqVO reqVO);
    

    /**
     * 检查ECS上是否有运行中的微服务
     * @param ecsServerId ECS服务器ID
     * @return 运行中的微服务数量
     */
    int countRunningServicesByEcsId(@Param("ecsServerId") Long ecsServerId);
        
    /**
     * 根据ID更新ECS状态
     * @param ecsServerId ECS服务器ID
     * @param status 状态
     * @return 影响行数
     */
    int updateEcsStatusById(@Param("ecsServerId") Long ecsServerId, @Param("status") Integer status);

    /**
     * 获取所有阿里云ECS实例
     * @return 阿里云ECS实例列表
     */
    List<AdminAliEcsInstanceDTO> getAliActiveEcsServer();

    /**
     * 根据ECS实例状态获取ECS实例列表
     * @param status ECS实例状态
     * @return ECS实例列表
     */
    List<AdminAliEcsInstanceDTO> getAliEcsServerByStatus(@Param("status") Integer status);

    /**
     * 根据ECS实例ID获取ECS实例详情
     * @param ecsServerId ECS实例ID
     * @return ECS实例详情
     */
    AdminAliEcsInstanceDTO getAliEcsServerById(@Param("ecsServerId") Long ecsServerId);

    /**
     * 更新ECS实例状态、IP、CPU信息
     * @param ecsServerId ECS实例ID
     * @param status 状态
     * @param publicIp 公网IP
     * @param privateIp 内网IP
     * @param instanceChargeType 实例计费类型
     */
    void updateEcsStatusIp(@Param("ecsServerId") Long ecsServerId,
                              @Param("status") Integer status,
                              @Param("publicIp") String publicIp,
                              @Param("privateIp") String privateIp,
                              @Param("instanceChargeType") String instanceChargeType);

    /**
     * 插入ECS标签关系
     * @param ecsServerId ECS ID
     * @param tagIdList 标签ID列表
     */
    void insertEcsTags(@Param("ecsServerId") Long ecsServerId, @Param("tagIdList") List<String> tagIdList);


    /**
     * 更新ECS实例付费类型
     * @param ecsServerId
     * @param chargeType
     */
    void updateEcsChargeType(@Param("ecsServerId") Long ecsServerId, @Param("chargeType") String chargeType, @Param("period") Integer period);
}