package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("订阅记录响应")
public class AdminPaymentSubscriptionRespVO {
    @ApiModelProperty("订阅记录ID")
    private Long id;

    @ApiModelProperty("主订单号")
    private Long parentOrderNo;

    @ApiModelProperty("三方平台")
    private String platformCode;

    @ApiModelProperty("订阅状态 订阅状态：1=ACTIVE(活跃), 2=INACTIVE(不活跃), 3=EXPIRED(已过期), 4=CANCELLED(已取消)")
    private Integer status;

    @ApiModelProperty("下次扣费时间")
    private LocalDateTime nextChargeTime;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
