<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminServiceQueryDao">

    <!-- 查询全部services列表 -->
    <select id="selectServicesList" resultType="com.mega.platform.cloud.admin.dto.AdminServicesDetailDTO">
        SELECT
            es.public_ip as publicIp,
			es.private_ip as innerIp,

            s.id as servicesId,
            s.name as servicesName,
            s.services_group_id as servicesGroupId,
            s.ecs_server_id as servicesEcsServerId,
            s.jenkins_job_id as servicesJenkinsJobId,
            s.path as path,
            s.log_path as logPath,
            s.version as version,
            s.description ,
            s.log_timeout_second as logTimeoutSecond,
            s.status as servicesStatus,
            s.log_status as logStatus,
            s.running_status as servicesRunningStatus,
            s.real_running_status as servicesRealRunningStatus,
            s.sort as sort,
            s.remark as servicesRemark,

            ltg.id as lastTaskGroupId,
            ltg.action as lastTaskGroupAction,
            ltg.is_success as lastTaskGroupIsSuccess,
            ltg.complete_time as lastTaskGroupCompleteTime,

            lt.id as lastTaskId,
            lt.action as lastTaskAction,
            lt.is_success as lastTaskIsSuccess,
            lt.complete_time as lastTaskCompleteTime,
            lt.jenkins_job_url as lastTaskJenkinsJobUrl,

            ar.`name` as adminUserName,
			p.`name` as projectName,
			pa.`name` as projectAppName,
            sg.sort as servicesGroupSort,
            sg.delsign as servicesGroupDelsign,
            dic.id as servicesEnvId,
            sg.*
        FROM services s
        LEFT JOIN services_group sg ON s.services_group_id = sg.id AND sg.delsign = 0
        LEFT JOIN (
            SELECT services_group_id, MAX(id) as max_id
            FROM jenkins_task_group 
            WHERE delsign = 0
            GROUP BY services_group_id
        ) ltg_max ON sg.id = ltg_max.services_group_id
        LEFT JOIN jenkins_task_group ltg ON ltg_max.max_id = ltg.id
        LEFT JOIN (
            SELECT jenkins_job_id, MAX(id) as max_id
            FROM jenkins_task 
            WHERE delsign = 0
            GROUP BY jenkins_job_id
        ) lt_max ON s.jenkins_job_id = lt_max.jenkins_job_id
        LEFT JOIN jenkins_task lt ON lt_max.max_id = lt.id
        LEFT JOIN admin_role AS ar ON ar.id = sg.admin_user_id
        LEFT JOIN project AS p ON p.id = sg.project_id
		LEFT JOIN project_app AS pa ON pa.id = sg.project_app_id
        LEFT JOIN ecs_server AS es ON es.id = s.ecs_server_id
        LEFT JOIN dic ON dic.dic_cate_id = 2005 AND dic.value = sg.services_env
        WHERE  s.delsign = 0
        <if test="projectId != null">
            AND sg.project_id = #{projectId} 
        </if>
        <if test="reqVO.servicesGroupId != null">
            AND s.services_group_id = #{reqVO.servicesGroupId}
        </if>
        <if test="reqVO.keyword != null">
           AND (s.name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR s.remark LIKE CONCAT('%', #{reqVO.keyword}, '%'))
        </if>
        <if test="reqVO.adminUserId != null">
            AND sg.admin_user_id = #{reqVO.adminUserId}
        </if>
        <if test="reqVO.isSelf != null">
            AND sg.is_self = #{reqVO.isSelf}
        </if>
        <if test="reqVO.status != null">
            AND s.status = #{reqVO.status}
        </if>
        <if test="reqVO.runningStatus != null">
            AND s.running_status = #{reqVO.runningStatus}
        </if>
        <if test="reqVO.realRunningStatus != null">
            AND s.real_running_status = #{reqVO.realRunningStatus}
        </if>
        <if test="tags != null and !tags.isEmpty()">
            AND sg.id IN (
                SELECT DISTINCT sgtr.service_group_id 
                FROM services_group_tag_relation sgtr 
                WHERE sgtr.delsign = 0 
                AND sgtr.service_tag_id IN
                <foreach collection="tags" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            )
        </if>
        <if test="reqVO.projectName != null">
            AND p.name LIKE CONCAT('%', #{reqVO.projectName}, '%')
        </if>
        <if test="reqVO.projectAppName != null">
            AND pa.name LIKE CONCAT('%', #{reqVO.projectAppName}, '%')
        </if>

        <if test="reqVO.projectId != null">
            AND sg.project_id = #{reqVO.projectId}
        </if>
        <if test="reqVO.projectAppId != null">
            AND sg.project_app_id = #{reqVO.projectAppId}
        </if>
        ORDER BY sg.project_id ASC, sg.sort ASC, s.sort ASC
    </select>

    <!-- 基于组查询services列表 -->
    <select id="selectServicesListByGroup" resultType="com.mega.platform.cloud.admin.vo.AdminServicesListByGroupRespVO">
        SELECT 
            s.id as servicesId,
            s.name as servicesName,
            s.remark as servicesRemark,
            s.status as servicesStatus,
            s.running_status as servicesRunningStatus,
            s.real_running_status as servicesRealRunningStatus,
            s.create_time as servicesCreateTime,

            es.public_ip as publicIp,
			es.private_ip as innerIp,
            
            lt.id as lastTaskId,
            lt.action as lastTaskAction,
            lt.is_success as lastTaskIsSuccess,
            lt.complete_time as lastTaskCompleteTime,
            lt.jenkins_job_url as lastTaskJenkinsJobUrl,
            lt.git_commit as lastTaskGitCommit
        FROM services s
        LEFT JOIN services_group as sg ON sg.id = s.services_group_id
        LEFT JOIN (
            SELECT jenkins_job_id, MAX(id) as max_id
            FROM jenkins_task 
            WHERE delsign = 0
            GROUP BY jenkins_job_id
        ) lt_max ON s.jenkins_job_id = lt_max.jenkins_job_id
        LEFT JOIN jenkins_task lt ON lt_max.max_id = lt.id
        LEFT JOIN ecs_server AS es ON es.id = s.ecs_server_id
        WHERE sg.project_id = #{projectId} 
        AND s.services_group_id = #{servicesGroupId}
        AND s.delsign = 0
        ORDER BY s.create_time DESC
    </select>

    <!-- selectServicesTags --> 

    <select id="selectServicesTags" resultType="com.mega.platform.cloud.admin.dto.AdminServicesTagDTO">
    SELECT 
        sgt.service_group_id as serviceGroupId,
        sgt.service_tag_id as tagId
        FROM services_group_tag_relation AS sgt
        WHERE sgt.service_group_id in 
        <foreach collection="servicesGroupIds" item="servicesGroupId" open="(" separator="," close=")">
            #{servicesGroupId}
        </foreach>
        AND sgt.delsign = 0;
    </select>

    <!-- selectServicesParams --> 

    <select id="selectServicesParams" resultType="com.mega.platform.cloud.admin.dto.AdminServicesJenkinsParamDTO">
    SELECT 
        s.id,
        s.`name`,
        jp.param_key as paramKey,
        jp.param_name as paramName,
        jpv.param_value as paramValue
        FROM jenkins_job_template_param_value as jpv
        LEFT JOIN jenkins_job_template_param as jp ON jpv.jenkins_job_templete_param_id = jp.id
        LEFT JOIN services as s ON jpv.services_data_id = s.id
        WHERE
        jpv.services_data_type = 2
        AND s.id in 
        <foreach collection="servicesIds" item="servicesId" open="(" separator="," close=")">
            #{servicesId}
        </foreach>
        AND s.delsign = 0
        AND jpv.delsign = 0;
    </select>

    <!-- selectServicesGroupParams --> 

    <select id="selectServicesGroupParams" resultType="com.mega.platform.cloud.admin.dto.AdminServicesJenkinsParamDTO">
    SELECT 
        sg.id,
        sg.`name`,
        jp.param_key as paramKey,
        jp.param_name as paramName,
        jpv.param_value as paramValue
        FROM jenkins_job_template_param_value as jpv
        LEFT JOIN jenkins_job_template_param as jp ON jpv.jenkins_job_templete_param_id = jp.id
        LEFT JOIN services_group as sg ON jpv.services_data_id = sg.id
        WHERE
        jpv.services_data_type = 1
        AND sg.id in 
        <foreach collection="servicesGroupIds" item="servicesGroupId" open="(" separator="," close=")">
            #{servicesGroupId}
        </foreach>
        AND sg.delsign = 0
        AND jpv.delsign = 0;
    </select>

    <!-- 查询服务最后一次任务 -->
    <select id="selectServicesLastTask" resultType="com.mega.platform.cloud.admin.vo.AdminServicesLastTaskRespVO">
        SELECT
            s.id                  AS servicesId,
            s.name                AS servicesName,
            s.remark              AS servicesRemark,
            s.status              AS servicesStatus,
            s.running_status      AS servicesRunningStatus,
            s.real_running_status AS servicesRealRunningStatus,
            s.create_time         AS serviceCreateTime,
            lt.id                 AS lastTaskId,
            lt.action             AS lastTaskAction,
            lt.is_success         AS lastTaskIsSuccess,
            lt.failed_reason      AS lastFailedReason,
            lt.git_commit         AS lastGitCommit,
            lt.jenkins_job_id     AS lastJenkinsJobId,
            lt.jenkins_job_url    AS lastJenkinsJobUrl,
            lt.jenkins_task_group_id AS lastJenkinsTaskGroupId,
            lt.request_data       AS lastTaskRequestData,
            lt.remark             AS lastTaskRemark,
            lt.complete_time      AS lastTaskCompleteTime
        FROM services AS s
        LEFT JOIN services_group AS sg ON sg.id = s.services_group_id
        LEFT JOIN (
            SELECT jenkins_job_id, MAX(id) AS max_id
            FROM jenkins_task
            WHERE delsign = 0
            GROUP BY jenkins_job_id
        ) lt_max ON s.jenkins_job_id = lt_max.jenkins_job_id
        LEFT JOIN jenkins_task lt ON lt_max.max_id = lt.id
        WHERE
        s.delsign = 0

        <if test="projectId != null and projectId != 0">
            AND sg.project_id = #{projectId}
        </if>
        
        AND sg.delsign = 0
        AND s.id = #{servicesId}
        AND (lt.delsign = 0 OR lt.delsign IS NULL)
    </select>

    <!-- 查询Jenkins任务组列表 -->
    <select id="selectJenkinsTaskGroupList" resultType="com.mega.platform.cloud.admin.vo.AdminJenkinsTaskGroupListRespVO">
        SELECT
        p.name AS projectName,
	    au.username as adminUserName,
        jtg.*
        FROM jenkins_task_group AS jtg
        LEFT JOIN services_group AS sg ON jtg.services_group_id = sg.id
        LEFT JOIN project AS p ON p.id = sg.project_id
        LEFT JOIN admin_user AS au ON au.id = sg.admin_user_id
        WHERE sg.project_id = #{projectId}
        AND jtg.services_group_id = #{servicesGroupId}
        AND jtg.delsign = 0
        AND sg.delsign = 0
        ORDER BY jtg.id DESC
    </select>

    <!-- 查询Jenkins任务组日志 -->
    <select id="selectJenkinsTaskGroupLog" resultType="com.mega.platform.cloud.admin.dto.AdminJenkinsTaskLogDTO">
        SELECT
        jtl.jenkins_task_group_id AS jenkinsTaskGroupId,
        jtl.jenkins_task_id AS jenkinsTaskId,
        jtl.log_time AS logTime,
        jtl.log_content AS logContent
        FROM jenkins_task_log AS jtl
        LEFT JOIN jenkins_task_group AS jtg ON jtg.id = jtl.jenkins_task_group_id
        LEFT JOIN services_group AS sg ON jtg.services_group_id = sg.id
        WHERE
        jtl.jenkins_task_group_id = #{jenkinsTaskGroupId}
        AND sg.project_id = #{projectId}
        ORDER BY jtl.jenkins_task_id, jtl.log_time
    </select>
    <select id="selectServiceGroupJenkinsValue"
            resultType="com.mega.platform.cloud.admin.vo.AdminJenkinsTemplateParamRespVO">
        SELECT
            p.id                   AS paramId,
            p.param_key            AS paramKey,
            p.param_name           AS paramName,
            p.services_data_type   AS servicesDataType,
            p.required             AS required,
            p.default_value        AS defaultValue,
            COALESCE(v.param_value, p.default_value) AS finalValue,  -- 优先取实际值，没有则取默认值
            v.remark               AS valueRemark
        FROM services_group g
                 JOIN jenkins_job_template t
                      ON g.jenkins_template_id = t.id
                 JOIN jenkins_job_template_param p
                      ON t.id = p.jenkins_template_id
                 LEFT JOIN jenkins_job_template_param_value v
                           ON v.jenkins_job_templete_param_id = p.id
                               AND v.services_data_type = 1
                               AND v.services_data_id = g.id
        WHERE g.id = #{servicesGroupId}
          AND v.services_data_type = 1
          AND p.delsign = 0
          AND t.delsign = 0;
    </select>
    <select id="selectServiceJenkinsValue"
            resultType="com.mega.platform.cloud.admin.vo.AdminJenkinsTemplateParamRespVO">
        SELECT
            p.id                   AS paramId,
            p.param_key            AS paramKey,
            p.param_name           AS paramName,
            p.services_data_type   AS servicesDataType,
            p.required             AS required,
            p.default_value        AS defaultValue,
            COALESCE(v.param_value, p.default_value) AS finalValue,
            v.remark               AS valueRemark
        FROM services s
                 JOIN services_group g
                      ON s.services_group_id = g.id
                 JOIN jenkins_job_template t
                      ON g.jenkins_template_id = t.id
                 JOIN jenkins_job_template_param p
                      ON t.id = p.jenkins_template_id
                 LEFT JOIN jenkins_job_template_param_value v
                           ON v.jenkins_job_templete_param_id = p.id
                               AND v.services_data_type = 2
                               AND v.services_data_id = s.id
        WHERE s.id = #{serviceId}
          AND v.services_data_type = 2
          AND p.delsign = 0
          AND t.delsign = 0;
    </select>

    <!-- 查询services组的service数量 -->
    <select id="selectServicesGroupServiceCount" resultType="map">
        SELECT services_group_id, COUNT(*)as total
        FROM services
        WHERE services_group_id
        GROUP BY services_group_id;
    </select>
</mapper>