package com.mega.platform.cloud.admin.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 服务组状态编辑请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "服务组状态编辑请求参数", description = "服务组状态编辑请求参数")
public class AdminServicesGroupStatusEditReqVO {

    @ApiModelProperty(value = "projectId", example = "1")
    private Long projectId;
    /**
     * 服务组ID
     */
    @NotNull(message = "服务组ID不能为空")
    @ApiModelProperty(value = "服务组ID", required = true, example = "1")
    private Long servicesGroupId;
    
    /**
     * 状态 0：下线 1：上线
     */
    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "状态", required = true, example = "1", notes = "0：下线 1：上线")
    private Integer status;
}
