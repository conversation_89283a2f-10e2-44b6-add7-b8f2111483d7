package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.service.AdminBaseService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.entity.ProjectApp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/api")
@Api(tags = "基础接口")
@Slf4j
@RequiredArgsConstructor
public class AdminBaseController {
    private final AdminBaseService adminBaseService;

    @ApiOperation("基础接口 字典列表查询")
    @PostMapping("/public/dic/list")
    public Result<AdminBaseDicListRespVO> dicList(@RequestBody AdminBaseDicListReqVO reqVO) {
        return Results.success(adminBaseService.getDicList(reqVO));
    }

    @ApiOperation("基础接口 日志格式列表查询")
    @PostMapping("/public/log/format/list")
    public Result<AdminBaseLogFormatListRespVO> logFormatList() {
        return Results.success(adminBaseService.getLogFormatList());
    }

    @ApiOperation("基础接口 jenkins实例列表查询")
    @PostMapping("/public/microservice/jenkins/instances/list")
    public Result<AdminBaseJenkinsInstancesListRespVO> jenkinsInstancesList() {
        return Results.success(adminBaseService.getJenkinsInstancesList());
    }

    @ApiOperation("基础接口 jenkins模板列表查询")
    @PostMapping("/public/microservice/jenkins/template/list")
    public Result<AdminBaseJenkinsTemplateListRespVO> jenkinsTemplateList() {
        return Results.success(adminBaseService.getJenkinsTemplateList());
    }

    @ApiOperation("基础接口 jenkins模板参数列表查询")
    @PostMapping("/public/microservice/jenkins/param/list")
    public Result<AdminBaseJenkinsTemplateParamListRespVO> jenkinsTemplateListParam(@RequestBody AdminBaseJenkinsTemplateParamListReqVO reqVO) {
        return Results.success(adminBaseService.getJenkinsTemplateListParam(reqVO));
    }

    @ApiOperation("基础接口 ecs_server列表查询")
    @PostMapping({"/{projectId}/microservice/ecs/server/list", "/system/microservice/ecs/server/list"})
    public Result<AdminBaseEcsServerListRespVO> ecsServerList(@PathVariable(value = "projectId", required = false) Long projectId, @RequestBody AdminBaseEcsServerListReqVO reqVO) {
        projectId = checkProjectId(projectId, reqVO.getProjectId());
        if (projectId == null) {
            return Results.error(0, "project不能为空", null);
        }

        return Results.success(adminBaseService.getEcsServerList(projectId));
    }

    @ApiOperation("基础接口 管理员列表")
    @PostMapping( "/public/microservice/admin/user/list")
    public Result<List<AdminBaseUserListRespVO>> adminUserList() {
        return Results.success(adminBaseService.adminUserList());
    }

    @ApiOperation("基础接口 app列表查询")
    @PostMapping({"/{projectId}/microservice/app/list", "/system/microservice/app/list"})
    public Result<List<AdminBaseAppListRespVO>> appList(@PathVariable(value = "projectId", required = false) Long projectId) {
        return Results.success(adminBaseService.getAppList(projectId));
    }

    // 基础接口 建立自定义标签
    @ApiOperation("基础接口 建立自定义标签")
    @PostMapping("/public/tag/create")
    public Result<AdminBaseTagCreateRespVO> createTag(@RequestBody AdminBaseTagCreateReqVO reqVO) {
        return Results.success(adminBaseService.createTag(reqVO));
    }

    private Long checkProjectId(Long pathProjectId, Long bodyProjectId) {
        if (pathProjectId != null && pathProjectId > 0) {
            return pathProjectId;
        }

        if (bodyProjectId != null && bodyProjectId > 0) {
            return bodyProjectId;
        }
        return null;
    }
    /**
     * 检查端口占用
     */
    @PostMapping("/public/microservice/check/port")
    @ApiOperation(value = "检查端口占用情况", notes = "检查端口占用情况")
    public Result<?> checkPortInUse(@RequestBody @Validated AdminBaseCheckPortReqVO reqVO) {
        adminBaseService.checkPortInUse(reqVO);
        return Results.success();
    }
}
