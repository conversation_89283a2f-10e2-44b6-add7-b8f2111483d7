package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.service.AdminBaseService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.entity.ProjectApp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/api")
@Api(tags = "基础接口")
@Slf4j
@RequiredArgsConstructor
public class AdminBaseController {
    private final AdminBaseService adminBaseService;

    @ApiOperation("基础接口 字典列表查询")
    @PostMapping("/public/dic/list")
    public Result<AdminBaseDicListRespVO> dicList(@RequestBody AdminBaseDicListReqVO reqVO) {
        return Results.success(adminBaseService.getDicList(reqVO));
    }

    @ApiOperation("基础接口 日志格式列表查询")
    @PostMapping("/public/log/format/list")
    public Result<AdminBaseLogFormatListRespVO> logFormatList() {
        return Results.success(adminBaseService.getLogFormatList());
    }

    @ApiOperation("基础接口 jenkins实例列表查询")
    @PostMapping("/public/microservice/jenkins/instances/list")
    public Result<AdminBaseJenkinsInstancesListRespVO> jenkinsInstancesList() {
        return Results.success(adminBaseService.getJenkinsInstancesList());
    }

    @ApiOperation("基础接口 jenkins模板列表查询")
    @PostMapping("/public/microservice/jenkins/template/list")
    public Result<AdminBaseJenkinsTemplateListRespVO> jenkinsTemplateList() {
        return Results.success(adminBaseService.getJenkinsTemplateList());
    }

    @ApiOperation("基础接口 jenkins模板参数列表查询")
    @PostMapping("/public/microservice/jenkins/param/list")
    public Result<AdminBaseJenkinsTemplateParamListRespVO> jenkinsTemplateListParam(@RequestBody AdminBaseJenkinsTemplateParamListReqVO reqVO) {
        return Results.success(adminBaseService.getJenkinsTemplateListParam(reqVO));
    }

    @ApiOperation("基础接口 ecs_server列表查询 projectId=-1全部查询")
    @PostMapping("/{projectId}/microservice/ecs/server/list")
    public Result<AdminBaseEcsServerListRespVO> ecsServerList(@PathVariable Long projectId) {
        return Results.success(adminBaseService.getEcsServerList(projectId));
    }

    @ApiOperation("基础接口 管理员列表")
    @PostMapping( "/public/microservice/admin/user/list")
    public Result<List<AdminBaseUserListRespVO>> adminUserList() {
        return Results.success(adminBaseService.adminUserList());
    }

    @ApiOperation("基础接口 app列表查询")
    @PostMapping({"/{projectId}/system/app/list", "/system/app/list"})
    public Result<List<AdminBaseAppListRespVO>> appList(@PathVariable(value = "projectId", required = false) Long projectId) {
        return Results.success(adminBaseService.getAppList(projectId));
    }

    // 基础接口 建立自定义标签
    @ApiOperation("基础接口 建立自定义标签")
    @PostMapping("/public/tag/create")
    public Result<AdminBaseTagCreateRespVO> createTag(@RequestBody AdminBaseTagCreateReqVO reqVO) {
        return Results.success(adminBaseService.createTag(reqVO));
    }

}
