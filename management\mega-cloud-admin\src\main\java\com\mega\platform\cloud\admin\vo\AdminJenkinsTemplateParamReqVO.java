package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "查询Jenkins模版参数请求", description = "根据服务组ID或服务ID查询Jenkins模版参数请求参数")
public class AdminJenkinsTemplateParamReqVO {
    @ApiModelProperty(value = "服务组ID", example = "1001")
    private Long servicesGroupId;

    @ApiModelProperty(value = "服务ID", example = "2001")
    private Long serviceId;
}
