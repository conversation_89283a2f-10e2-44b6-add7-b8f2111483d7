## ECS配置
### ECS配置查询
#### 接口
`/admin/api/{projectId}/microservice/ecs/config/list`
`/admin/api/system/microservice/ecs/config/list`
#### 条件
- ProjectID(可能没有)
- 名称、备注 (模糊查询)
- 管理员(创建的管理员)
- ECS类型(mysql,redis,services)
- ECS标签选择(多选)
- ECS自定义标签选择(多选)
- 状态
- 版本
#### 返回字段
- 所有配置信息
- 脚本内容

### ECS配置创建(优先级: 低)
#### 接口
`/admin/api/{projectId}/microservice/ecs/config/create`
`/admin/api/system/microservice/ecs/config/create`
#### 字段
- ProjectID(可能没有)
- 名称、备注 (模糊查询)
- 镜像ID
- Aliyun相关参数
- 管理员(创建的管理员)
- ECS类型(mysql,redis,services)
- ECS标签选择(多选)
- ECS自定义标签选择(多选)
- 版本

### ECS配置编辑(优先级: 低)
#### 接口
`/admin/api/{projectId}/microservice/ecs/config/edit`
`/admin/api/system/microservice/ecs/config/edit`
#### 字段
- 同create

### ECS配置删除(优先级: 低)
#### 接口
`/admin/api/{projectId}/microservice/ecs/config/delete`
`/admin/api/system/microservice/ecs/config/delete`

## ECS配置Command
### ECS配置Command列表(优先级: 低)
#### 接口
`/admin/api/{projectId}/microservice/ecs/command/config/list`
`/admin/api/system/microservice/ecs/command/config/list`
#### 字段
- 名称
- 描述
- 负责人(最后一次更改人)
- 创建时间
- 更改时间
- 脚本内容

### ECS配置Command创建(优先级: 低)
#### 接口
`/admin/api/{projectId}/microservice/ecs/command/config/create`
`/admin/api/system/microservice/ecs/command/config/create`
#### 字段
- 名称
- 脚本内容
- 描述

### ECS配置Command编辑(优先级: 低)
#### 接口
`/admin/api/{projectId}/microservice/ecs/command/config/edit`
`/admin/api/system/microservice/ecs/command/config/edit`
#### 字段
- 名称
- 脚本内容
- 描述

## ECS
### ECS查询
#### 接口
`/admin/api/{projectId}/microservice/ecs/list`
`/admin/api/system/microservice/ecs/list`
#### 条件
- ProjectID(可能没有)
- 名称、备注 （模糊查询）
- 管理员(创建的管理员)
- ECS类型(mysql,redis,services)
- ECS标签选择(多选)
- ECS自定义标签选择(多选)
- 状态
- 版本

#### 返回字段
- 项目信息(id,名称)
- ECS的基本信息
- ECS指标metric信息
- ECS标签选择(多选)
- ECS自定义标签选择(多选)
- **创建时的配置信息(可拆成单独接口)**

### ECS创建
#### 接口
`/admin/api/{projectId}/microservice/ecs/create`
`/admin/api/system/microservice/ecs/create`
#### 调用方法
`microservice模块创建ecs的方法`
#### 创建字段
- ProjectID(优先级: 低,Feign)
- 名称
- ecsConfigID
- ECS类型(mysql,redis,services)
- ECS标签选择(多选)
- ECS自定义标签选择(多选)
- 备注

### ECS升级
#### 接口
`/admin/api/{projectId}/microservice/ecs/upgrade`
`/admin/api/system/microservice/ecs/upgrade`
#### 调用方法
`microservice模块升级ecs的方法`,基于镜像版本升级

### ECS重启 
`/admin/api/{projectId}/microservice/ecs/restart`
`/admin/api/system/microservice/ecs/restart`
#### 调用方法
`microservice模块重启ecs的方法`

### ECS关闭
`/admin/api/{projectId}/microservice/ecs/close`
`/admin/api/system/microservice/ecs/close`
#### 调用方法
`microservice模块关闭ecs的方法`

### ECS删除(退订)
`/admin/api/{projectId}/microservice/ecs/close`
`/admin/api/system/microservice/ecs/close`
#### 调用方法
`microservice模块退订ecs的方法`

> **注意**: ECS重启、删除、关闭、升级时需要确保当前ECS上面无微服务运行

## 镜像
### 镜像查询
#### 接口
`/admin/api/{projectId}/microservice/ecs/image/list`
`/admin/api/system/microservice/ecs/image/list`
#### 条件
- ProjectID(可能没有)
- 名称、备注 （模糊查询）
- 管理员(创建的管理员)
- 镜像类型(mysql,redis,services)
- 镜像标签选择(多选)
- 状态
- 版本

#### 返回字段
- 项目信息(id,名称)
- image的基本信息
- image标签


### 创建镜像
#### 接口
`/admin/api/{projectId}/microservice/ecs/image/create`
`/admin/api/system/microservice/ecs/image/create`
#### 调用方法
`microservice模块创建ecs镜像的方法`
#### 创建字段
- ProjectID(优先级: 低,Feign)
- 名称
- ECSID(基于哪个ECS)
- 管理员(优先级: 低,默认创建者)
- 镜像类型
- 镜像标签
- 备注
- 版本号(可以算法自动增加)
- 排序

### 删除镜像
#### 调用方法
`microservice模块删除ecs镜像的方法`
#### 接口
`/admin/api/{projectId}/microservice/ecs/image/delete`
`/admin/api/system/microservice/ecs/image/delete`
