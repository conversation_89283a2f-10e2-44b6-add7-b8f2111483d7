package com.mega.platform.cloud.admin.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mega.platform.cloud.admin.service.AdminAppAuthService;
import com.mega.platform.cloud.admin.service.AdminAppService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.PageResult;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.entity.AuthSmsTemplateConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/api/{projectId}/third")
@Api(tags = "第三方配置基础接口")
@Slf4j
@RequiredArgsConstructor
public class AdminThirdController {
    private final AdminAppService adminAppService;

    /**
     * 查询应用列表
     */
    @PostMapping({"/app/list"})
    public Result<List<AdminAppRespVO>> list(@PathVariable Long projectId) {
        log.info("查询应用列表，项目ID: {}", projectId);
        List<AdminAppRespVO> result = adminAppService.findAppList(projectId);
        return Results.success(result);
    }
}
