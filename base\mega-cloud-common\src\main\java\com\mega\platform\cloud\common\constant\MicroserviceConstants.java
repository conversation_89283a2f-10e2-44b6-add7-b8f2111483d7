package com.mega.platform.cloud.common.constant;

public class MicroserviceConstants {
    // 服务器用户名密码
    public static final String SERVER_USERNAME = "root";
    public static final String SERVER_PASSWORD = "Mangosteen0!";

    // jenkins生成资源前缀
    public static final String JENKINS_PREFIX_MEGA_PLAT = "mega-platform";

    // services_data_type
    public static final Integer SERVICES_DATA_TYPE_SERVICES_GROUP = 1;
    public static final Integer SERVICES_DATA_TYPE_SERVICES = 2;

    // jenkins模板通用特殊key
    public static final String JENKINS_TEMPLATE_KEY_SSH_SERVER_NAME = "sshServerName";
    public static final String JENKINS_TEMPLATE_KEY_ECS_SERVER_IP = "ecsServerIp";
    public static final String JENKINS_TEMPLATE_KEY_ACTIVE_PROFILE = "activeProfile";
    public static final String JENKINS_TEMPLATE_KEY_PORT = "port";
    public static final String JENKINS_TEMPLATE_KEY_RESTART_TYPE = "restartType";
    public static final String JENKINS_TEMPLATE_KEY_SERVICES_ID = "servicesId";
    public static final String JENKINS_TEMPLATE_KEY_CUSTOM_SCRIPT = "customScript";
    public static final String JENKINS_TEMPLATE_KEY_APP_TOKEN_KEY = "appTokenKey";
    public static final String JENKINS_TEMPLATE_KEY_APP_TOKEN_SECRET = "appTokenSecret";

    // microserviceapp
    public static final String MICROSERVICE_APP = "microserviceapp";

    // 服务重启类型
    public static final Integer SERVICES_GROUP_UPDATE_TYPE_NORMAL = 1; // 普通重启
    public static final Integer SERVICES_GROUP_UPDATE_TYPE_ROLLING = 2; // 滚服重启
    public static final Integer SERVICES_GROUP_UPDATE_TYPE_ROLLING_WAIT = 3; // 导流重启
    public static final Integer SERVICES_GROUP_UPDATE_TYPE_SCRIPT = 4; // 脚本运行

    // services/servicesGroup上下线状态
    public static final Integer SERVICES_DATA_STATUS_ONLINE = 1;
    public static final Integer SERVICES_DATA_STATUS_OFFLINE = 0;

    // services运行状态
    public static final Integer SERVICES_RUNNING_STATUS_NOT_RUNNING = 0;
    public static final Integer SERVICES_RUNNING_STATUS_RUNNING = 1;
    public static final Integer SERVICES_RUNNING_STATUS_BUILDING = 2;
    public static final Integer SERVICES_RUNNING_STATUS_IN_QUEUE = 3;
    public static final Integer SERVICES_RUNNING_STATUS_WAITING_STOP = 4;
    public static final Integer SERVICES_RUNNING_STATUS_BUILD_ERROR = -1;

    // servicesGroup运行状态
    public static final Integer SERVICES_GROUP_RUNNING_STATUS_NOT_RUNNING = 0;
    public static final Integer SERVICES_GROUP_RUNNING_STATUS_RUNNING = 1;
    public static final Integer SERVICES_GROUP_RUNNING_STATUS_BUILDING = 2;
    public static final Integer SERVICES_GROUP_RUNNING_STATUS_BUILD_ERROR = -1;
    public static final Integer SERVICES_GROUP_RUNNING_STATUS_PARTIALLY_RUNNING = 3;

}
