# 后台管理-开服任务调度ECS接口设计

## 1. 概述

### 1.1 设计目标
本文档详细描述游戏开服任务调度系统的接口设计，包括子任务接口规范和REST API接口定义。

### 1.2 接口分类
- **子任务接口**：统一的子任务执行接口规范
- **REST API接口**：面向前端和外部系统的HTTP接口
- **内部服务接口**：系统内部组件间的调用接口

## 2. 子任务接口设计

### 2.1 子任务接口定义

根据系统架构需要，定义统一的子任务接口规范，所有子任务（MySQL部署、Redis部署、微服务部署）都需要实现此接口：

```java
/**
 * 游戏开服子任务接口
 * 所有子任务实现类需要实现此接口
 */
public interface GameLaunchSubTask {
    
    /**
     * 启动任务
     * @param subTask 子任务实体
     * @return 任务启动结果
     */
    SubTaskResult start(GameLaunchSubTask subTask);
    
    /**
     * 检测任务结果
     * @param subTask 子任务实体
     * @return 任务检测结果
     */
    SubTaskResult checkResult(GameLaunchSubTask subTask);
    
    /**
     * 获取任务类型
     * @return 子任务类型
     */
    SubTaskTypeEnum getTaskType();
    
    /**
     * 获取任务名称
     * @return 任务名称
     */
    String getTaskName();
}
```

### 2.2 子任务实现类

#### 2.2.1 MySQL部署任务实现

```java
@Component
@Slf4j
public class MysqlDeploySubTask implements GameLaunchSubTask {
    
    @Override
    public SubTaskResult start(GameLaunchSubTask subTask) {
        try {
            log.info("开始执行MySQL部署任务，子任务ID：{}", subTask.getId());
            
            // 1. 检查是否需要新建MySQL
            if (needCreateNew(subTask)) {
                // 2. 获取MySQL ECS配置
                EcsServerConfig config = getEcsConfig(subTask);
                
                // 3. 调用阿里云ECS API创建实例
                String instanceId = ecsService.createInstance(config);
                
                // 4. 更新子任务状态
                updateSubTaskStatus(subTask.getId(), TaskStatus.IN_PROGRESS, instanceId);
                
                return SubTaskResult.builder()
                    .success(true)
                    .message("MySQL ECS实例创建成功")
                    .data(Map.of("instanceId", instanceId))
                    .build();
            } else {
                // 复用现有MySQL实例
                EcsServer existingServer = findReusableMySQL(subTask);
                return SubTaskResult.builder()
                    .success(true)
                    .message("复用现有MySQL实例")
                    .data(Map.of("serverId", existingServer.getId()))
                    .build();
            }
        } catch (Exception e) {
            log.error("MySQL部署任务启动失败", e);
            return SubTaskResult.builder()
                .success(false)
                .message("MySQL部署任务启动失败：" + e.getMessage())
                .errorCode("MYSQL_START_FAILED")
                .build();
        }
    }
    
    @Override
    public SubTaskResult checkResult(GameLaunchSubTask subTask) {
        try {
            // 1. 轮询ECS创建状态
            EcsInstanceStatus status = ecsService.getInstanceStatus(subTask.getInstanceId());
            
            if (status == EcsInstanceStatus.RUNNING) {
                // 2. 检查MySQL服务启动状态
                if (checkMySQLService(subTask)) {
                    // 3. 执行数据库初始化
                    initializeDatabase(subTask);
                    
                    // 4. 更新任务状态为成功
                    updateSubTaskStatus(subTask.getId(), TaskStatus.SUCCESS, null);
                    
                    return SubTaskResult.builder()
                        .success(true)
                        .message("MySQL部署完成")
                        .build();
                }
            }
            
            // 检查是否超时
            if (isTimeout(subTask)) {
                updateSubTaskStatus(subTask.getId(), TaskStatus.FAILED, "部署超时");
                return SubTaskResult.builder()
                    .success(false)
                    .message("MySQL部署超时")
                    .errorCode("MYSQL_DEPLOY_TIMEOUT")
                    .build();
            }
            
            // 仍在处理中
            return SubTaskResult.builder()
                .success(true)
                .message("MySQL部署进行中")
                .data(Map.of("status", "IN_PROGRESS"))
                .build();
                
        } catch (Exception e) {
            log.error("MySQL部署任务检查失败", e);
            updateSubTaskStatus(subTask.getId(), TaskStatus.FAILED, e.getMessage());
            return SubTaskResult.builder()
                .success(false)
                .message("MySQL部署检查失败：" + e.getMessage())
                .errorCode("MYSQL_CHECK_FAILED")
                .build();
        }
    }
    
    @Override
    public SubTaskTypeEnum getTaskType() {
        return SubTaskTypeEnum.MYSQL;
    }
    
    @Override
    public String getTaskName() {
        return "MySQL数据库部署任务";
    }
    
    // 私有方法实现具体逻辑
    private boolean needCreateNew(GameLaunchSubTask subTask) {
        // 检查是否需要新建MySQL实例的逻辑
    }
    
    private EcsServerConfig getEcsConfig(GameLaunchSubTask subTask) {
        // 获取ECS配置的逻辑
    }
    
    private void initializeDatabase(GameLaunchSubTask subTask) {
        // 数据库初始化逻辑
    }
}
```

#### 2.2.2 Redis部署任务实现

```java
@Component
@Slf4j
public class RedisDeploySubTask implements GameLaunchSubTask {
    
    @Override
    public SubTaskResult start(GameLaunchSubTask subTask) {
        try {
            log.info("开始执行Redis部署任务，子任务ID：{}", subTask.getId());
            
            if (needCreateNew(subTask)) {
                // 创建3台Redis ECS实例（1主2从）
                List<String> instanceIds = createRedisCluster(subTask);
                
                updateSubTaskStatus(subTask.getId(), TaskStatus.IN_PROGRESS, 
                    String.join(",", instanceIds));
                
                return SubTaskResult.builder()
                    .success(true)
                    .message("Redis集群ECS实例创建成功")
                    .data(Map.of("instanceIds", instanceIds))
                    .build();
            } else {
                // 复用现有Redis集群
                return reuseExistingRedis(subTask);
            }
        } catch (Exception e) {
            log.error("Redis部署任务启动失败", e);
            return SubTaskResult.builder()
                .success(false)
                .message("Redis部署任务启动失败：" + e.getMessage())
                .errorCode("REDIS_START_FAILED")
                .build();
        }
    }
    
    @Override
    public SubTaskResult checkResult(GameLaunchSubTask subTask) {
        try {
            // 1. 检查所有ECS实例状态
            List<String> instanceIds = getInstanceIds(subTask);
            if (!allInstancesRunning(instanceIds)) {
                return checkInProgress(subTask);
            }
            
            // 2. 配置Redis主从关系
            if (!isRedisConfigured(subTask)) {
                configureRedisCluster(subTask);
                return SubTaskResult.builder()
                    .success(true)
                    .message("Redis集群配置中")
                    .data(Map.of("status", "CONFIGURING"))
                    .build();
            }
            
            // 3. 部署Sentinel哨兵节点
            if (!isSentinelDeployed(subTask)) {
                deploySentinel(subTask);
                return SubTaskResult.builder()
                    .success(true)
                    .message("Sentinel部署中")
                    .data(Map.of("status", "DEPLOYING_SENTINEL"))
                    .build();
            }
            
            // 4. 验证Redis集群状态
            if (validateRedisCluster(subTask)) {
                updateSubTaskStatus(subTask.getId(), TaskStatus.SUCCESS, null);
                return SubTaskResult.builder()
                    .success(true)
                    .message("Redis集群部署完成")
                    .build();
            }
            
            return SubTaskResult.builder()
                .success(true)
                .message("Redis集群验证中")
                .data(Map.of("status", "VALIDATING"))
                .build();
                
        } catch (Exception e) {
            log.error("Redis部署任务检查失败", e);
            updateSubTaskStatus(subTask.getId(), TaskStatus.FAILED, e.getMessage());
            return SubTaskResult.builder()
                .success(false)
                .message("Redis部署检查失败：" + e.getMessage())
                .errorCode("REDIS_CHECK_FAILED")
                .build();
        }
    }
    
    @Override
    public SubTaskTypeEnum getTaskType() {
        return SubTaskTypeEnum.REDIS;
    }
    
    @Override
    public String getTaskName() {
        return "Redis缓存部署任务";
    }
}
```

#### 2.2.3 微服务部署任务实现

```java
@Component
@Slf4j
public class MicroserviceDeploySubTask implements GameLaunchSubTask {
    
    @Override
    public SubTaskResult start(GameLaunchSubTask subTask) {
        try {
            log.info("开始执行微服务部署任务，子任务ID：{}", subTask.getId());
            
            // 1. 获取微服务ECS配置
            EcsServerConfig config = getMicroserviceConfig(subTask);
            
            // 2. 调用阿里云ECS API创建实例
            String instanceId = ecsService.createInstance(config);
            
            updateSubTaskStatus(subTask.getId(), TaskStatus.IN_PROGRESS, instanceId);
            
            return SubTaskResult.builder()
                .success(true)
                .message("微服务ECS实例创建成功")
                .data(Map.of("instanceId", instanceId))
                .build();
                
        } catch (Exception e) {
            log.error("微服务部署任务启动失败", e);
            return SubTaskResult.builder()
                .success(false)
                .message("微服务部署任务启动失败：" + e.getMessage())
                .errorCode("MICROSERVICE_START_FAILED")
                .build();
        }
    }
    
    @Override
    public SubTaskResult checkResult(GameLaunchSubTask subTask) {
        try {
            // 1. 检查ECS实例状态
            if (!isInstanceRunning(subTask.getInstanceId())) {
                return checkInProgress(subTask);
            }
            
            // 2. 微服务环境初始化
            if (!isEnvironmentReady(subTask)) {
                initMicroserviceEnvironment(subTask);
                return progressResult("环境初始化中");
            }
            
            // 3. 生成微服务配置
            if (!isConfigGenerated(subTask)) {
                generateMicroserviceConfig(subTask);
                return progressResult("配置生成中");
            }
            
            // 4. 部署微服务应用
            if (!isMicroserviceDeployed(subTask)) {
                deployMicroservices(subTask);
                return progressResult("微服务部署中");
            }
            
            // 5. 健康检查
            if (performHealthCheck(subTask)) {
                // 6. 插入区服信息
                insertGameRegionInfo(subTask);
                
                updateSubTaskStatus(subTask.getId(), TaskStatus.SUCCESS, null);
                return SubTaskResult.builder()
                    .success(true)
                    .message("微服务部署完成")
                    .build();
            }
            
            return progressResult("健康检查中");
            
        } catch (Exception e) {
            log.error("微服务部署任务检查失败", e);
            updateSubTaskStatus(subTask.getId(), TaskStatus.FAILED, e.getMessage());
            return SubTaskResult.builder()
                .success(false)
                .message("微服务部署检查失败：" + e.getMessage())
                .errorCode("MICROSERVICE_CHECK_FAILED")
                .build();
        }
    }
    
    @Override
    public SubTaskTypeEnum getTaskType() {
        return SubTaskTypeEnum.MICROSERVICE;
    }
    
    @Override
    public String getTaskName() {
        return "微服务部署任务";
    }
}
```

### 2.3 枚举和结果类定义

#### 2.3.1 子任务类型枚举

```java
public enum SubTaskTypeEnum {
    MYSQL(1, "MySQL数据库"),
    REDIS(2, "Redis缓存"),
    MICROSERVICE(3, "微服务");
    
    private final int code;
    private final String desc;
    
    SubTaskTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static SubTaskTypeEnum fromCode(int code) {
        for (SubTaskTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown sub task type code: " + code);
    }
}
```

#### 2.3.2 任务结果类

```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubTaskResult {
    private boolean success;           // 是否成功
    private String message;            // 结果消息
    private Object data;               // 结果数据
    private String errorCode;          // 错误码
    private Map<String, Object> extra; // 扩展信息
    
    // 静态工厂方法
    public static SubTaskResult success(String message) {
        return SubTaskResult.builder()
            .success(true)
            .message(message)
            .build();
    }
    
    public static SubTaskResult success(String message, Object data) {
        return SubTaskResult.builder()
            .success(true)
            .message(message)
            .data(data)
            .build();
    }
    
    public static SubTaskResult failure(String message, String errorCode) {
        return SubTaskResult.builder()
            .success(false)
            .message(message)
            .errorCode(errorCode)
            .build();
    }
}
```

## 3. REST API接口设计

### 3.1 接口列表概览

| 接口名称 | 请求方法 | 接口路径 | 功能描述 | 权限级别 |
|---------|---------|----------|----------|----------|
| 创建开服任务 | POST | /system/game/launch/create | 创建游戏开服任务 | 系统管理员 |
| 查询任务详情 | POST | /system/game/launch/detail | 查询任务执行详情 | 系统管理员 |
| 任务列表查询 | POST | /system/game/launch/list | 分页查询任务列表 | 系统管理员 |
| 取消任务 | POST | /system/game/launch/cancel | 取消执行中的任务 | 系统管理员 |
| 重试任务 | POST | /system/game/launch/retry | 重试失败的任务 | 系统管理员 |
| 任务日志查询 | POST | /system/game/launch/logs | 查询任务执行日志 | 系统管理员 |
| 子任务详情查询 | POST | /system/game/launch/subtask/detail | 查询子任务详情 | 系统管理员 |
| 手动执行子任务 | POST | /system/game/launch/subtask/execute | 手动执行指定子任务 | 系统管理员 |

### 3.2 详细接口定义

#### 3.2.1 创建开服任务

**接口信息**：
- **URL**：`POST /system/game/launch/create`
- **权限**：系统管理员
- **功能**：创建新的游戏开服任务

**请求参数**：
```json
{
  "gameName": "测试游戏",
  "regionName": "S001区",
  "regionCode": "S001",
  "mysqlConfig": {
    "useExisting": false,
    "configId": 1,
    "reuseThreshold": 70
  },
  "redisConfig": {
    "useExisting": false,
    "configId": 2,
    "clusterMode": true
  },
  "microserviceConfig": {
    "configId": 3,
    "replicas": 2,
    "resources": {
      "cpu": "2",
      "memory": "4Gi"
    }
  }
}
```

**响应示例**：
```json
{
  "code": 1,
  "message": "任务创建成功",
  "data": {
    "taskId": 123456,
    "status": 0,
    "subTasks": [
      {
        "subTaskId": 789,
        "type": 1,
        "typeName": "MySQL数据库"
      },
      {
        "subTaskId": 790,
        "type": 2,
        "typeName": "Redis缓存"
      },
      {
        "subTaskId": 791,
        "type": 3,
        "typeName": "微服务"
      }
    ]
  }
}
```

#### 3.2.2 查询任务详情

**接口信息**：
- **URL**：`POST /system/game/launch/detail`
- **权限**：系统管理员
- **功能**：查询指定任务的详细执行信息

**请求参数**：
{
  "taskId": 1
}

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "taskId": 123456,
    "gameName": "测试游戏",
    "regionName": "S001区",
    "status": 1,
    "statusName": "进行中",
    "progress": {
      "total": 3,
      "completed": 1,
      "percentage": 33
    },
    "createTime": "2024-01-01 10:00:00",
    "startTime": "2024-01-01 10:01:00",
    "estimatedCompleteTime": "2024-01-01 10:30:00",
    "subTasks": [
      {
        "subTaskId": 789,
        "type": 1,
        "typeName": "MySQL数据库",
        "status": 2,
        "statusName": "成功",
        "startTime": "2024-01-01 10:01:00",
        "completeTime": "2024-01-01 10:05:00",
        "result": {
          "serverId": 1001,
          "connectionInfo": {
            "host": "*************",
            "port": 3306,
            "database": "game_db_s001"
          }
        }
      },
      {
        "subTaskId": 790,
        "type": 2,
        "typeName": "Redis缓存",
        "status": 1,
        "statusName": "进行中",
        "startTime": "2024-01-01 10:05:00",
        "progress": "配置主从关系中"
      },
      {
        "subTaskId": 791,
        "type": 3,
        "typeName": "微服务",
        "status": 0,
        "statusName": "待处理"
      }
    ]
  }
}
```

#### 3.2.3 任务列表查询

**接口信息**：
- **URL**：`POST /system/game/launch/list`
- **权限**：系统管理员
- **功能**：分页查询任务列表，支持条件过滤

**请求参数**：
```json
{
  "pageNum": 1,
  "pageSize": 20,
  "filters": {
    "status": [1, 2],
    "gameName": "测试",
    "createTimeStart": "2024-01-01",
    "createTimeEnd": "2024-01-31",
    "adminUserId": 100
  },
  "sortBy": "create_time",
  "sortOrder": "desc"
}
```

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "total": 50,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 3,
    "list": [
      {
        "taskId": 123456,
        "gameName": "测试游戏",
        "regionName": "S001区",
        "status": 2,
        "statusName": "成功",
        "progress": 100,
        "createUser": "张三",
        "createTime": "2024-01-01 10:00:00",
        "completeTime": "2024-01-01 10:25:00",
        "duration": "25分钟"
      }
    ]
  }
}
```

#### 3.2.4 取消任务

**接口信息**：
- **URL**：`POST /system/game/launch/cancel`
- **权限**：系统管理员
- **功能**：取消执行中的任务

**请求参数**：
```json
{
  "reason": "用户手动取消",
  "forceCancel": false,
  "taskId": 1
}
```

**响应示例**：
```json
{
  "code": 1,
  "message": "任务取消成功",
  "data": {
    "taskId": 123456,
    "status": 4,
    "cancelTime": "2024-01-01 10:15:00"
  }
}
```

#### 3.2.5 重试任务

**接口信息**：
- **URL**：`POST /system/game/launch/retry`
- **权限**：系统管理员
- **功能**：重试失败的任务

**请求参数**：
```json
{
  "retryScope": "failed_only", // "all" | "failed_only" | "specific"
  "subTaskIds": [790, 791], // retryScope为"specific"时指定
  "resetConfig": false // 是否重置配置
}
```

#### 3.2.6 任务日志查询

**接口信息**：
- **URL**：`GET /system/game/launch/logs`
- **权限**：系统管理员
- **功能**：查询任务执行日志

**查询参数**：
- `subTaskId`：子任务ID（可选）
- `pageNum`：页码
- `pageSize`：页大小

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "total": 100,
    "logs": [
      {
        "logId": 1001,
        "taskId": 123456,
        "subTaskId": 789,
        "logLevel": "INFO",
        "logTime": "2024-01-01 10:01:00",
        "logContent": "开始执行MySQL部署任务"
      },
      {
        "logId": 1002,
        "taskId": 123456,
        "subTaskId": 789,
        "logLevel": "INFO",
        "logTime": "2024-01-01 10:02:00",
        "logContent": "ECS实例创建成功，实例ID：i-bp1234567890"
      }
    ]
  }
}
```

#### 3.2.7 子任务详情查询

**接口信息**：
- **URL**：`GET /system/game/launch/subtask/detail`
- **权限**：系统管理员
- **功能**：查询子任务详情

**查询参数**：
- `taskId`：主任务ID（必填）
- `subTaskId`：子任务ID（必填）

**响应示例**：
```json
{
  "code": 1,
  "message": "查询成功",
  "data": {
    "subTaskId": 789,
    "taskId": 123456,
    "type": 1,
    "typeName": "MySQL数据库",
    "status": 2,
    "statusName": "成功",
    "config": {
      "useExisting": false,
      "configId": 1,
      "instanceType": "ecs.g6.large"
    },
    "result": {
      "instanceId": "i-bp1234567890",
      "connectionInfo": {
        "host": "*************",
        "port": 3306,
        "database": "game_db_s001"
      }
    },
    "startTime": "2024-01-01 10:01:00",
    "completeTime": "2024-01-01 10:05:00",
    "retryCount": 0,
    "errorMessage": null
  }
}
```

#### 3.2.8 手动执行子任务

**接口信息**：
- **URL**：`POST /system/game/launch/subtask/execute`
- **权限**：系统管理员
- **功能**：手动执行指定子任务

**请求参数**：
```json
{
  "taskId": 123456,
  "subTaskId": 790,
  "forceExecute": false,
  "resetConfig": false
}
```

**响应示例**：
```json
{
  "code": 1,
  "message": "子任务执行成功",
  "data": {
    "subTaskId": 790,
    "status": 1,
    "statusName": "进行中",
    "executeTime": "2024-01-01 10:20:00"
  }
}
```

## 4. 错误码定义

### 4.1 通用错误码

| 错误码 | 错误消息 | 描述 |
|--------|----------|------|
| INVALID_PARAM | 参数错误 | 请求参数格式或内容错误 |
| UNAUTHORIZED | 权限不足 | 用户没有执行该操作的权限 |
| RESOURCE_NOT_FOUND | 资源不存在 | 请求的资源不存在 |
| OPERATION_NOT_ALLOWED | 操作不允许 | 当前状态下不允许执行该操作 |
| SYSTEM_ERROR | 系统错误 | 内部系统错误 |

### 4.2 业务错误码

| 错误码 | 错误消息 | 描述 |
|--------|----------|------|
| TASK_CREATE_FAILED | 任务创建失败 | 开服任务创建时发生错误 |
| CONFIG_NOT_FOUND | 配置不存在 | 指定的ECS配置不存在 |
| RESOURCE_INSUFFICIENT | 资源不足 | ECS资源不足，无法创建实例 |
| MYSQL_START_FAILED | MySQL启动失败 | MySQL子任务启动失败 |
| REDIS_START_FAILED | Redis启动失败 | Redis子任务启动失败 |
| MICROSERVICE_START_FAILED | 微服务启动失败 | 微服务子任务启动失败 |
| DEPLOYMENT_TIMEOUT | 部署超时 | 任务部署超过预设时间 |
| HEALTH_CHECK_FAILED | 健康检查失败 | 服务健康检查不通过 |