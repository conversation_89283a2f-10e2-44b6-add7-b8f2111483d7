package com.mega.platform.cloud.admin.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mega.platform.cloud.admin.service.AdminAppPaymentService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.common.mapper.PaymentOrderDeviceInfoMapper;
import com.mega.platform.cloud.core.PageResult;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.entity.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@Api(tags = "三方应用payment管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/api/{projectId}/third/payment")
public class AdminThirdPaymentController {

    private final AdminAppPaymentService adminAppPaymentService;
    private final PaymentOrderDeviceInfoMapper paymentOrderDeviceInfoMapper;

    @ApiOperation("Payment配置管理 创建支付配置")
    @PostMapping("/config/create")
    public Result<?> createPaymentConfig(@PathVariable Long projectId,
                                         @RequestBody AdminAppPaymentConfigReqVO reqVO) throws JsonProcessingException {
        adminAppPaymentService.paymentConfigCreate(projectId, reqVO);
        return Results.success();
    }

    @ApiOperation("Payment配置管理 删除支付配置")
    @PostMapping("/config/delete")
    public Result<?> deletePaymentConfig(@PathVariable Long projectId,
                                         @RequestBody AdminAppPaymentHandleConfigReqVO reqVO) {
        adminAppPaymentService.paymentConfigDelete(reqVO.getProjectAppId(), reqVO.getThirdPlatformId());
        return Results.success();
    }

    @ApiOperation("Payment配置管理 更新支付配置")
    @PostMapping("/config/edit")
    public Result<?> updatePaymentConfig(@PathVariable Long projectId,
                                         @RequestBody AdminAppPaymentConfigReqVO reqVO) throws JsonProcessingException {
        adminAppPaymentService.paymentConfigUpdate(projectId, reqVO);
        return Results.success();
    }

    @ApiOperation("Payment配置管理 查询支付配置列表")
    @PostMapping("/config/list")
    public Result<List<PaymentAppConfig>> listPaymentConfig(@PathVariable Long projectId,
                                                            @RequestBody AdminAppPaymentHandleConfigReqVO reqVO) {
        List<PaymentAppConfig> configs = adminAppPaymentService.paymentConfigList(reqVO);
        return Results.success(configs);
    }



    // ---------------- 产品配置 ----------------

    @ApiOperation("充值产品配置管理 创建支付产品配置")
    @PostMapping("/product/config/create")
    public Result<?> createProductConfig(@PathVariable Long projectId,
                                         @RequestBody AdminAppProductConfigReqVO reqVO) throws JsonProcessingException {
        adminAppPaymentService.paymentProductConfigCreate(reqVO);
        return Results.success();
    }

    @ApiOperation("充值产品配置管理 删除支付产品配置")
    @PostMapping("/product/config/delete")
    public Result<?> deleteProductConfig(@PathVariable Long projectId,
                                         @RequestBody AdminAppPaymentProductConfigHandleReqVO reqVO) {
        adminAppPaymentService.paymentProductConfigDelete(reqVO.getProductId());
        return Results.success();
    }

    @ApiOperation("充值产品配置管理 更新支付产品配置")
    @PostMapping("/product/config/edit")
    public Result<?> updateProductConfig(@PathVariable Long projectId,
                                         @RequestBody AdminAppProductConfigUpdateReqVO reqVO) throws JsonProcessingException {
        adminAppPaymentService.paymentProductConfigUpdate(reqVO);
        return Results.success();
    }

    @ApiOperation("充值产品配置管理 查询支付产品配置列表")
    @PostMapping("/product/config/list")
    public Result<List<PaymentProductConfig>> listProductConfig(@PathVariable Long projectId,
                                                                @RequestBody AdminAppPaymentProductConfigListReqVO reqVO) {
        List<PaymentProductConfig> configs = adminAppPaymentService.paymentProductConfigList(reqVO);
        return Results.success(configs);
    }

    // ---------------- 支付订单 ----------------

    @ApiOperation("订单查询 分页查询支付订单")
    @PostMapping("/order/list")
    public Result<PageResult<AdminPaymentOrderRespVO>> listOrders(@PathVariable Long projectId,
                                                                  @RequestBody AdminPaymentOrderListReqVO reqVO) {
        PageResult<AdminPaymentOrderRespVO> result = adminAppPaymentService.findOrderList(reqVO);
        return Results.success(result);
    }

    @ApiOperation("订单设备查询 获取订单设备信息")
    @PostMapping("/order/device")
    public Result<PaymentOrderDeviceInfo> getOrderDeviceInfo(@PathVariable Long projectId,
                                                             @RequestBody AdminPaymentDeviceReqVO reqVO) {
        return Results.success(paymentOrderDeviceInfoMapper
                .selectOne(new PaymentOrderDeviceInfo().setPaymentOrderId(reqVO.getOrderId())));
    }

    // ---------------- 订阅配置 ----------------

    @ApiOperation("订阅配置 分页查询订阅记录")
    @PostMapping("/subscription/list")
    public Result<PageResult<AdminPaymentSubscriptionRespVO>> listSubscriptions(@PathVariable Long projectId,
                                                                                @RequestBody AdminPaymentSubscriptionListReqVO reqVO) {
        PageResult<AdminPaymentSubscriptionRespVO> result = adminAppPaymentService.querySubscriptionPage(reqVO);
        return Results.success(result);
    }

    @ApiOperation("订阅扣款查询 分页查询订阅扣费日志")
    @PostMapping("/subscription/charge/log/list")
    public Result<PageResult<PaymentSubscriptionChargeLog>> listSubscriptionChargeLog(@PathVariable Long projectId,
                                                                                      @RequestBody AdminPaymentSubscriptionChargeReqVO reqVO) {
        PageResult<PaymentSubscriptionChargeLog> result = adminAppPaymentService.querySubscriptionChargeLogPage(reqVO);
        return Results.success(result);
    }

    // ---------------- 支付回调 ----------------

    @ApiOperation("回调日志查询 分页查询支付回调日志")
    @PostMapping("/callback/log/list")
    public Result<PageResult<PaymentCallbackLog>> listCallbackLogs(@PathVariable Long projectId,
                                                                   @RequestBody AdminPaymentCallbackLogReqVO reqVO) {
        PageResult<PaymentCallbackLog> result = adminAppPaymentService.queryPaymentCallbackLogPage(reqVO);
        return Results.success(result);
    }
}

