package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("ECS配置列表查询响应")
public class AdminEcsConfigListRespVO {
    
    @ApiModelProperty("ECS配置列表")
    private List<EcsConfigItemVO> configList;
    
    @Data
    @Accessors(chain = true)
    @ApiModel("ECS配置项信息")
    public static class EcsConfigItemVO {
        
        @ApiModelProperty(value = "配置ID", example = "1")
        private Long id = 0L;

        @ApiModelProperty(value = "名称", example = "生产环境配置")
        private String name = "";

        @ApiModelProperty(value = "项目ID", example = "1001")
        private Long projectId = 0L;

        @ApiModelProperty(value = "ecs_server_region.name对应阿里云的regionId字段", example = "cn-hangzhou")
        private String regionStr = "";

        @ApiModelProperty(value = "ecs_server_region.zone", example = "cn-hangzhou-i")
        private String regionZone = "";

        @ApiModelProperty(value = "项目应用ID", example = "2001")
        private Long projectAppId = 0L;

        @ApiModelProperty(value = "ECS区域ID", example = "3001")
        private Long ecsServerRegionId = 0L;

        @ApiModelProperty(value = "镜像配置ID", example = "4001")
        private Long ecsServerImageId = 0L;

        @ApiModelProperty(value = "阿里镜像id")
        private String imageRef = "";

        @ApiModelProperty(value = "安全组ID", example = "sg-12345678")
        private String securityGroupId = "";

        @ApiModelProperty(value = "子网ID", example = "subnet-12345678")
        private String subnetId = "";

        @ApiModelProperty(value = "系统规格", example = "s6.large.2")
        private String flavorRef = "";

        @ApiModelProperty(value = "磁盘类型", example = "cloud_essd")
        private String diskCategory = "";

        @ApiModelProperty(value = "磁盘级别", example = "PL1")
        private String diskPerformanceLevel = "";

        @ApiModelProperty(value = "突发性能实例", example = "standard")
        private String creditSpecification = "";

        @ApiModelProperty(value = "VPC ID", example = "vpc-12345678")
        private String vpcId = "";

        @ApiModelProperty(value = "计费模式", example = "postPaid")
        private String chargingMode = "";

        @ApiModelProperty(value = "周期类型", example = "month")
        private String periodType = "";

        @ApiModelProperty(value = "网络计费类型", example = "traffic")
        private String internetChargeType = "";

        @ApiModelProperty(value = "带宽", example = "100")
        private Integer bandwidth = 0;

        @ApiModelProperty(value = "执行脚本ID", example = "5001")
        private Long ecsServicesCommandId = 0L;

        @ApiModelProperty(value = "版本", example = "v1.0.0")
        private String version = "";

        @ApiModelProperty(value = "备注", example = "生产环境ECS配置")
        private String remark = "";

        @ApiModelProperty(value = "创建时间", example = "2024-01-01 10:00:00")
        private Date createTime = null;

        @ApiModelProperty(value = "更新时间", example = "2024-01-01 10:00:00")
        private Date updateTime = null;

        @ApiModelProperty(value = "额外数据磁盘 json结构", example = "{\"size\": 500, \"device\": \"/dev/vdb1\", \"category\": \"cloud_auto\", \"diskName\": \"data\", \"performanceLevel\": \"\"}")
        private String extraDataDisk;

        @ApiModelProperty(value = "删除标志", example = "0")
        private Integer delsign = 0;

        @ApiModelProperty(value = "ECS类型", example = "1")
        private Integer ecsType = 0;

        @ApiModelProperty(value = "镜像额外数据磁盘 json结构", example = "{\"size\": 500, \"device\": \"/dev/vdb1\", \"category\": \"cloud_auto\", \"diskName\": \"data\", \"performanceLevel\": \"\"}")
        private String imageExtraDataDisk;
    }
}