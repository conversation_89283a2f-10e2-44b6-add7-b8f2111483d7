package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.admin.dao.AdminAppPermissionDao;
import com.mega.platform.cloud.admin.vo.AdminAppPermissionListReqVO;
import com.mega.platform.cloud.admin.vo.AdminAppPermissionListRespVO;
import com.mega.platform.cloud.admin.vo.AdminAppPermissionUpdateReqVO;

import com.mega.platform.cloud.admin.vo.AppPermissionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * App访问平台权限服务类
 */
@Service
@Slf4j
public class AdminAppPermissionService {

    private final AdminAppPermissionDao adminAppPermissionDao;

    @Autowired
    public AdminAppPermissionService(AdminAppPermissionDao adminAppPermissionDao) {
        this.adminAppPermissionDao = adminAppPermissionDao;
    }

    /**
     * 获取路由配置列表
     * 查询project_url_pattern表中所有未删除的记录
     * 
     * @return 路由配置列表
     */
    public Set<String> urlPatternList() {
        try {
            List<String> sqlResult = adminAppPermissionDao.selectUrlPatternList();
            Set<String> result = new TreeSet<>(sqlResult);
            return result;
        } catch (Exception e) {
            log.error("查询路由配置列表失败", e);
            throw e;
        }
    }

    /**
     * 获取App权限列表
     * 查询project_app_permission表并关联project_app表获取App名称
     * 
     * @return App权限列表
     */
    public AdminAppPermissionListRespVO appPermissionList(Long projectId, AdminAppPermissionListReqVO reqVO) {
        AdminAppPermissionListRespVO result = new AdminAppPermissionListRespVO();
        List<AppPermissionVO> sqlResult = adminAppPermissionDao.selectAppPermissionList(projectId, reqVO.getProjectAppId());
        sqlResult.sort((a, b) -> a.getUrlPattern().compareTo(b.getUrlPattern()));
        result.setDetailList(sqlResult);
        return result;
    }

    /**
     * 更新App权限
     * 在project_app_permission表中插入或更新权限记录
     *
     * @param projectId 项目ID
     * @param reqVO 权限更新请求参数
     */
    public void updateAppPermission(Long projectId, AdminAppPermissionUpdateReqVO reqVO) {
        try {
            log.info("更新App权限，项目ID: {}, 应用项目ID: {}, URL模式: {}, 删除标识: {}",
                    projectId, reqVO.getAppProjectId(), reqVO.getUrlPattern(), reqVO.getDelsign());

            int affectedRows = adminAppPermissionDao.insertOrUpdateAppPermission(
                    projectId,
                    reqVO.getAppProjectId(),
                    reqVO.getUrlPattern(),
                    reqVO.getDelsign()
            );

            if (affectedRows > 0) {
                log.info("App权限更新成功，影响行数: {}", affectedRows);
            } else {
                log.warn("App权限更新未影响任何记录");
            }
        } catch (Exception e) {
            log.error("更新App权限失败，项目ID: {}, 应用项目ID: {}, URL模式: {}",
                    projectId, reqVO.getAppProjectId(), reqVO.getUrlPattern(), e);
            throw e;
        }
    }

}
