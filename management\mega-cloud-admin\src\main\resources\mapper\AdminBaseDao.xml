<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminBaseDao">
    
    <!-- 检查标签名称是否存在 -->
    <select id="checkTagNameExists" resultType="int">
        SELECT COUNT(1)
        FROM dic
        WHERE dic_cate_id IN (2008, 2009)
          AND name = #{tagName}
          AND delsign = 0
    </select>

    <!-- 获取指定分类下的最大dic_id -->
    <select id="getMaxDicIdByCateId" resultType="java.lang.Long">
        SELECT MAX(id)
        FROM dic
        WHERE dic_cate_id = #{dicCateId}
          AND delsign = 0
    </select>

</mapper>
