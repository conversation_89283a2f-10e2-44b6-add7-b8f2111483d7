package com.mega.platform.cloud.auth.controller;

import com.mega.platform.cloud.auth.service.AuthVerificationService;
import com.mega.platform.cloud.client.auth.AuthVerificationClient;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.vo.auth.*;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "auth验证接口")
public class AuthVerificationController implements AuthVerificationClient {
    private final AuthVerificationService authVerificationService;
    @Override
    public Result<AuthSendSmsCodeRespVO> sendSmsCode(AuthSendSmsCodeReqVO vo) throws Exception {
        return Results.success(authVerificationService.sendSmsCode(vo));
    }

    @Override
    public Result<Boolean> verifySmsCode(AuthVerifySmsCodeReqVO vo) {
        return Results.success(authVerificationService.verifySmsCode(vo));
    }

    @Override
    public Result<AuthVerifyAppleTokenRespVO> verifyAppleToken(AuthVerifyAppleTokenReqVO vo) {
        return Results.success(authVerificationService.verifyAppleToken(vo));
    }

    @Override
    public Result<AuthWeChatUserInfoRespVO> verifyWechatCode(AuthWeChatUserInfoReqVO vo) {
        return Results.success(authVerificationService.verifyWeChatCode(vo));
    }

    @Override
    public Result<AuthWeChatMiniUserInfoRespVO> verifyWechatMiniCode(AuthWeChatUserInfoReqVO vo) {
        return Results.success(authVerificationService.verifyWeChatMiniCode(vo));
    }

    @Override
    public Result<Boolean> wechatMiniCodeCheckSession(AuthWeChatCheckSessionReqVO vo) {
        return Results.success(authVerificationService.checkWeChatMiniSession(vo));
    }

    @Override
    public Result<Boolean> wechatMiniRemoveUserStorage(AuthWeChatRemoveUserStorageReqVO vo) {
        return Results.success(authVerificationService.removeWeChatUserStorage(vo));
    }

    @Override
    public Result<String> wechatMiniToken(AuthMiniTokenReqVO vo) {
        return Results.success(authVerificationService.getWeChatGeneralAccessToken(vo.getTargetAppId()));
    }

    @Override
    public Result<AuthDouyinMiniUserInfoRespVO> verifyDouyinMiniCode(AuthWeChatUserInfoReqVO vo) {
        return Results.success(authVerificationService.verifyDouyinMiniCode(vo));
    }

    @Override
    public Result<String> douyinMiniToken(AuthMiniTokenReqVO vo) throws Exception {
        return Results.success(authVerificationService.getDouyinGeneralAccessToken(vo.getTargetAppId()));
    }
}
