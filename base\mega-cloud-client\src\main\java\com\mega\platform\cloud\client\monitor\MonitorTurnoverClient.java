package com.mega.platform.cloud.client.monitor;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.monitor.MetricsTurnoverCollectReqVO;
import com.mega.platform.cloud.data.vo.monitor.MetricsTurnoverDeleteReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "mega-cloud-monitor-" + "${spring.profiles.active}", contextId = "mega-cloud-monitor-turnover-client")
@Api(tags = {"流水上报接口", "/monitor/api/turnover"})
public interface MonitorTurnoverClient {
    @ApiOperation("流水数据上报")
    @PostMapping("/monitor/api/turnover/collect")
    public Result<?> monitorTurnoverCollect(@Validated @RequestBody MetricsTurnoverCollectReqVO reqVO) throws Exception;
    @ApiOperation("流水数据重置")
    @PostMapping("/monitor/api/turnover/delete")
    public Result<?> monitorTurnoverDelete(@Validated @RequestBody MetricsTurnoverDeleteReqVO reqVO) throws Exception;
}
