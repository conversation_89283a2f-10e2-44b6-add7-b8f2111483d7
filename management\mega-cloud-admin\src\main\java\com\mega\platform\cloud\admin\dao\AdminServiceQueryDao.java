package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.admin.dto.AdminServicesJenkinsParamDTO;
import com.mega.platform.cloud.admin.dto.AdminServicesTagDTO;
import com.mega.platform.cloud.admin.dto.AdminJenkinsTaskLogDTO;
import com.mega.platform.cloud.admin.dto.AdminServicesDetailDTO;
import com.mega.platform.cloud.admin.vo.*;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 服务查询数据访问层
 */
@Repository
public interface AdminServiceQueryDao {

    /**
     * 查询全部services列表
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return services列表
     */
    List<AdminServicesDetailDTO> selectServicesList(@Param("projectId") Long projectId, 
                                                    @Param("reqVO") AdminServicesListReqVO reqVO,
                                                    @Param("tags") Set<Long> tags);

    /**
     * 基于组查询services列表
     * @param projectId 项目ID
     * @param servicesGroupId 服务组ID
     * @return services列表
     */
    List<AdminServicesListByGroupRespVO> selectServicesListByGroup(@Param("projectId") Long projectId, 
                                                                  @Param("servicesGroupId") Long servicesGroupId);

                                                                  
    /**
     * 查询services标签
     * @param servicesGroupIds 服务组ID列表
     * @return 服务组标签列表
     */
    List<AdminServicesTagDTO> selectServicesTags( @Param("servicesGroupIds") Set<Long> servicesGroupIds);

    /**
     * 查询services参数
     * @param servicesIds 服务ID列表
     * @return 服务参数列表
     */
    List<AdminServicesJenkinsParamDTO> selectServicesParams( @Param("servicesIds") Set<Long> servicesIds);

    /**
     * 查询services组参数
     * @param servicesGroupIds 服务组ID列表
     * @return 服务组参数列表
     */
    List<AdminServicesJenkinsParamDTO> selectServicesGroupParams( @Param("servicesGroupIds") Set<Long> servicesGroupIds);

    /**
     * 查询服务最后一次任务
     * @param projectId 项目ID
     * @param servicesId 服务ID
     * @return 服务最后一次任务信息
     */
    AdminServicesLastTaskRespVO selectServicesLastTask(@Param("projectId") Long projectId,
                                                      @Param("servicesId") Long servicesId);

    /**
     * 查询Jenkins任务组列表
     * @param projectId 项目ID
     * @param servicesGroupId 服务组ID
     * @return Jenkins任务组列表
     */
    List<AdminJenkinsTaskGroupListRespVO> selectJenkinsTaskGroupList(@Param("projectId") Long projectId,
                                                                     @Param("servicesGroupId") Long servicesGroupId);

    /**
     * 查询Jenkins任务组日志
     * @param projectId 项目ID
     * @param jenkinsTaskGroupId 任务组ID
     * @return Jenkins任务组日志列表
     */
    List<AdminJenkinsTaskLogDTO> selectJenkinsTaskGroupLog(@Param("projectId") Long projectId,
                                                          @Param("jenkinsTaskGroupId") Long jenkinsTaskGroupId);


    List<AdminJenkinsTemplateParamRespVO> selectServiceGroupJenkinsValue(@Param("servicesGroupId") Long servicesGroupId);


    List<AdminJenkinsTemplateParamRespVO> selectServiceJenkinsValue(@Param("serviceId") Long serviceId);

    /**
     * 查询services组的service数量
     * @return 服务组service数量列表
     */
    @MapKey("services_group_id")
    Map<Long, Map<String, Object>> selectServicesGroupServiceCount();
}