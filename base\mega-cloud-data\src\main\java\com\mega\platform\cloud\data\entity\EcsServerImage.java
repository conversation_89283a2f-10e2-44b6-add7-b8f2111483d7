package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "ecs_server_image")
public class EcsServerImage {
    /**
     * 镜像配置ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 云服务镜像ID
     */
    @Column(name = "ref")
    private String ref;

    /**
     * 镜像名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 产品ID
     */
    @Column(name = "project_id")
    private Long projectId;

    /**
     * appId
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 镜像版本号
     */
    @Column(name = "version")
    private String version;

    /**
     * 服务器区域id
     */
    @Column(name = "esc_server_region_id")
    private Long escServerRegionId;

    /**
     * 云服务镜像任务ID
     */
    @Column(name = "job_id")
    private String jobId;

    /**
     * 云厂商（Aliyun/Tencent/AWS等）
     */
    @Column(name = "provider")
    private String provider;

    /**
     * 镜像源的服务器id（阿里镜像新建模式非空）
     */
    @Column(name = "from_ecs_server_id")
    private Long fromEcsServerId;

    /**
     * 镜像拷贝的原id(阿里镜像拷贝模式非空)
     */
    @Column(name = "from_esc_server_image_id")
    private Long fromEscServerImageId;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 1 创建完成  2 正在创建 
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建镜像的管理员id
     */
    @Column(name = "admin_user_id")
    private Long adminUserId;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}