# 后台管理-开服任务调度ECS设计总览

## 1. 文档概述

### 1.1 设计目标
设计一个自动化的游戏开服任务调度系统，通过调用阿里云ECS API创建和管理MySQL、Redis、微服务等资源，实现游戏区服的快速部署和管理。

### 1.2 系统特性
- **自动化部署**：MySQL、Redis、微服务的一键式自动化部署
- **资源复用**：复用现有资源
- **任务调度**：任务状态跟踪和异步执行机制
- **模块化设计**：通过统一接口实现高度解耦，便于扩展维护
- **容错机制**：重试和异常处理策略


## 2. 文档结构

本设计方案已按照功能模块拆分为5个独立文档，每个文档专注于特定的技术领域：

### 2.1 业务流程设计
📋 **文档名称**: [`后台管理-开服任务调度ECS业务流程设计.md`](./后台管理-开服任务调度ECS业务流程设计.md)

**主要内容**：
- **总体流程设计**：从任务创建到完成的整体流程图
- **MySQL部署流程**：数据库创建和初始化的详细流程
- **Redis部署流程**：Redis集群创建和配置流程
- **微服务部署流程**：微服务部署和健康检查流程


**关键设计**：
- 模块化的子任务设计，设计为独立执行和状态跟踪
- 完善的状态流转机制，支持重试和取消操作

### 2.2 数据库设计
🗄️ **文档名称**: [`后台管理-开服任务调度ECS数据库设计.md`](./后台管理-开服任务调度ECS数据库设计.md)

**主要内容**：
- **ER图设计**：完整的实体关系图，展示表间关联
- **核心表结构**：6个核心表的详细设计和字段说明
  - `game_launch_task`：游戏开服主任务表
  - `game_launch_sub_task`：游戏开服子任务表
  - `ecs_server_config`：ECS服务器配置表
  - `ecs_server_image`：ECS服务器镜像表
  - `game_region`：游戏区服表
  - `ecs_server`：ECS服务器表（扩展现有表）
- **索引设计策略**：针对主要查询场景的复合索引设计
- **数据字典**：状态枚举和JSON字段结构定义


### 2.3 接口设计
🔌 **文档名称**: [`后台管理-开服任务调度ECS接口设计.md`](./后台管理-开服任务调度ECS接口设计.md)

**主要内容**：
- **子任务接口规范**：统一的`GameLaunchSubTask`接口定义
  - `start()`：任务启动方法
  - `checkResult()`：任务结果检测方法
  - `getTaskType()`和`getTaskName()`：辅助方法
- **子任务实现类**：MySQL、Redis、微服务三种子任务的具体实现
- **REST API设计**：8个核心接口的详细定义
  - 任务创建、查询、管理接口
  - 日志查询和子任务操作接口


### 2.4 技术实现细节
⚙️ **文档名称**: [`后台管理-开服任务调度ECS技术实现细节.md`](./后台管理-开服任务调度ECS技术实现细节.md)

**主要内容**：
- **阿里云ECS API集成**：SDK配置和服务封装
- **MySQL数据库初始化**：脚本模板和初始化服务
- **Redis集群配置**：主从哨兵部署和配置管理
- **微服务部署策略**：Docker化部署和配置动态生成
- **异步任务调度器**：任务调度和线程池配置
- **系统架构设计**：分层架构和组件交互



### 2.5 其他相关文档
📚 **文档名称**: [`后台管理-开服任务调度ECS其他相关.md`](./后台管理-开服任务调度ECS其他相关.md)

**主要内容**：
- **配置管理策略**：多环境配置和敏感信息管理
- **部署策略**：容器化部署和灰度发布方案
- **运维监控**：监控指标定义和日志管理策略
- **业务细节确认**：10个关键业务问题的确认事项


## 3. 系统架构总览

### 3.1 整体架构图

```mermaid
graph TB
    subgraph "用户层"
        A[管理员] --> B[开服任务创建]
        A --> C[任务状态监控]
        A --> D[日志查询分析]
    end
    
    subgraph "应用层"
        E[GameLaunchController] --> F[GameLaunchService]
        F --> G[MySQL部署服务]
        F --> H[Redis部署服务]
        F --> I[微服务部署服务]
    end
    
    subgraph "调度层"
        J[任务调度器] --> K[状态监控器]
        J --> L[健康检查器]
        J --> M[重试机制]
    end
    
    subgraph "资源层"
        N[阿里云ECS API] --> O[MySQL实例]
        N --> P[Redis集群]
        N --> Q[微服务实例]
    end
    
    subgraph "数据层"
        R[(任务数据库)]
        S[(配置数据库)]
        T[(日志存储)]
    end
    
    B --> E
    C --> E
    D --> E
    F --> J
    G --> N
    H --> N
    I --> N
    F --> R
    F --> S
    J --> T
```

### 3.2 数据流向

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as Controller
    participant S as Service
    participant T as TaskScheduler
    participant E as EcsService
    participant D as Database
    
    U->>C: 创建开服任务
    C->>S: 处理任务请求
    S->>D: 保存任务信息
    S->>T: 提交任务到调度器
    
    T->>S: 执行MySQL子任务
    S->>E: 调用ECS API创建实例
    E-->>S: 返回实例ID
    S->>D: 更新任务状态
    
    T->>S: 执行Redis子任务
    S->>E: 创建Redis集群
    S->>D: 更新任务状态
    
    T->>S: 执行微服务子任务
    S->>E: 创建微服务实例
    S->>D: 更新任务状态
    
    S->>D: 插入区服信息
    S-->>U: 返回任务完成结果
```

## 4. 核心功能模块

### 4.1 任务管理模块
- **任务创建**：支持参数配置的任务创建
- **任务执行**：异步的任务执行和状态跟踪
- **任务监控**：实时的进度监控和日志查看
- **任务管理**：支持取消、重试等操作

### 4.2 资源管理模块
- **ECS管理**：自动化的ECS实例创建和配置
- **配置管理**：模板化的配置生成和管理
- **资源复用**：智能的资源复用判断和分配
- **状态监控**：实时的资源状态监控

### 4.3 部署服务模块
- **MySQL部署**：数据库实例创建和初始化
- **Redis部署**：Redis集群配置和哨兵部署
- **微服务部署**：应用部署和健康检查
- **配置注入**：动态配置生成和注入
