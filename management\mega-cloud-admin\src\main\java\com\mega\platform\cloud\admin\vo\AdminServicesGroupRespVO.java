package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel("服务组列表返回项")
public class AdminServicesGroupRespVO {

    @ApiModelProperty("标签ID列表（逗号分隔）")
    private String tagIds;

    @ApiModelProperty(value = "自定义组标签ID列表 逗号分隔", example = "1,2,3")
    private String customizeTagIds;

    @ApiModelProperty("服务组ID")
    private Long id;

    @ApiModelProperty("服务组名称")
    private String name;

    @ApiModelProperty("项目ID")
    private Long projectId;

    @ApiModelProperty("项目应用ID")
    private Long projectAppId;

    @ApiModelProperty("服务更新类型")
    private Integer servicesUpdateType;

    @ApiModelProperty("微服务环境 2005")
    private String servicesEnv;

    @ApiModelProperty("微服务日志格式ID")
    private Long servicesLogFormatId;

    @ApiModelProperty("滚服/导流重启时保活数量")
    private Integer servicesAliveNum;

    @ApiModelProperty("Jenkins 服务ID")
    private Long jenkinsServicesId;

    @ApiModelProperty("Jenkins 模板ID")
    private Long jenkinsTemplateId;

    @ApiModelProperty("是否自研（1-自研，3-三方）")
    private Integer isSelf;

    @ApiModelProperty("负责人ID")
    private Long adminUserId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态 2001")
    private Integer status;

    @ApiModelProperty("运行状态 2003")
    private Integer runningStatus;

    @ApiModelProperty("真实运行状态 2003")
    private Integer realRunningStatus;

    @ApiModelProperty("检查运行方式 2008")
    private Integer checkAliveType;

    @ApiModelProperty("是否使用 Jenkins（0-否，1-是）")
    private Byte useJenkins;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("是否删除（0-未删除，1-已删除）")
    private Byte delsign;

    @ApiModelProperty("当前组service数量")
    private Integer serviceCount;

    @ApiModelProperty("当前组运行service数量")
    private Integer runningServiceCount;

    @ApiModelProperty("环境ID")
    private Long envId;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("管理员name")
    private String adminUserName;

    @ApiModelProperty("app name")
    private String projectAppName;

    @ApiModelProperty("project name")
    private String projectName;
}
