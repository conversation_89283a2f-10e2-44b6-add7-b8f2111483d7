package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 查询Jenkins任务组日志响应参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "查询Jenkins任务组日志响应参数", description = "查询Jenkins任务组日志响应参数")
public class AdminJenkinsTaskGroupLogRespVO {

    @ApiModelProperty("任务组ID")
    private Long jenkinsTaskGroupId;

    @ApiModelProperty("任务ID")
    private String jenkinsTaskId;

    @ApiModelProperty("日志内容列表")
    private List<String> logContentList;
}
