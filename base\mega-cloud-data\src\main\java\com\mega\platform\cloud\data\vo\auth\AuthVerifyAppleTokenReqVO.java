package com.mega.platform.cloud.data.vo.auth;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("苹果token请求参数")
public class AuthVerifyAppleTokenReqVO extends BaseReqVO {
    @ApiModelProperty(value = "苹果token")
    @NotBlank(message = "idToken不能为空")
    private String idToken;

    @NotBlank(message = "clientIp不能为空")
    private String clientIp;

    @NotBlank(message = "deviceId不能为空")
    private String deviceId;
}
