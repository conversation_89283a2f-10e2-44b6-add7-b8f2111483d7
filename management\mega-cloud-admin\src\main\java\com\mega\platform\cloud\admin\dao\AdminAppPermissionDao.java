package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.admin.vo.AppPermissionVO;
import feign.Param;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * App访问平台权限数据访问层
 */
@Mapper
public interface AdminAppPermissionDao {
    
    /**
     * 查询路由配置列表
     * 查询project_url_pattern表中所有未删除的记录
     * 
     * @return 路由配置列表
     */
    List<String> selectUrlPatternList();
    
    /**
     * 查询App权限列表
     * 查询project_app_permission表并关联project_app表获取App名称
     * @param projectId
     * @param projectAppId
     *
     * @return App权限列表
     */
    List<AppPermissionVO> selectAppPermissionList(@Param("projectId") Long projectId, @Param("projectAppId") Long projectAppId);

    /**
     * 插入或更新App权限
     * 使用 INSERT ... ON DUPLICATE KEY UPDATE 语法
     * 当 app_project_id 和 url_pattern 组合冲突时更新 delsign 字段
     *
     * @param projectId 项目ID
     * @param appProjectId 应用项目ID
     * @param urlPattern URL模式
     * @param delsign 删除标识：0=未删除，1=已删除
     * @return 影响的行数
     */
    int insertOrUpdateAppPermission(@Param("projectId") Long projectId,
                                   @Param("appProjectId") Long appProjectId,
                                   @Param("urlPattern") String urlPattern,
                                   @Param("delsign") Integer delsign);
}
