package com.mega.platform.cloud.auth.controller;

import com.mega.platform.cloud.common.constant.JwtConstants;
import com.mega.platform.cloud.common.utils.JwtTokenUtil;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "测试导流通知接口")
@Slf4j
@RestController
@RequestMapping("/actuator/platform")
public class TestMonitorNotifyController {
    private final JwtTokenUtil jwtTokenUtil;

    public TestMonitorNotifyController(JwtTokenUtil jwtTokenUtil) {
        this.jwtTokenUtil = jwtTokenUtil;
    }

    @ApiOperation("测试")
    @PostMapping("/notify/stop")
    public Result<?> platformNotifyStop() {
        return Results.success();
    }

    @ApiOperation("测试")
    @PostMapping("/check/canStop")
    public Result<?> platformCheckCanStop() {
        return Results.success();
    }
}
