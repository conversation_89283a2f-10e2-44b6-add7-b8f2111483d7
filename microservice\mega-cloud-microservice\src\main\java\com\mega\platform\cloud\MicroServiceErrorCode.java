package com.mega.platform.cloud;

import com.mega.platform.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum MicroServiceErrorCode {

    CREATE_JENKINS_JOB_FAILED_SERVICES_GROUP_PARAM_ERROR(10000),
    CREATE_JENKINS_JOB_FAILED_SERVICES_PARAM_ERROR(10001),
    BUILD_JENKINS_JOB_FAILED_SERVICES_NUM_LESS_THAN_ALIVE_NUM(10002),
    BUI<PERSON>_JENKINS_JOB_FAILED_ALIVE_NUM_LESS_THAN_2(10003),
    BUILD_JENKINS_JOB_FAILED_SERVICES_GROUP_NOT_ONLINE(10004),
    BUILD_JENKINS_JOB_FAILED_SERVICES_GROUP_RUNNING_STATUS_ERROR(10005),
    BUI<PERSON>_JENKINS_JOB_FAILED_SERVICES_ONLY_SUPPORT_STOP(10006),
    BUILD_JENKINS_JOB_FAILED_SERVICES_NOT_ONLINE(10007),
    BUILD_JENKINS_JOB_FAILED_SERVICES_RUNNING_STATUS_ERROR(10008),
    CREATE_SERVICES_GROUP_FAILED_NAME_DUPLICATE(10009),
    CREATE_SERVICES_FAILED_NAME_DUPLICATE(10010),
    CREATE_SERVICES_FAILED_SAME_ECS_PORT(10011),
    BUILD_JENKINS_JOB_FAILED_SERVICES_GROUP_BUILDING(10012),
    BUILD_JENKINS_JOB_FAILED_NOT_USE_JENKINS(10013),
    BUILD_JENKINS_JOB_FAILED_IS_MICROSERVICE_APP(10014),
    ;

    private final Integer code;

    MicroServiceErrorCode(Integer code) {
        this.code = code;
    }

    public static MicroServiceErrorCode getExchangeCode(Integer code) {
        for (MicroServiceErrorCode exchangeCode : MicroServiceErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
