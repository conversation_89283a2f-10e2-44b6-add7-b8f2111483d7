package com.mega.platform.cloud.push.controller;

import com.mega.platform.cloud.client.push.PushClient;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.vo.push.PushDouyinMiniReqVO;
import com.mega.platform.cloud.data.vo.push.PushWeChatMiniReqVO;
import com.mega.platform.cloud.push.service.PushService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "auth验证接口")
public class PushController implements PushClient {
    private final PushService pushService;

    @Override
    public Result<?> wechatMiniPush(PushWeChatMiniReqVO vo) {
        pushService.wechatMiniPush(vo);
        return Results.success();
    }

    @Override
    public Result<?> douyinMiniPush(PushDouyinMiniReqVO vo) throws Exception {
        pushService.douyinMiniPush(vo);
        return Results.success();
    }
}
