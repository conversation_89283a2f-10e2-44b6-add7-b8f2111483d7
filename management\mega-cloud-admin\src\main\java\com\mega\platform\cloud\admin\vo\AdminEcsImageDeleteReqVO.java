package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * ECS镜像删除请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel("ECS镜像删除请求参数")
public class AdminEcsImageDeleteReqVO {

    @NotBlank(message = "项目ID不能为空")
    @ApiModelProperty("项目ID")
    private Long projectId;

    @NotBlank(message = "镜像ID不能为空")
    @ApiModelProperty("镜像ID")
    private Long imageId;
}
