package com.mega.platform.cloud.monitor.controller;

import com.mega.platform.cloud.client.monitor.MonitorTurnoverClient;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.monitor.MetricsTurnoverCollectReqVO;
import com.mega.platform.cloud.data.vo.monitor.MetricsTurnoverDeleteReqVO;
import com.mega.platform.cloud.monitor.service.items.MonitorTurnoverService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "流水接口")
@Slf4j
@RestController
@RequiredArgsConstructor
public class MonitorTurnoverController implements MonitorTurnoverClient {

    private final MonitorTurnoverService monitorTurnoverService;

    @Override
    public Result<?> monitorTurnoverCollect(MetricsTurnoverCollectReqVO reqVO) throws Exception {
        return null;
    }

    @Override
    public Result<?> monitorTurnoverDelete(MetricsTurnoverDeleteReqVO reqVO) throws Exception {
        return null;
    }
}
