package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "ecs_server_log")
public class EcsServerLog {
    /**
     * 创建esc的记录表
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 创建esc的配置表
     */
    @Column(name = "ecs_server_id")
    private Long ecsServerId;

    /**
     * 名字
     */
    @Column(name = "name")
    private String name;

    /**
     * 项目id
     */
    @Column(name = "project_id")
    private Long projectId;

    /**
     * appId
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * esc区域ID
     */
    @Column(name = "esc_server_region_id")
    private Long escServerRegionId;

    /**
     * 镜像配置ID
     */
    @Column(name = "esc_server_image_id")
    private Long escServerImageId;

    /**
     * 子网id。 阿里 虚拟交换机ID vSwitchId 
     */
    @Column(name = "subnet_id")
    private String subnetId;

    /**
     * ipv4 地址前缀
     */
    @Column(name = "ip_prefix")
    private String ipPrefix;

    /**
     * 系统规格。阿里 实例的资源规格instanceType 
     */
    @Column(name = "flavor_ref")
    private String flavorRef;

    /**
     * 磁盘类型
     */
    @Column(name = "disk_category")
    private String diskCategory;

    /**
     * 磁盘级别
     */
    @Column(name = "disk_performance_level")
    private String diskPerformanceLevel;

    /**
     * 突发性能实例
     */
    @Column(name = "credit_specification")
    private String creditSpecification;

    /**
     * 指定已创建VPC的ID
     */
    @Column(name = "vpc_id")
    private String vpcId;

    /**
     * 华为：prePaid周期付款，postPaid按需付款。 阿里：PostPaid按需付费， PrePaid包年包月
     */
    @Column(name = "charging_mode")
    private String chargingMode;

    /**
     * charging_mode=prePaid起作用，只有month,year两种
     */
    @Column(name = "period_type")
    private String periodType;

    /**
     * 阿里云网络计费类型:PayByBandwidth-按固定带宽计费,PayByTraffic-按使用流量计费
     */
    @Column(name = "internet_charge_type")
    private String internetChargeType;

    /**
     * 带宽
     */
    @Column(name = "bandwidth")
    private Integer bandwidth;

    /**
     * 创建esc完成后执行脚本ID
     */
    @Column(name = "ecs_services_command_id")
    private Long ecsServicesCommandId;

    /**
     * 配置版本
     */
    @Column(name = "version")
    private String version;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;

    /**
     * 安全组id
     */
    @Column(name = "security_group_id")
    private String securityGroupId;
}