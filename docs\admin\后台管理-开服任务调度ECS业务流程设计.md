# 后台管理-开服任务调度ECS业务流程设计

## 1. 概述

### 1.1 设计目标
本文档详细描述游戏开服任务调度系统的业务流程设计，包括总体流程和各个子任务的执行流程。

### 1.2 流程分类
- **总体流程**：从任务创建到完成的整体流程
- **MySQL部署流程**：MySQL数据库的创建和初始化流程
- **Redis部署流程**：Redis集群的创建和配置流程
- **微服务部署流程**：微服务的部署和健康检查流程

## 2. 总体流程设计

### 2.1 主流程图

```mermaid
graph TD
    A[开服任务创建] --> B[任务分解]
    B --> C[MySQL子任务]
    B --> D[Redis子任务] 
    B --> E[微服务子任务]
    
    C --> C1{是否需要新建MySQL}
    C1 -->|否| C2[获取运行中的MySQL实例]
    C1 -->|是| C3[创建MySQL ECS]
    C2 --> C4[MySQL任务完成]
    C3 --> C5[轮询ECS创建状态]
    C5 --> C6[MySQL初始化]
    C6 --> C4
    
    D --> D1{是否需要新建Redis}
    D1 -->|否| D2[获取运行中的Redis实例]
    D1 -->|是| D3[创建Redis ECS集群]
    D2 --> D4[Redis任务完成]
    D3 --> D5[轮询ECS创建状态]
    D5 --> D6[Redis主从哨兵配置]
    D6 --> D4
    
    E --> E1[创建微服务ECS]
    E1 --> E2[轮询ECS创建状态]
    E2 --> E3[微服务配置初始化]
    E3 --> E4[启动微服务]
    E4 --> E5[健康检查]
    E5 --> E6[微服务任务完成]
    
    C4 --> F[所有子任务完成检查]
    D4 --> F
    E6 --> F
    F --> G[插入区服信息]
    G --> H[开服任务完成]
```

### 2.2 任务状态流转图

```mermaid
stateDiagram-v2
    [*] --> 待处理
    待处理 --> 进行中 : 开始执行
    进行中 --> 成功 : 执行完成
    进行中 --> 失败 : 执行失败
    进行中 --> 已取消 : 手动取消
    失败 --> 进行中 : 重试
    失败 --> 已取消 : 放弃重试
    成功 --> [*]
    已取消 --> [*]
```

## 3. MySQL部署流程

### 3.1 MySQL部署详细流程

```mermaid
graph TD
    A[MySQL子任务开始] --> B{检查是否需要新建}
    B -->|复用现有| C[查询运行中的MySQL ECS]
    B -->|新建| D[获取MySQL ECS配置]
    
    C --> E[验证MySQL连接]
    E --> F[更新任务状态为成功]
    
    D --> G[调用阿里云ECS API创建实例]
    G --> H[定时轮询ECS状态]
    H --> I{ECS是否创建成功?}
    I -->|否| J{是否超时?}
    J -->|否| H
    J -->|是| K[任务失败]
    I -->|是| L[MySQL服务启动检查]
    L --> M[生成MySQL连接信息]
    M --> N[执行数据库初始化脚本]
    N --> O[同步数据结构和基础数据]
    O --> F
```

### 3.2 MySQL初始化步骤

1. **ECS实例创建**
   - 根据配置选择合适的ECS规格
   - 使用预配置的MySQL镜像
   - 设置安全组和网络配置
2. **MySQL服务启动**
   - 检查MySQL服务状态
   - 验证端口连通性
   - 配置root密码

3. **数据库初始化**
   - 创建游戏专用数据库
   - 创建游戏用户并授权
   - 执行数据结构脚本
   - 导入基础数据

4. **连接信息保存**
   - 生成连接配置
   - 保存到任务结果中
   - 更新ECS服务器记录

## 4. Redis部署流程

### 4.1 Redis集群部署流程

```mermaid
graph TD
    A[Redis子任务开始] --> B{检查是否需要新建}
    B -->|复用现有| C[查询运行中的Redis ECS]
    B -->|新建| D[获取Redis ECS配置]
    
    C --> E[验证Redis连接]
    E --> F[更新任务状态为成功]
    
    D --> G[创建3台Redis ECS实例]
    G --> H[定时轮询所有ECS状态]
    H --> I{所有ECS是否创建成功?}
    I -->|否| J{是否超时?}
    J -->|否| H
    J -->|是| K[任务失败]
    I -->|是| L[配置Redis主从关系]
    L --> M[部署Sentinel哨兵节点]
    M --> N[验证Redis集群状态]
    N --> F
```

### 4.2 Redis集群配置步骤

1. **多实例创建**
   - 创建1个主节点ECS实例
   - 创建2个从节点ECS实例
   - 等待所有实例就绪

2. **主从配置**
   - 配置主节点Redis服务
   - 配置从节点并指向主节点
   - 验证主从同步状态

3. **哨兵部署**
   - 在每个节点部署Sentinel
   - 配置哨兵监控主节点
   - 测试故障转移机制

4. **集群验证**
   - 测试读写操作
   - 验证高可用性
   - 保存集群连接信息

## 5. 微服务部署流程

*
```mermaid
graph TD
   A[微服务子任务开始] --> B[获取微服务ECS配置]
   B --> C[调用阿里云ECS API创建实例]
   C --> D[定时轮询ECS状态]
   D --> E{ECS是否创建成功?}
   E -->|否| F{是否超时?}
   F -->|否| D
   F -->|是| G[任务失败]
   E -->|是| H[任务成功]
```
*


