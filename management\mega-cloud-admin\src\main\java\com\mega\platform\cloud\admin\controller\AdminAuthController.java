package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.constant.AdminAuthConstant;
import com.mega.platform.cloud.admin.service.AdminAuthService;
import com.mega.platform.cloud.admin.vo.AdminAuthLoginReqVO;
import com.mega.platform.cloud.admin.vo.AdminAuthLoginRespVO;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 管理员认证控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/api")
@Api(tags = "管理员认证")
@Slf4j
public class AdminAuthController {

    private final AdminAuthService adminAuthService;

    @Autowired
    public AdminAuthController(AdminAuthService adminAuthService) {
        this.adminAuthService = adminAuthService;
    }

    /**
     * 管理员登录
     *
     * @param reqVO 登录请求参数
     * @return 登录响应结果
     */
    @PostMapping("/public/auth/login")
    @ApiOperation("管理员登录")
    public Result<AdminAuthLoginRespVO> login(@Validated @RequestBody AdminAuthLoginReqVO reqVO) {
        AdminAuthLoginRespVO result = adminAuthService.login(reqVO);
        return Results.success(result);
    }

    @PostMapping("/system/auth/profile")
    @ApiOperation("获取登录用户详细信息")
    public Result<AdminAuthProfileRespVO> profile(HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        if (adminUserId == null) {
            throw new RuntimeException("用户未登录或登录已过期");
        }
        AdminAuthProfileRespVO result = adminAuthService.getProfile(adminUserId);
        return Results.success(result);
    }

    /**
     * 管理员登出
     *
     * @param request HTTP请求对象
     * @return 登出响应结果
     */
    @PostMapping("/system/auth/logout")
    @ApiOperation("管理员登出")
    public Result<?> logout(HttpServletRequest request) {

        // 从请求上下文中获取管理员用户ID
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        if (adminUserId == null) {
            throw new RuntimeException("用户未登录或登录已过期");
        }

        adminAuthService.logout(adminUserId);
        return Results.success();
    }

    /**
     * 管理员全部登出
     *
     * @param request HTTP请求对象
     * @return 全部登出响应结果
     */
    @PostMapping("/system/auth/logout-all")
    @ApiOperation("管理员全部登出")
    public Result<?> logoutAll(HttpServletRequest request) {
            // 从请求上下文中获取管理员用户ID
            Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
            if (adminUserId == null) {
                throw new RuntimeException("用户未登录或登录已过期");
            }

            adminAuthService.logoutAll(adminUserId);
            return Results.success();
    }

}