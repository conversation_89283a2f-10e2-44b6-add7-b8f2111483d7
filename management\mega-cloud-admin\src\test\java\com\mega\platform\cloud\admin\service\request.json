{"queryParameters": {"IoOptimized": "optimized", "Description": "", "ZoneId": "cn-hangzhou-i", "InstanceChargeType": "PostPaid", "CpuOptions": {"core": 1, "threadsPerCore": 2}, "VSwitchId": "vsw-bp1lw1longj7zoxy5txez", "SecurityGroupIds": ["sg-bp1atg4jn10vcvy39lch", " sg-bp1fvp7j3mibt0kxcs0p"], "InternetChargeType": "PayByTraffic", "SystemDisk": {"category": "cloud_auto", "size": "40", "burstingEnabled": true, "provisionedIops": 0}, "InstanceName": "nginx-5", "InternetMaxBandwidthOut": 100, "ImageId": "m-bp15kyrnd24wnnm2fe6j", "RegionId": "cn-hangzhou", "InstanceType": "ecs.t6-c1m1.large", "CreditSpecification": "Unlimited", "PasswordInherit": true}, "hostParameters": {}, "pathParameters": {}, "headerParameters": {}, "cpuOptions": {"core": 1, "threadsPerCore": 2}, "systemDisk": {"category": "cloud_auto", "size": "40", "burstingEnabled": true, "provisionedIops": 0}, "creditSpecification": "Unlimited", "description": "", "imageId": "m-bp15kyrnd24wnnm2fe6j", "instanceChargeType": "PostPaid", "instanceName": "nginx-5", "instanceType": "ecs.t6-c1m1.large", "internetChargeType": "PayByTraffic", "internetMaxBandwidthOut": 0, "ioOptimized": "optimized", "passwordInherit": true, "regionId": "cn-hangzhou", "securityGroupIds": ["sg-bp1atg4jn10vcvy39lch", " sg-bp1fvp7j3mibt0kxcs0p"], "zoneId": "cn-hangzhou-i", "vswitchId": "vsw-bp1lw1longj7zoxy5txez"}