package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.admin.dao.AdminServiceQueryDao;
import com.mega.platform.cloud.admin.dto.AdminServicesJenkinsParamDTO;
import com.mega.platform.cloud.admin.dto.AdminServicesTagDTO;
import com.mega.platform.cloud.admin.dto.AdminJenkinsTaskLogDTO;
import com.mega.platform.cloud.admin.dto.AdminServicesDetailDTO;
import com.mega.platform.cloud.admin.vo.*;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务查询业务逻辑层
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminServiceQueryService {

    
    private final AdminServiceQueryDao adminServiceQueryDao;

    /**
     * 查询全部services列表
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return services列表
     */
    public List<AdminServicesListRespVO> getServicesList(Long projectId, AdminServicesListReqVO reqVO) {
            if(Strings.isEmpty(reqVO.getKeyword()) && !Strings.isEmpty(reqVO.getServicesName())) {
                reqVO.setKeyword(reqVO.getServicesName());
            }
            if(Strings.isEmpty(reqVO.getKeyword()) && !Strings.isEmpty(reqVO.getRemark())) {
                reqVO.setKeyword(reqVO.getRemark());
            }

            // 合并系统标签和自定义标签
            Set<Long> tagSet = new HashSet<>();
            if (reqVO.getTags() != null) {
                tagSet.addAll(reqVO.getTags());
            }
            if (reqVO.getCustomizeTaglds() != null) {
                tagSet.addAll(reqVO.getCustomizeTaglds());
            }

            // 1 查询总信息
            List<AdminServicesDetailDTO> sqlResult = adminServiceQueryDao.selectServicesList(projectId, reqVO, tagSet);
            if (sqlResult == null || sqlResult.isEmpty()) {
                return new ArrayList<>();
            }
            log.info("查询全部services列表成功，projectId: {}, 结果数量: {}", projectId, sqlResult.size());

            // 遍历result，把所有servicesId存入Set<Long> ,也把servicesGroupId存入Set<Long>
            Set<Long> servicesIds = new HashSet<>();
            Set<Long> servicesGroupIds = new HashSet<>();
            List<AdminServicesListRespVO> respVOs = new ArrayList<>();
            // 固定顺序
            for (int i = 0; i < sqlResult.size(); i++) {
                AdminServicesDetailDTO item = sqlResult.get(i);
                servicesIds.add(item.getServicesId());
                servicesGroupIds.add(item.getId());

                AdminServicesListRespVO respVO = new AdminServicesListRespVO();

                // 设置services基本信息
                respVO.setPublicIp(item.getPublicIp() != null ? item.getPublicIp() : "");
                respVO.setInnerIp(item.getInnerIp() != null ? item.getInnerIp() : "");
                respVO.setServicesId(item.getServicesId() != null ? item.getServicesId() : 0L);
                respVO.setServicesName(item.getServicesName() != null ? item.getServicesName() : "");
                respVO.setServicesGroupId(item.getServicesGroupId() != null ? item.getServicesGroupId() : 0L);
                respVO.setEcsServerId(item.getServicesEcsServerId() != null ? item.getServicesEcsServerId() : 0L);
                respVO.setJenkinsJobId(item.getServicesJenkinsJobId() != null ? item.getServicesJenkinsJobId() : 0L);
                respVO.setPath(item.getPath() != null ? item.getPath() : "");
                respVO.setLogPath(item.getLogPath() != null ? item.getLogPath() : "");
                respVO.setVersion(item.getVersion() != null ? item.getVersion() : "");
                respVO.setDescription(item.getDescription() != null ? item.getDescription() : "");
                respVO.setLogTimeoutSecond(item.getLogTimeoutSecond() != null ? item.getLogTimeoutSecond() : 0);
                respVO.setLogStatus(item.getLogStatus() != null ? item.getLogStatus() : 0);
                respVO.setServicesRemark(item.getServicesRemark() != null ? item.getServicesRemark() : "");
                respVO.setServicesStatus(item.getServicesStatus() != null ? item.getServicesStatus() : 0);
                respVO.setServicesRunningStatus(item.getServicesRunningStatus() != null ? item.getServicesRunningStatus() : 0);
                respVO.setServicesRealRunningStatus(item.getServicesRealRunningStatus() != null ? item.getServicesRealRunningStatus() : 0);
                respVO.setSort(item.getSort() != null ? item.getSort() : 0);

                // 设置服务组信息
                AdminServicesListRespVO.ServicesGroup group = new AdminServicesListRespVO.ServicesGroup();
                group.setId(item.getId());
                group.setName(item.getName());
                group.setProjectId(item.getProjectId());
                group.setProjectAppId(item.getProjectAppId());
                group.setServicesUpdateType(item.getServicesUpdateType());
                group.setServicesEnv(item.getServicesEnv());
                group.setServicesEnvId(item.getServicesEnvId());
                group.setServicesLogFormatId(item.getServicesLogFormatId());
                group.setServicesAliveNum(item.getServicesAliveNum());
                group.setJenkinsServicesId(item.getJenkinsServicesId());
                group.setJenkinsTemplateId(item.getJenkinsTemplateId());
                group.setIsSelf(item.getIsSelf());
                group.setAdminUserId(item.getAdminUserId());
                group.setRemark(item.getRemark());
                group.setStatus(item.getStatus());
                group.setRunningStatus(item.getRunningStatus());
                group.setRealRunningStatus(item.getRealRunningStatus());
                group.setCheckAliveType(item.getCheckAliveType());
                group.setUseJenkins(item.getUseJenkins() == null ? 0 : item.getUseJenkins());
                group.setCreateTime(item.getCreateTime());
                group.setUpdateTime(item.getUpdateTime());
                group.setDelsign(item.getServicesGroupDelsign());
                group.setSort(item.getServicesGroupSort());
                group.setAdminUserName(item.getAdminUserName());
                group.setProjectName(item.getProjectName());
                group.setProjectAppName(item.getProjectAppName());
                respVO.setServicesGroupDetail(group);

                // 设置最后一次jenkins任务组信息
                AdminServicesListRespVO.TaskGroup taskGroup = new AdminServicesListRespVO.TaskGroup();
                taskGroup.setLastTaskGroupId(item.getLastTaskGroupId());
                taskGroup.setLastTaskGroupAction(item.getLastTaskGroupAction());
                taskGroup.setLastTaskGroupIsSuccess(item.getLastTaskGroupIsSuccess());
                taskGroup.setLastTaskGroupCompleteTime(item.getLastTaskGroupCompleteTime());
                respVO.setLastTaskGroup(taskGroup);

                // 设置最后一次jenkins任务信息
                AdminServicesListRespVO.Task task = new AdminServicesListRespVO.Task();
                task.setLastTaskId(item.getLastTaskId());
                task.setLastTaskAction(item.getLastTaskAction());
                task.setLastTaskIsSuccess(item.getLastTaskIsSuccess());
                task.setLastTaskCompleteTime(item.getLastTaskCompleteTime());
                task.setLastTaskJenkinsJobUrl(item.getLastTaskJenkinsJobUrl());
                respVO.setLastTask(task);

                respVOs.add(respVO);
            }

            // 2 查询tags
            Map<Long, List<Long>> servicesTagsMap = getServicesTagsMap(servicesGroupIds);

            // 3 查询services params 
            Map<Long, Map<String,String>> servicesParamsMap = getServicesParams(servicesIds);

            // 4 查询services group params
            Map<Long, Map<String,String>> servicesGroupParamsMap = getServicesGroupParams(servicesGroupIds);

            // 5 查询 services group 的service数量
           Map<Long, Integer> servicesGroupServiceCountMap = getServicesGroupServiceCount();
            
            // 遍历respVOs补充tags和params信息
            for (AdminServicesListRespVO item : respVOs) {   
                Map<String,String> servicesParams = servicesParamsMap.get(item.getServicesId());
                item.setJenkinsParams(servicesParams != null ? servicesParams : new HashMap<>());

                List<Long> tags = servicesTagsMap.get(item.getServicesGroupDetail().getId());
                if (tags != null && !tags.isEmpty()) {
                    String systemTagsStr = tags.stream()
                        .filter(tagId -> !String.valueOf(tagId).startsWith("2009"))
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
                    String customizeTagsStr = tags.stream()
                        .filter(tagId -> String.valueOf(tagId).startsWith("2009"))
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
                    item.getServicesGroupDetail().setTagIds(systemTagsStr);
                    item.getServicesGroupDetail().setCustomizeTagIds(customizeTagsStr);
                } else {
                    item.getServicesGroupDetail().setTagIds("");
                    item.getServicesGroupDetail().setCustomizeTagIds("");
                }

                // 补充service数量
                Integer serviceCount = servicesGroupServiceCountMap.get(item.getServicesGroupDetail().getId());
                item.getServicesGroupDetail().setServiceCount(serviceCount != null ? serviceCount : 0);

                Map<String,String> groupParams = servicesGroupParamsMap.get(item.getServicesGroupDetail().getId());
                item.getServicesGroupDetail().setServicesGroupParams(groupParams != null ? groupParams : new HashMap<>());
            }

            return respVOs;
    }

    /**
     * 查询services group 的service数量
     * @return
     */
    private Map<Long, Integer> getServicesGroupServiceCount() {
        Map<Long, Map<String, Object>> countMap = adminServiceQueryDao.selectServicesGroupServiceCount();
        Map<Long, Integer> result = new HashMap<>();
        for (Map.Entry<Long, Map<String, Object>> entry : countMap.entrySet()) {
            result.put(entry.getKey(), (Integer) entry.getValue().get("count"));
        }
        return result;
    }

    /**
     * 获取services标签map
     * @param servicesGroupIds
     * @return key: servicesGroupId, value: tagId列表
     */
    private Map<Long, List<Long>> getServicesTagsMap( Set<Long> servicesGroupIds) {
        List<AdminServicesTagDTO> tagList = adminServiceQueryDao.selectServicesTags(servicesGroupIds);
        // 生成map key为 servicesGroupId, value为tagId列表
        return tagList.stream()
                      .collect(
                        Collectors.groupingBy(AdminServicesTagDTO::getServiceGroupId, 
                        Collectors.mapping(AdminServicesTagDTO::getTagId, Collectors.toList()))
                        ); 
    }

    private Map<Long, Map<String,String>> getServicesParams(Set<Long> servicesIds){
        List<AdminServicesJenkinsParamDTO> servicesParams = adminServiceQueryDao.selectServicesParams(servicesIds);
        // 生成map key为servicesId , value为paramKey和paramValue组成的map
        return servicesParams.stream()
                      .collect(
                        Collectors.groupingBy(AdminServicesJenkinsParamDTO::getId, 
                        Collectors.toMap(AdminServicesJenkinsParamDTO::getParamName, AdminServicesJenkinsParamDTO::getParamValue))
                        );
    }

    private Map<Long, Map<String, String>> getServicesGroupParams(Set<Long> servicesGroupIds) {
        List<AdminServicesJenkinsParamDTO> servicesGroupParams = adminServiceQueryDao.selectServicesGroupParams(servicesGroupIds);
        // 生成map key为servicesGroupId , value为paramKey和paramValue组成的map
        return servicesGroupParams.stream()
                      .collect(
                        Collectors.groupingBy(AdminServicesJenkinsParamDTO::getId, 
                        Collectors.toMap(AdminServicesJenkinsParamDTO::getParamName, AdminServicesJenkinsParamDTO::getParamValue))
                        );
    }


    /**
     * 基于组查询services列表
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return services列表
     */
    public List<AdminServicesListByGroupRespVO> getServicesListByGroup(Long projectId, AdminServicesListByGroupReqVO reqVO) {
        List<AdminServicesListByGroupRespVO> result = adminServiceQueryDao.selectServicesListByGroup(projectId, reqVO.getServicesGroupId());
        if(result == null || result.isEmpty()){
            return new ArrayList<>();
        }
        Set<Long> servicesIds = new HashSet<>();
        for (AdminServicesListByGroupRespVO item : result) {
            servicesIds.add(item.getServicesId());
        }

        Map<Long, Map<String,String>> servicesParamsMap = getServicesParams(servicesIds);
        for (AdminServicesListByGroupRespVO item : result) {   
            Map<String,String> servicesParams = servicesParamsMap.get(item.getServicesId());
            item.setServicesParams(servicesParams != null ? servicesParams : new HashMap<>());
        }

        log.info("基于组查询services列表成功，projectId: {}, servicesGroupId: {}, 结果数量: {}",
                    projectId, reqVO.getServicesGroupId(), result.size());
        return result;

    }

    /**
     * 查询服务最后一次任务
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return 服务最后一次任务信息
     */
    public AdminServicesLastTaskRespVO getServicesLastTask(Long projectId, AdminServicesLastTaskReqVO reqVO) {
        AdminServicesLastTaskRespVO result = adminServiceQueryDao.selectServicesLastTask(projectId, reqVO.getServicesId());
        log.info("查询服务最后一次任务成功，projectId: {}, servicesId: {}", projectId, reqVO.getServicesId());
        return result;
    }

    /**
     * 查询Jenkins任务组列表
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return Jenkins任务组列表
     */
    public List<AdminJenkinsTaskGroupListRespVO> getJenkinsTaskGroupList(Long projectId, AdminJenkinsTaskGroupListReqVO reqVO) {
        List<AdminJenkinsTaskGroupListRespVO> result = adminServiceQueryDao.selectJenkinsTaskGroupList(projectId, reqVO.getServicesGroupId());
        //action map 1 构建 2 重启 3 停止 4 获取日志 5 更新
        Map<Integer, String> actionMap = new HashMap<>();
        actionMap.put(1, "构建");
        actionMap.put(2, "重启");
        actionMap.put(3, "停止");
        actionMap.put(4, "获取日志");
        actionMap.put(5, "更新");

        if (result == null || result.isEmpty()) {
            result = new ArrayList<>();
        }
        for (AdminJenkinsTaskGroupListRespVO item : result) {
            item.setActionName(actionMap.getOrDefault(item.getAction(), "未知"));
        }
        log.info("查询Jenkins任务组列表成功，projectId: {}, servicesGroupId: {}, 结果数量: {}",
                    projectId, reqVO.getServicesGroupId(), result.size());
        return result;
    }

    /**
     * 查询Jenkins任务组日志
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return Jenkins任务组日志信息
     */
    public List<AdminJenkinsTaskGroupLogRespVO> getJenkinsTaskGroupLog(Long projectId, AdminJenkinsTaskGroupLogReqVO reqVO) {
        List<AdminJenkinsTaskLogDTO> rawResult = adminServiceQueryDao.selectJenkinsTaskGroupLog(projectId, reqVO.getJenkinsTaskGroupId());
        if (rawResult == null || rawResult.isEmpty()) {
            log.info("查询Jenkins任务组日志成功，projectId: {}, jenkinsTaskGroupId: {}, 结果数量: 0",
                        projectId, reqVO.getJenkinsTaskGroupId());
            return new ArrayList<>();
        }
        List<AdminJenkinsTaskGroupLogRespVO> result = new ArrayList<>();
        // 创建单条记录
        AdminJenkinsTaskGroupLogRespVO logRecord = new AdminJenkinsTaskGroupLogRespVO();
        logRecord.setJenkinsTaskGroupId(reqVO.getJenkinsTaskGroupId());
        // 收集所有taskId并用逗号连接
        String taskIds = rawResult.stream()
            .map(AdminJenkinsTaskLogDTO::getJenkinsTaskId)
            .filter(Objects::nonNull)
            .distinct()
            .map(String::valueOf)
            .collect(Collectors.joining(","));
        logRecord.setJenkinsTaskId(taskIds);
        
        // 收集所有日志内容
        List<String> logContents = rawResult.stream()
            .sorted(Comparator.comparing(AdminJenkinsTaskLogDTO::getLogTime))
            .map(AdminJenkinsTaskLogDTO::getLogContent)
            .filter(content -> content != null && !content.trim().isEmpty())
            .collect(Collectors.toList());
        logRecord.setLogContentList(logContents);
        
        result.add(logRecord);
        
        
        // // 按jenkinsTaskId分组，将相同taskId的日志内容合并到一个List<String>中
        // Map<Long, List<AdminJenkinsTaskLogDTO>> groupedLogs = new HashMap<>();
        // for (AdminJenkinsTaskLogDTO log : rawResult) {
        //     if (log.getJenkinsTaskId() == null) {
        //         // 如果taskId为null，直接创建一个新的日志对象并添加到结果中
        //         AdminJenkinsTaskGroupLogRespVO nullTaskLog = new AdminJenkinsTaskGroupLogRespVO();
        //         nullTaskLog.setJenkinsTaskGroupId(reqVO.getJenkinsTaskGroupId());
        //         nullTaskLog.setLogContentList(List.of(log.getLogContent()));
        //         result.add(nullTaskLog);
        //         continue;
        //     }
        //     groupedLogs.computeIfAbsent(log.getJenkinsTaskId(), k -> new ArrayList<>()).add(log);
        // }

        
        // for (Map.Entry<Long, List<AdminJenkinsTaskLogDTO>> entry : groupedLogs.entrySet()) {
        //     Long taskId = entry.getKey();
        //     List<AdminJenkinsTaskLogDTO> logs = entry.getValue();

        //     // 创建合并后的日志对象
        //     AdminJenkinsTaskGroupLogRespVO mergedLog = new AdminJenkinsTaskGroupLogRespVO();
        //     mergedLog.setJenkinsTaskGroupId(reqVO.getJenkinsTaskGroupId());
        //     mergedLog.setJenkinsTaskId(taskId);

        //     // 从原始查询结果中提取日志内容，每条记录包含一个log_content
        //     List<String> logContentList = logs.stream()
        //             .map(AdminJenkinsTaskLogDTO::getLogContent)
        //             .filter(content -> content != null && !content.trim().isEmpty())
        //             .collect(Collectors.toList());

        //     mergedLog.setLogContentList(logContentList);
        //     result.add(mergedLog);
        // }
        return result;
    }

    public List<AdminJenkinsTemplateParamRespVO> getJenkinsParamValue(AdminJenkinsTemplateParamReqVO reqVO) {
        if (reqVO.getServiceId() != null) {
            // 按服务维度查 Jenkins 模版参数
            return adminServiceQueryDao.selectServiceJenkinsValue(reqVO.getServiceId());
        } else if (reqVO.getServicesGroupId() != null) {
            // 按服务组维度查 Jenkins 模版参数
            return adminServiceQueryDao.selectServiceGroupJenkinsValue(reqVO.getServicesGroupId());
        } else {
            // 两个参数都没传，返回空列表
            return Collections.emptyList();
        }
    }
}