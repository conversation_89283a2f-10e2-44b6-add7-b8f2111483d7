package com.mega.platform.cloud.data.dto.jenkins;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CleanWorkspaceDTO extends JenkinsGroovyDTO{
    private final static String groovyScriptFormat = "def jobName = \"%s\"\n" +
            "def job = Jenkins.instance.getItemByFullName(jobName)\n" +
            "if (job != null) {\n" +
            "    job.workspace?.deleteRecursive()  // 删除本地工作空间\n" +
            "    println \"工作空间已清理: ${jobName}\"\n" +
            "} else {\n" +
            "    println \"Job 不存在: ${jobName}\"\n" +
            "}";

    private String jobName;

    public CleanWorkspaceDTO(String jobName) {
        this.jobName = jobName;
    }

    @Override
    public String getGroovyScriptStr() {
        return String.format(groovyScriptFormat, jobName);
    }
}
