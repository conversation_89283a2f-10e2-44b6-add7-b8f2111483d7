package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("ECS列表查询响应")
public class AdminEcsListRespVO {
    
    @ApiModelProperty("ECS列表")
    private List<EcsServerVO> ecsList;
    
    @Data
    @Accessors(chain = true)
    @ApiModel("ECS项目信息")
    public static class EcsServerVO {
        
        @ApiModelProperty("ECS Server ID")
        private Long id = 0L;
        
        @ApiModelProperty("名称")
        private String name = "";
        
        @ApiModelProperty("项目ID")
        private String projectId = "";
        
        @ApiModelProperty("项目名称")
        private String projectName = "";
        
        @ApiModelProperty("ECS类型(1 mysql, 2 redis, 3 services)")
        private Integer ecsType = 0;
        
        @ApiModelProperty("ECS类型名称")
        private String ecsTypeName = "";
        
        @ApiModelProperty("配置ID")
        private String configId = "";
        
        @ApiModelProperty("配置名称")
        private String configName = "";
        
        @ApiModelProperty("版本")
        private String version = "";
        
        @ApiModelProperty("公网ip地址")
        private String publicIp = "";

        @ApiModelProperty("内网IP地址")
        private String privateIp = "";
        
        @ApiModelProperty("云主机实例ID")
        private String instanceId = "";
        
        @ApiModelProperty("ECS标签选择(多选，逗号分隔)")
        private String tagList = "";
        
        @ApiModelProperty("ECS自定义标签选择(多选，逗号分隔)")
        private String customTagList = "";
        
        @ApiModelProperty("状态")
        private Integer status = 0;
        
        @ApiModelProperty("状态名称")
        private String statusName = "";
        
        @ApiModelProperty("创建的管理员ID")
        private String adminUserId = "";
        
        @ApiModelProperty("创建的管理员名称")
        private String adminUserName = "";
        
        @ApiModelProperty("备注")
        private String remark = "";
        
        @ApiModelProperty("创建时间")
        private Date createTime = new Date();
        
        @ApiModelProperty("更新时间")
        private Date updateTime = new Date();


        @ApiModelProperty("区域")
        private String regionStr = "";

        @ApiModelProperty("可用区")
        private String regionZone ="";
    }
}