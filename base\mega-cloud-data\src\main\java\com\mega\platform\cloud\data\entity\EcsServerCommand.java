package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "ecs_server_command")
public class EcsServerCommand {
    /**
     * 创建esc完成后执行脚本ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 名字
     */
    @Column(name = "name")
    private String name;

    /**
     * 立即执行，n秒后执行 这里考虑是否等待什么程序启动完成后在执行的配置？
     */
    @Column(name = "phase")
    private Integer phase;

    /**
     * 脚本类型: shell,python
     */
    @Column(name = "type")
    private String type;

    /**
     * 脚本版本
     */
    @Column(name = "version")
    private Integer version;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;

    /**
     * 脚本内容
     */
    @Column(name = "context")
    private String context;
}