package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
@Accessors(chain = true)
@ApiModel("App权限列表响应数据")
public class AdminAppPermissionListRespVO {
    
    // @ApiModelProperty(value = "Ant风格路径匹配列表")
    // private Set<String> urlPatternList;

    @ApiModelProperty(value = "Ant风格路径详情列表")
    private List<AppPermissionVO> detailList;

}
