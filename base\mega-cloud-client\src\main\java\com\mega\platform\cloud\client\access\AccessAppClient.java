package com.mega.platform.cloud.client.access;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.access.AccessAppTokenReqVO;
import com.mega.platform.cloud.data.vo.access.AccessAppTokenRespVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = "mega-cloud-access-" + "${spring.profiles.active}", contextId = "mega-cloud-access-app-client")
@RequestMapping("/access/api/app")
@Api(tags = {"应用访问接口", "/access/api/app"})
public interface AccessAppClient {
    @ApiOperation("获取登录token")
    @PostMapping("/public/token")
    Result<AccessAppTokenRespVO> accessAppToken(@Validated @RequestBody AccessAppTokenReqVO vo);
}
