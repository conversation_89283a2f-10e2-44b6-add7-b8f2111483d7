```mermaid
sequenceDiagram
participant User as 用户/管理后台
participant API as Admin Services
participant Controller as 开服控制器 Controller
participant DB as Plat DB
participant Resource as MicroService Services
participant Config as Plat DB Config Data
participant Deploy as 部署服务 Services
participant Game as 游戏Account服务
participant ECS as ECS实例
participant Cloud as Aliyun Cloud
participant DBA as 数据库管理服务
participant DBResource as 数据库资源池

    Note over User, ECS: 开服成功路径
    
    User->>API: 发起开服请求(region_id)
    API->>Controller: 开服请求
    
    Controller->>DBA: 请求数据库资源(region_id)
    alt 使用现有MySQL
        DBA->>DBResource: 分配MySQL实例
        DBA->>DBA: 创建数据库，同步数据库结构和基础数据
        DBResource-->>DBA: 返回MySQL连接信息
    else 新建MySQL实例
        DBA->>Cloud: 购买ECS，自动部署MySQL服务
        DBA->>DBA: 创建数据库，同步数据库结构和基础数据
        Cloud-->>DBA: 返回MySQL连接信息
    end
    DBA-->>Controller: 返回MySQL连接信息
    
    Controller->>DBA: 请求Redis资源(region_id)
    alt 使用现有Redis
        DBA->>DBResource: 分配Redis实例
        DBResource-->>DBA: 返回Redis连接信息
    else 新建Redis实例
        DBA->>Cloud: 购买ECS，自动部署Redis服务
        Cloud-->>DBA: 返回Redis连接信息
    end
    DBA-->>Controller: 返回Redis连接信息
    
    Controller->>Config: 读取服务组配置(region_id)
    Config-->>Controller: 返回配置
    
    Controller->>DB: 创建服务组记录(包含ECS和数据库信息)，状态=resources_allocated
    
    Controller->>Deploy: 创建服务组(ECS_ip, 配置, DB连接信息)
    Deploy->>ECS: 执行初始化脚本
    Deploy->>ECS: 创建微服务

    Controller->>Resource: 获取ECS资源(region_id)
    alt 资源池模式
        Resource-->>Controller: 返回ECS(id, internal_ip)
    else 购买模式
        Resource->>Cloud: 调用云API购买ECS
        Cloud-->>Resource: 返回ECS信息
        Resource-->>Controller: 返回ECS(id, internal_ip)
    end


    Deploy-->>Controller: 创建成功
    Controller->>DB: 更新状态=services_created
    
    Controller->>Deploy: 启动服务组
    Deploy->>ECS: 启动容器
    Deploy->>ECS: 健康检查(循环)
    ECS-->>Deploy: 服务就绪
    Deploy-->>Controller: 启动成功
    Controller->>DB: 更新状态=services_ready
    
    Controller->>Game: 调用插入区服API(服信息)
    Game-->>Controller: 插入成功
    Controller->>DB: 更新状态=success
    
    Controller-->>API: 开服成功
    API-->>User: 开服成功响应
    
    Note over User, ECS: 异常处理路径(以健康检查失败为例)
    
    Deploy->>ECS: 健康检查(循环)
    ECS-->>Deploy: 服务未就绪(超时)
    Deploy-->>Controller: 启动失败
    Controller->>DB: 更新状态=health_check_failed
    
    Controller->>Resource: 触发回滚清理
    Resource->>Deploy: 清理服务
    Deploy->>ECS: 停止并删除容器
    
    Controller->>DBA: 释放数据库资源
    DBA->>DBResource: 释放MySQL/Redis实例
    alt 新建的数据库实例
        DBA->>Cloud: 删除云数据库实例
    end
    
    Resource->>Cloud: 释放ECS(如需要)
    Resource-->>Controller: 回滚完成
    Controller->>DB: 更新状态=rollback_completed
    
    Controller-->>API: 开服失败
    API-->>User: 开服失败通知
```