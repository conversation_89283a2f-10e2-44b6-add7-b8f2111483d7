package com.mega.platform.cloud.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("阿里云ECS实例DTO")
public class AdminAliEcsInstanceDTO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("实例名称")
    private String name;

    @ApiModelProperty("ECS配置ID")
    private Long ecsConfigId;
    
    @ApiModelProperty("ECS服务器实例ID")
    private String instanceId;

    @ApiModelProperty("服务器区域id")
    private Long ecsServerRegionId;

    @ApiModelProperty("区域字符串")
    private String regionStr;

    private String regionZone;

    @ApiModelProperty("状态0.异常 1.正常 2.正在创建 3.正在初始化 4.待关闭 5.已退 6.正在升级 7.正在关闭")
    private Integer status; 

    @ApiModelProperty("前端节点 公网ip，暂时业务用不到可以不存")
    private String publicIp;

    @ApiModelProperty("前端节点 内网ip")
    private String privateIp;


    @ApiModelProperty("镜像引用")
    private String imageRef;

}
