package com.mega.platform.cloud.push.service;

import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.common.mapper.PushRecordMapper;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.entity.PushRecord;
import com.mega.platform.cloud.data.vo.push.PushDouyinMiniReqVO;
import com.mega.platform.cloud.data.vo.push.PushWeChatMiniReqVO;
import com.mega.platform.cloud.push.client.DouyinMiniPushClient;
import com.mega.platform.cloud.push.client.WeChatMiniPushClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PushService {
    private final WeChatMiniPushClient weChatMiniPushClient;
    private final DouyinMiniPushClient douyinMiniPushClient;
    private final PushRecordMapper pushRecordMapper;
    public void wechatMiniPush(PushWeChatMiniReqVO vo) {
        Boolean success = weChatMiniPushClient.pushSubscribeMessage(vo.getAppId(), vo.getToUser(), vo.getTemplateId(), vo.getPage(), vo.getData());
        PushRecord pushRecord = new PushRecord();
        pushRecord.setAppId(vo.getAppId());
        pushRecord.setPushId(1L);
        pushRecord.setDevicePushChannelId(Math.toIntExact(ThirdPlatformEnum.WECHAT.getCode()));
        pushRecord.setStatus((byte) (success?1:2));
        pushRecord.setPushCount(1);
        pushRecordMapper.insert(pushRecord);
    }

    public void douyinMiniPush(PushDouyinMiniReqVO vo) throws Exception {
        Boolean success = douyinMiniPushClient.pushSubscribeMessage(vo.getAppId(), vo.getOpenId(), vo.getTplId(),vo.getData(), vo.getPage());
        PushRecord pushRecord = new PushRecord();
        pushRecord.setAppId(vo.getAppId());
        pushRecord.setPushId(2L);
        pushRecord.setDevicePushChannelId(Math.toIntExact(ThirdPlatformEnum.DOUYIN.getCode()));
        pushRecord.setStatus((byte) (success?1:2));
        pushRecord.setPushCount(1);
        pushRecordMapper.insert(pushRecord);
    }

}
