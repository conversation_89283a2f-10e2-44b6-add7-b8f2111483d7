package com.mega.platform.cloud.push.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.client.auth.AuthVerificationClient;
import com.mega.platform.cloud.data.vo.auth.AuthMiniTokenReqVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class WeChatMiniPushClient {
    private final AuthVerificationClient authVerificationClient;
    private static final String MINI_MESSAGE_SUBSCRIBE_SEND_ACCESS_TOKEN = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s";
    private final ObjectMapper objectMapper;
    /**
     * 推送微信小程序订阅消息
     * https://api.weixin.qq.com/cgi-bin/message/subscribe/send
     */
    public Boolean pushSubscribeMessage(Long appId, String toUser, String templateId, String page, Map<String, String> data) {
        AuthMiniTokenReqVO authMiniTokenReqVO = new AuthMiniTokenReqVO();
        authMiniTokenReqVO.setAppId(appId);
        String accessToken = authVerificationClient.wechatMiniToken(authMiniTokenReqVO).getData();
        String url =  String.format(MINI_MESSAGE_SUBSCRIBE_SEND_ACCESS_TOKEN, accessToken);

        try {
            // 构造请求体
            Map<String, Object> body = new HashMap<>();
            body.put("touser", toUser);
            body.put("template_id", templateId);
            if (page != null) {
                body.put("page", page);
            }
            body.put("data", data);
            body.put("miniprogram_state", "formal");
            body.put("lang", "zh_CN");

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<String> response = new RestTemplate().postForEntity(url, entity, String.class);
            log.info("pushSubscribeMessage resp:{}", response.getBody());

            // 解析响应
            JsonNode node = objectMapper.readTree(response.getBody());
            int errCode = node.path("errcode").asInt(-1);
            return errCode == 0;
        } catch (Exception e) {
            throw new RuntimeException("调用微信订阅消息推送失败：", e);
        }
    }
}
