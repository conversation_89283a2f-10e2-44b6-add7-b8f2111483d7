package com.mega.platform.cloud.admin.service;

import com.aliyun.sdk.service.ecs20140526.AsyncClient;
import com.aliyun.sdk.service.ecs20140526.models.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.mega.platform.cloud.AdminErrorCode;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.constant.AdminServerEnum;
import com.mega.platform.cloud.admin.dao.AdminEcsDao;
import com.mega.platform.cloud.admin.dao.AdminEcsImageDao;
import com.mega.platform.cloud.admin.dto.AdminAliEcsImageDTO;
import com.mega.platform.cloud.admin.dto.AdminAliEcsInstanceDTO;
import com.mega.platform.cloud.admin.util.AdminAliEcsSdkTool;
import com.mega.platform.cloud.core.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import com.mega.platform.cloud.admin.vo.AdminEcsConfigListRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import javax.annotation.PostConstruct;

@Service
@Slf4j
public class AdminAliEcsService {

    @Value("${ali.ecs.access-key-id}")
    private String accessKeyId;

    @Value("${ali.ecs.access-key-secret}")
    private String accessKeySecret;

    private AdminAliEcsSdkTool adminAliEcsSdkTool;

    private final AdminEcsDao adminEcsDao;

    private final AdminAliEcsCheckService adminAliEcsCheckService;

    private final AdminEcsImageDao adminEcsImageDao;

    @Autowired
    public AdminAliEcsService(AdminEcsDao adminEcsDao, AdminAliEcsCheckService adminAliEcsCheckService, AdminEcsImageDao adminEcsImageDao) {
        // this.adminAliEcsSdkTool = new AdminAliEcsSdkTool(accessKeyId, accessKeySecret);
        this.adminEcsDao = adminEcsDao;
        this.adminAliEcsCheckService = adminAliEcsCheckService;
        this.adminEcsImageDao = adminEcsImageDao;
    }

    @PostConstruct
    public void init() {
        log.info("AdminAliEcsService 222222 init, accessKeyId: {}, accessKeySecret: {}", accessKeyId, accessKeySecret);
        this.adminAliEcsSdkTool = new AdminAliEcsSdkTool(accessKeyId, accessKeySecret);
    }


    /*****************************************************************  1 创建阿里云实例  *****************************************************************/

    /**
     * 创建ecs实例
     *
     * @param ecsConfig
     * @return
     */
    public String createEcs(AdminEcsConfigListRespVO.EcsConfigItemVO ecsConfig, String serverName) throws JsonProcessingException {
        log.info("create ecs: {}", ecsConfig);
        // Configure the Client
        AsyncClient client = adminAliEcsSdkTool.getClient(ecsConfig.getRegionStr());

        // 1 系统盘属性
        RunInstancesRequest.SystemDisk.Builder systemDiskBuilder = RunInstancesRequest.SystemDisk.builder();
        systemDiskBuilder.size("40");
        if (ecsConfig.getDiskCategory() == null || ecsConfig.getDiskCategory().isEmpty()) {
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), "系统盘配置 Category为空");
        }
        systemDiskBuilder.category(ecsConfig.getDiskCategory().trim());
        if (ecsConfig.getDiskCategory().equals("cloud_auto")) {
            systemDiskBuilder.provisionedIops(0L);
            systemDiskBuilder.burstingEnabled(true);
        }
        if (ecsConfig.getDiskPerformanceLevel() != null && !ecsConfig.getDiskPerformanceLevel().isEmpty()) {
            systemDiskBuilder.performanceLevel(ecsConfig.getDiskPerformanceLevel().trim());
        }
        RunInstancesRequest.SystemDisk systemDisk = systemDiskBuilder.build();

        // 2 通用请求属性
        RunInstancesRequest.Builder runInstancesRequestBuilder = RunInstancesRequest.builder()
                .regionId(ecsConfig.getRegionStr().trim()) // example: "cn-hangzhou"
                .zoneId(ecsConfig.getRegionZone().trim()) // 可用区ID
                .instanceType(ecsConfig.getFlavorRef().trim()) // 实例的资源规格 example: ecs.g6.large
                .cpuOptions(RunInstancesRequest.CpuOptions.builder()
                        .core(1)
                        .threadsPerCore(2)
                        .build())
                .ioOptimized("optimized") // 是否开启I/O优化
                .imageId(ecsConfig.getImageRef()) // 镜像ID
                .systemDisk(systemDisk)
                .creditSpecification(ecsConfig.getCreditSpecification().trim()) // 突发性能实例
                .internetChargeType(ecsConfig.getInternetChargeType().trim()) // 默认值：PayByTraffic。网络计费类型。PayByBandwidth：按固定带宽计费。PayByTraffic：按使用流量计费。
                .internetMaxBandwidthOut(Math.max(ecsConfig.getBandwidth(), 1)) // 🔧 修复：确保带宽至少为1Mbps，避免为0导致实例异常
                .instanceName(ecsConfig.getName()) // 实例名称 example：k8s-node-[1,4]-alibabacloud
                .vSwitchId(ecsConfig.getSubnetId().trim()) // 虚拟交换机ID
                .securityGroupIds(Arrays.asList(ecsConfig.getSecurityGroupId().split(","))) // 安全组ID
                .passwordInherit(true) // 是否使用镜像默认密码
                .instanceName(serverName)   // 实例名称
                .description(ecsConfig.getRemark()) // 描述
                ;

        // 2.1 支付方式
        String instanceChargeType = ecsConfig.getChargingMode();
        if (instanceChargeType == null || instanceChargeType.isEmpty()) {
            instanceChargeType = "PostPaid";
        }
        if (instanceChargeType.equals("PrePaid")) {
            runInstancesRequestBuilder.period(ecsConfig.getPeriodType().equals("Month") ? 1 : 12); // 购买实例的时长。取值范围：1-9。单位：月。当InstanceChargeType取值为PrePaid时有效，默认值：1。
        }
        runInstancesRequestBuilder.instanceChargeType(instanceChargeType);

        // 2.2 突发性能实例
        if (ecsConfig.getCreditSpecification() != null && !ecsConfig.getCreditSpecification().isEmpty()) {
            runInstancesRequestBuilder.creditSpecification(ecsConfig.getCreditSpecification().trim());
        }

        // 2.3 数据盘属性 存在时需要初始化
        String extraDataDisk = ecsConfig.getExtraDataDisk();
        List<RunInstancesRequest.DataDisk> dataDisks = getDataDiskList(extraDataDisk);
        if (dataDisks != null && !dataDisks.isEmpty()) {
            runInstancesRequestBuilder.dataDisk(dataDisks);
        }
        // 3 构建请求
        RunInstancesRequest runInstancesRequest = runInstancesRequestBuilder.build();
        try {
            log.info("-----阿里云 create req->: {}", JsonUtils.toJson(runInstancesRequest));
            log.info("--------阿里云 create start! ecs configId:{} name:{}, flavorRef:{}-----------", ecsConfig.getId(), serverName, ecsConfig.getFlavorRef());
            CompletableFuture<RunInstancesResponse> response = client.runInstances(runInstancesRequest);
            RunInstancesResponse resp = response.get();
            if (resp.getStatusCode() != 200) {
                throw new Exception("http status code: " + resp.getStatusCode());
            }
            String instanceId = resp.getBody().getInstanceIdSets().getInstanceIdSet().get(0);
            log.info("--------阿里云 create success! configId:{}, instanceId {}-----------", ecsConfig.getId(), instanceId);
            return instanceId;
        } catch (AdminException e) {
            throw e;
        } catch (Exception e) {
            String errorMsg = String.format("configId: %s, name: %s, flavorRef: %s, error: %s-----------", ecsConfig.getId(), serverName, ecsConfig.getFlavorRef(), e.getMessage());
            log.error("-----------阿里云 create error! {}-----------", errorMsg);
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), errorMsg);
        } finally {
            client.close();
        }
    }

    /**
     * 生成数据盘列表
     *
     * @param extraDataDisk
     * @return
     */
    private List<RunInstancesRequest.DataDisk> getDataDiskList(String configExtraDataDisk, String imageExtraDataDisk) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode baseDataDiskJson = null;
            JsonNode overrideDataDiskJson = null;

            // 解析基础镜像数据盘配置
            if (imageExtraDataDisk != null && !imageExtraDataDisk.isEmpty()) {
                overrideDataDiskJson = objectMapper.readTree(imageExtraDataDisk);
            }

            // 解析覆盖的数据盘配置
            if (configExtraDataDisk != null && !configExtraDataDisk.isEmpty()) {
                baseDataDiskJson = objectMapper.readTree(configExtraDataDisk);
            }

            // 如果两个配置都为空，返回null
            if (baseDataDiskJson == null && overrideDataDiskJson == null) {
                return null;
            }

            List<RunInstancesRequest.DataDisk> dataDisks = new ArrayList<>();
            RunInstancesRequest.DataDisk.Builder dataDiskBuilder = RunInstancesRequest.DataDisk.builder();

            // 设置数据盘属性，优先使用覆盖配置
            // 合并两个JSON配置，以overrideDataDiskJson为主
            ObjectMapper mapper = new ObjectMapper();
            JsonNode finalJson;
            if (baseDataDiskJson == null) {
                finalJson = overrideDataDiskJson;
            } else if (overrideDataDiskJson == null) {
                finalJson = baseDataDiskJson;
            } else {
                // 创建一个新的ObjectNode用于合并
                ObjectNode mergedJson = mapper.createObjectNode();
                // 先复制baseDataDiskJson的所有字段
                baseDataDiskJson.fields().forEachRemaining(entry -> 
                    mergedJson.set(entry.getKey(), entry.getValue())
                );
                // 用overrideDataDiskJson的字段覆盖或添加
                overrideDataDiskJson.fields().forEachRemaining(entry -> 
                    mergedJson.set(entry.getKey(), entry.getValue())
                );
                finalJson = mergedJson;
            }

            // 设置数据盘大小
            dataDiskBuilder.size(finalJson.has("size") ? finalJson.get("size").asInt(20) : 20);

            // 设置数据盘类型
            if (finalJson.has("category")) {
                dataDiskBuilder.category(finalJson.get("category").asText().trim());
            }

            // 设置性能等级
            if (finalJson.has("performanceLevel")) {
                dataDiskBuilder.performanceLevel(finalJson.get("performanceLevel").asText().trim());
            }

            // 设置数据盘名称
            if (finalJson.has("diskName")) {
                dataDiskBuilder.diskName(finalJson.get("diskName").asText().trim());
            }

            // 数据盘挂载点
            if (finalJson.has("device")) {
                dataDiskBuilder.device(finalJson.get("device").asText().trim());
            }

            // 数据盘镜像
            if (finalJson.has("snapshotId")) {
                dataDiskBuilder.snapshotId(finalJson.get("snapshotId").asText().trim());
            }

            dataDiskBuilder.deleteWithInstance(true);
            dataDiskBuilder.burstingEnabled(true);

            dataDisks.add(dataDiskBuilder.build());
            return dataDisks;
        } catch (AdminException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to parse disk JSON. extraDataDisk: {}, imageExtraDataDisk: {}, error: {}", 
                extraDataDisk, imageExtraDataDisk, e.getMessage());
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "Invalid disk configuration format");
        }
    }


    /***
     * 检测阿里云ecs实例状态
     */
    public void checkEcsStatus() {
        // List<AdminAliEcsInstanceDTO> servers = adminEcsDao.getAliActiveEcsServer();
        List<AdminAliEcsInstanceDTO> servers = adminEcsDao.getAliEcsServerByStatus(2);
        if (servers == null || servers.isEmpty()) {
            log.info("--------没有需要检测的阿里云ecs(status=1,2,7)实例-----------");
            return;
        }

        // 状态为2的阿里云ecs实例，key为regionStr, value为AdminAliEcsInstanceDTO
        Map<String, List<AdminAliEcsInstanceDTO>> createRegionMap = new HashMap<>();
        // 其他状态的阿里云ecs实例，key为regionStr, value为AdminAliEcsInstanceDTO
        Map<String, List<AdminAliEcsInstanceDTO>> otherRegionMap = new HashMap<>();

        // 获取阿里云ecs实例
        for (AdminAliEcsInstanceDTO server : servers) {
            if (server.getInstanceId() == null) {
                // ecs实例id为空，不检测
                log.error("--------阿里云ecs router_server_id: {}  ecs_server_id is null -----------", server.getId());
                continue;
            }
            if (server.getStatus().equals(AdminServerEnum.ServerStatus.CREATING.getCode()) || server.getStatus().equals(AdminServerEnum.ServerStatus.UPGRADING.getCode())) {
                createRegionMap.computeIfAbsent(server.getRegionStr(), k -> new ArrayList<>()).add(server);
            } else {
                otherRegionMap.computeIfAbsent(server.getRegionStr(), k -> new ArrayList<>()).add(server);
            }
        }

        // 检测创建状态
        for (Map.Entry<String, List<AdminAliEcsInstanceDTO>> entry : createRegionMap.entrySet()) {
            adminAliEcsCheckService.checkEcsCreateStatus(entry.getValue(), entry.getKey());
        }

        // 检测实时状态
//        for (Map.Entry<String, List<AdminAliEcsInstanceDTO>> entry : otherRegionMap.entrySet()) {
//            adminAliEcsCheckService.checkEcsRealTimeStatus(entry.getValue(), entry.getKey());
//        }

    }

    /**
     * 定时任务，检测待删除的ecs，如果连接数为0，则删除
     */
    public void scheduleDealyDeleteServers() {
        // 1 获得待关闭的ecs实例
        List<AdminAliEcsInstanceDTO> servers = adminEcsDao.getAliEcsServerByStatus(
                AdminServerEnum.ServerStatus.PENDING_SHUTDOWN.getCode());
        if (servers == null || servers.isEmpty()) {
            return;
        }

        // 3 删除服务器
        for (AdminAliEcsInstanceDTO server : servers) {
            this.safeDeleteInstance(server);
        }
    }

    private boolean safeDeleteInstance(AdminAliEcsInstanceDTO server) {
        try {
            // 1 查询实例状态
            // ["i-bp67acfmxazb4p****", "i-bp67acfmxazb4p****", … "i-bp67acfmxazb4p****"] 这样的形式
            String instanceIds = "[\"" + server.getInstanceId() + "\"]";
            List<DescribeInstancesResponseBody.Instance> instances = adminAliEcsSdkTool.fetchDescribeInstance(instanceIds, server.getRegionStr());
            if (instances == null || instances.isEmpty()) {
                return false;
            }
            //打印实例运行状态和付费类型
            DescribeInstancesResponseBody.Instance instanceInfo = instances.get(0);
            String chargeType = instanceInfo.getInstanceChargeType();
            String status = instanceInfo.getStatus();
            log.info("--------阿里云ecs ecs_server_id: {}, instanceId: {}, status: {}, instanceChargeType: {}-----------",
                    server.getId(),
                    instanceInfo.getInstanceId(),
                    status,
                    chargeType);

            // 2 未停止，先停止
            if (!status.equals("Stopped")) {
                adminAliEcsSdkTool.fetchStopInstance(server.getInstanceId(), server.getRegionStr());
                return false;
            }

            // 3 PrePaid支付转为PostPaid 包年包月转按量付费
            if (!chargeType.equals("PostPaid")) {
                adminAliEcsSdkTool.fetchModifyChargeType(instanceIds, server.getRegionStr(), "PostPaid");
                return false;
            }

            // 4 删除实例
            List<String> instanceIdList = new ArrayList<>();
            instanceIdList.add(server.getInstanceId());
            adminAliEcsSdkTool.fetchDeleteInstances(instanceIdList, server.getRegionStr());
            // 5 更新状态
            adminEcsDao.updateEcsStatusById(server.getId(), AdminServerEnum.ServerStatus.EXITED.getCode());
            return true;
        } catch (AdminException e) {
            throw e;
        } catch (Exception e) {
            log.error("--------阿里云ecs ecs_server_id: {} 删除失败 e:{}-----------", server.getId(), e.getMessage());
            return false;
        }
    }

    /***************************************************************** 2 付费转换 *****************************************************************/

    /**
     * ECS按需转按月
     *
     * @param instanceId 实例ID
     */
    public void changeToPeriod(String instanceId, String regionStr) {
        log.info("ECS按需转按月，instanceId: {}", instanceId);

        try {
            if (instanceId == null || instanceId.isEmpty()) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "实例ID不能为空");
            }
            String instanceIds = "[\"" + instanceId + "\"]";
            List<DescribeInstancesResponseBody.Instance> instances = adminAliEcsSdkTool.fetchDescribeInstance(instanceIds, regionStr);
            if (instances == null || instances.isEmpty()) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "实例不存在：" + instanceId);
            }
            // 1. 获取实例信息确定当前付费类型
            String chargeType = instances.get(0).getInstanceChargeType();

            // 2. 检查当前付费类型是否为PostPaid
            if (!chargeType.equals("PostPaid")) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "当前付费类型不是按量付费");
            }

            // 3. 调用阿里云API转换付费模式
            adminAliEcsSdkTool.fetchModifyChargeType(instanceIds, regionStr, "PrePaid");
            log.info("ECS按需转按月成功，instanceId: {}", instanceId);
        } catch (AdminException e) {
            throw e;
        } catch (Exception e) {
            log.error("ECS按需转按月失败，instanceId: {}, error: {}", instanceId, e.getMessage(), e);
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), "ECS按需转按月失败: " + e.getMessage());
        }
    }

    /**
     * ECS按月转自动续费
     *
     * @param instanceId 实例ID
     */
    public void changeToAutoRenew(String instanceId, String regionStr) {
        log.info("ECS按月转自动续费，instanceId: {}", instanceId);
        try {
            if (instanceId == null || instanceId.isEmpty()) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "实例ID不能为空");
            }

            String instanceIds = "[\"" + instanceId + "\"]";
            List<DescribeInstancesResponseBody.Instance> instances = adminAliEcsSdkTool.fetchDescribeInstance(instanceIds, regionStr);
            if (instances == null || instances.isEmpty()) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "实例不存在：" + instanceId);
            }
            // 1. 获取实例信息确定当前付费类型
            String chargeType = instances.get(0).getInstanceChargeType();

            // 2. 检查当前付费类型是否为周期付费
            if (!chargeType.equals("PrePaid")) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "当前付费类型不是周期付费");
            }

            // 3. 调用阿里云API开启自动续费
            adminAliEcsSdkTool.fetchModifyAutoRenewAttribute(instanceId, regionStr);
            log.info("ECS按月转自动续费成功，instanceId: {}", instanceId);

        } catch (AdminException e) {
            throw e;
        } catch (Exception e) {
            log.error("ECS按月转自动续费失败，instanceId: {}, error: {}", instanceId, e.getMessage(), e);
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), "ECS按月转自动续费失败: " + e.getMessage());
        }
    }

    /***************************************************************** 3 创建阿里云镜像 *****************************************************************/

    /**
     * 创建镜像
     *
     * @param server    实例信息
     * @param imageName 镜像名称
     * @return 镜像ID
     */
    public String createImage(AdminAliEcsInstanceDTO server, String imageName) {
        return adminAliEcsSdkTool.fetchCreateImage(server, imageName);
    }

    /**
     * 获取实例信息
     *
     * @param instanceId
     * @param regionStr
     * @return
     */
    public DescribeInstancesResponseBody.Instance getInstanceInfo(String instanceId, String regionStr) {
        String instanceIds = "[\"" + instanceId + "\"]";
        List<DescribeInstancesResponseBody.Instance> instances = adminAliEcsSdkTool.fetchDescribeInstance(instanceIds, regionStr);
        if (instances == null || instances.isEmpty()) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "实例不存在：" + instanceId);
        }
        DescribeInstancesResponseBody.Instance instance = instances.get(0);
        return instance;
    }

    /**
     * 检查创建中的镜像
     */
    public void checkCreatingImage() {
        // 1 查询处于信件中的镜像
        List<AdminAliEcsImageDTO> creatingImages = adminEcsImageDao.getAliCreatingImageList(2);
        if (creatingImages == null || creatingImages.isEmpty()) {
            return;
        }
        for (AdminAliEcsImageDTO image : creatingImages) {
            // 2 检查镜像是否存在
            try {
                DescribeImagesResponseBody imageBody = adminAliEcsSdkTool.fetchImageDetail(image.getRef(), image.getRegionStr());
                if (imageBody.getTotalCount() < 1) {
                    continue;
                }
                // 3 镜像存在，更新状态
                String dataDiskInfo = getDataDiskInfo(imageBody);
                adminEcsImageDao.updateEcsImageStatus(image.getId(), 1, dataDiskInfo);
            } catch (Exception e) {
                log.error("检查镜像是否存在失败，imageId: {}, error: {}", image.getId(), e.getMessage(), e);
            }
        }
    }

    private String getDataDiskInfo(DescribeImagesResponseBody imageBody) {
        if (imageBody.getImages() == null || imageBody.getImages().getImage().isEmpty()) {
            return "";
        }
        Map map = new HashMap();
        for (DescribeImagesResponseBody.Image image : imageBody.getImages().getImage()) {
            System.out.println("=== 镜像信息 ===");
            System.out.println("镜像ID: " + image.getImageId());
            System.out.println("镜像名称: " + image.getImageName());
            List<DescribeImagesResponseBody.DiskDeviceMapping> diskDeviceMappings = image.getDiskDeviceMappings().getDiskDeviceMapping();
            for (DescribeImagesResponseBody.DiskDeviceMapping diskDeviceMapping : diskDeviceMappings) {
                System.out.println("系统盘类型: " + diskDeviceMapping.getType());
                if("system".equals(diskDeviceMapping.getType())){
                    continue;
                }
                // {"size": 500, "device": "/dev/xvdb", "category": "cloud_auto", "snapshotId": "s-bp1ewag6nesce62x7gr4", "performanceLevel": ""}
                map.put("size", diskDeviceMapping.getSize());
                map.put("device", diskDeviceMapping.getDevice());
                map.put("snapshotId", diskDeviceMapping.getSnapshotId());
                log.info("数据盘信息: {}", map);
                try {
                    return JsonUtils.toJson(map);
                } catch (JsonProcessingException e) {
                    log.error("获取数据盘信息失败，error: {}", e.getMessage(), e);
                    return "";
                }
            }
        }
        return "";
    }

    /**
     * 删除镜像
     *
     * @param imageRef  镜像ID
     * @param regionStr 区域
     */
    public void deleteImage(String imageRef, String regionStr) {
        adminAliEcsSdkTool.fetchDeleteImage(imageRef, regionStr);
    }
}
