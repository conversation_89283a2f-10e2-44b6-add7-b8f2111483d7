package com.mega.platform.cloud.admin.service;

import com.aliyun.sdk.service.ecs20140526.models.DescribeInstancesResponseBody;
import com.mega.platform.cloud.AdminErrorCode;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.dao.AdminEcsDao;
import com.mega.platform.cloud.admin.dao.AdminEcsImageDao;
import com.mega.platform.cloud.admin.dto.AdminAliEcsImageDTO;
import com.mega.platform.cloud.admin.dto.AdminAliEcsInstanceDTO;
import com.mega.platform.cloud.admin.vo.AdminEcsImageCreateReqVO;
import com.mega.platform.cloud.admin.vo.AdminEcsImageDeleteReqVO;
import com.mega.platform.cloud.admin.vo.AdminEcsImageListReqVO;
import com.mega.platform.cloud.admin.vo.AdminEcsImageListRespVO;
import com.mega.platform.cloud.common.mapper.EcsServerImageMapper;
import com.mega.platform.cloud.common.utils.StringUtils;
import com.mega.platform.cloud.data.entity.EcsServerImage;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.Admin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * ECS镜像管理服务
 */
@Service
@Slf4j
public class AdminEcsImageService {

    private final AdminEcsImageDao adminEcsImageDao;
    private final EcsServerImageMapper ecsServerImageMapper;
    private final AdminEcsDao adminEcsDao;
    private final AdminAliEcsService adminAliEcsService;

    @Autowired
    public AdminEcsImageService(AdminEcsImageDao adminEcsImageDao, EcsServerImageMapper ecsServerImageMapper, AdminEcsDao adminEcsDao, AdminAliEcsService adminAliEcsService) {
        this.adminEcsImageDao = adminEcsImageDao;
        this.ecsServerImageMapper = ecsServerImageMapper;
        this.adminEcsDao = adminEcsDao;
        this.adminAliEcsService = adminAliEcsService;
    }

    /**
     * 查询ECS镜像列表
     * @param reqVO     查询条件
     * @return ECS镜像列表
     */
    public AdminEcsImageListRespVO getEcsImageList(AdminEcsImageListReqVO reqVO) {
        log.info("查询ECS镜像列表，projectId: {}, reqVO: {}", reqVO.getProjectId(), reqVO);

        // 查询镜像列表
        List<AdminEcsImageListRespVO.EcsServerImageVO> imageList = adminEcsImageDao.selectEcsImageListByCondition(reqVO);

        AdminEcsImageListRespVO respVO = new AdminEcsImageListRespVO();
        respVO.setEcsList(imageList);

        log.info("查询到{}个ECS镜像", imageList.size());
        return respVO;
    }

    /**
     * 创建ECS镜像
     *
     * @param reqVO       创建参数
     * @param adminUserId 管理员ID
     */
    @Transactional
    public void createEcsImage(AdminEcsImageCreateReqVO reqVO, Long adminUserId) {
        log.info("创建ECS镜像，reqVO: {}, adminUserId: {}", reqVO, adminUserId);
        //查询ECS服务器是否存在
        AdminAliEcsInstanceDTO ecsServer = adminEcsDao.getAliEcsServerById(reqVO.getFromEcsServerId());

        if (ecsServer == null) {
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), "ECS服务器不存在");
        }
        if(StringUtils.isEmpty(ecsServer.getInstanceId())){
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), "ECS服务器实例ID不能为空");
        }

        // 新建image
        String imageName = reqVO.getName().trim() +"-v" +reqVO.getVersion();
        log.info("创建ECS镜像，imageName: {}", imageName);
        String imageId = adminAliEcsService.createImage(ecsServer, imageName);

        // 创建镜像
        EcsServerImage ecsServerImage = new EcsServerImage();
        ecsServerImage.setName(reqVO.getName());
        ecsServerImage.setProjectId(reqVO.getProjectId());
        // ecsServerImage.setEcsType(reqVO.getEcsType());
        ecsServerImage.setVersion(reqVO.getVersion().toString());
        ecsServerImage.setStatus(2); // 状态：2-创建中
        ecsServerImage.setRef(imageId);
        ecsServerImage.setAdminUserId(adminUserId);
        ecsServerImage.setRemark(reqVO.getRemark());
        ecsServerImage.setFromEcsServerId(reqVO.getFromEcsServerId());
        ecsServerImage.setName(imageName);
        ecsServerImage.setEscServerRegionId(ecsServer.getEcsServerRegionId());
        ecsServerImage.setDelsign((byte) 0);
        ecsServerImageMapper.insertSelective(ecsServerImage);
        log.info("ECS镜像创建成功 id:"+ecsServerImage.getId() +"image ref:"+imageId);
    }

    /**
     * 删除ECS镜像
     *
     * @param reqVO       删除参数
     * @param adminUserId 管理员ID
     */
    @Transactional
    public void deleteEcsImage(AdminEcsImageDeleteReqVO reqVO, Long adminUserId) {
        log.info("删除ECS镜像，reqVO: {}, adminUserId: {}", reqVO, adminUserId);
        // 1 查询镜像是否存在
        AdminAliEcsImageDTO ecsServerImage = adminEcsImageDao.getAliImageById(reqVO.getImageId());
        if (ecsServerImage == null) {
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), "ECS镜像不存在");
        }

        // 2 检查镜像状态
        if (ecsServerImage.getStatus() == 2) {
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), "ECS镜像新建中状态，不能删除");
        }

        // 3 删除镜像
        String imageRef = ecsServerImage.getRef();
        String regionStr = ecsServerImage.getRegionStr();
        adminAliEcsService.deleteImage( imageRef, regionStr);

        // 4 删除镜像
        adminEcsImageDao.deleteEcsImage(reqVO, adminUserId);

        log.info("ECS镜像删除成功");
    }
}
