<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminEcsConfigDao">

    <!-- 根据条件查询ECS配置列表 -->
    <select id="selectEcsConfigListByCondition" resultType="com.mega.platform.cloud.admin.vo.AdminEcsConfigListRespVO$EcsConfigItemVO">
       SELECT
            esc.*,
			esr.`name` as regionStr,
			esr.zone as regionZone,
			esi.ref  as imageRef,
            esi.extra_data_disk as imageExtraDataDisk
        FROM ecs_server_config esc
		LEFT JOIN ecs_server_region AS esr ON esc.ecs_server_region_id  = esr.id
		LEFT JOIN ecs_server_image  AS esi ON esc.ecs_server_image_id   = esi.id
        <where>
            esc.delsign = 0
            <if test="req.projectId != null and req.projectId != ''">
                AND esc.project_id = #{req.projectId}
            </if>
            <if test="req.name != null and req.name != ''">
                AND esc.name LIKE CONCAT('%', #{req.name}, '%')
            </if>
            <if test="req.ecsType != null">
                AND esc.ecs_type = #{req.ecsType}
            </if>
            <if test="req.ecsConfigId != null">
                AND esc.id = #{req.ecsConfigId}
            </if>
        </where>
        ORDER BY esc.create_time DESC
    </select>

    <!-- 根据条件统计ECS配置数量 -->
    <select id="countEcsConfigListByCondition" resultType="int">
        SELECT COUNT(*)
        FROM ecs_server_config esc
        <where>
            esc.delsign = 0
            <if test="req.projectId != null and req.projectId != ''">
                AND esc.project_id = #{req.projectId}
            </if>
            <if test="req.name != null and req.name != ''">
                AND esc.name LIKE CONCAT('%', #{req.name}, '%')
            </if>
            <if test="req.ecsType != null">
                AND esc.ecs_type = #{req.ecsType}
            </if>
        </where>
    </select>

</mapper>