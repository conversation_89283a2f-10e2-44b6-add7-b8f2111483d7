# 后台管理-开服任务调度ECS其他相关文档

## 2. 配置管理策略

### 2.1 环境配置

#### 开发环境配置
```yaml
# application-dev.yml
spring:
  profiles: dev

# 数据库配置
spring:
  datasource:
    url: ******************************************
    username: dev_user
    password: dev_password

# 阿里云配置（使用测试账号）
aliyun:
  ecs:
    access-key-id: ${DEV_ALIYUN_ACCESS_KEY_ID}
    access-key-secret: ${DEV_ALIYUN_ACCESS_KEY_SECRET}
    region-id: cn-hangzhou

# 开服任务配置
game:
  launch:
    concurrency:
      global-max: 2  # 开发环境限制并发
      project-max: 1
    timeout:
      mysql: 600     # 开发环境缩短超时时间
      redis: 900
      microservice: 600
```

#### 测试环境配置
```yaml
# application-test.yml
spring:
  profiles: test

# 数据库配置
spring:
  datasource:
    url: ********************************************
    username: test_user
    password: ${TEST_DB_PASSWORD}

# 阿里云配置
aliyun:
  ecs:
    access-key-id: ${TEST_ALIYUN_ACCESS_KEY_ID}
    access-key-secret: ${TEST_ALIYUN_ACCESS_KEY_SECRET}
    region-id: cn-shanghai

# 测试环境特殊配置
game:
  launch:
    auto-cleanup: true  # 自动清理测试资源
    test-mode: true     # 测试模式标识
```

#### 生产环境配置
```yaml
# application-prod.yml
spring:
  profiles: prod

# 数据库配置
spring:
  datasource:
    url: ****************************************************
    username: ${PROD_DB_USERNAME}
    password: ${PROD_DB_PASSWORD}
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10

# 阿里云配置
aliyun:
  ecs:
    access-key-id: ${PROD_ALIYUN_ACCESS_KEY_ID}
    access-key-secret: ${PROD_ALIYUN_ACCESS_KEY_SECRET}
    region-id: cn-beijing

# 生产环境优化配置
game:
  launch:
    concurrency:
      global-max: 20
      project-max: 5
    enable-approval: true  # 生产环境启用审批
    enable-notification: true  # 启用通知
```

### 2.2 敏感信息管理

#### 密钥管理策略
```yaml
# 密钥轮换配置
security:
  key-rotation:
    enabled: true
    interval: 30d  # 30天轮换一次
    
  encryption:
    algorithm: AES-256-GCM
    key-derivation: PBKDF2
    
  secrets:
    # 数据库密码
    db-password-rotation: 90d
    
    # API密钥
    api-key-rotation: 60d
    
    # 内部服务通信密钥
    service-key-rotation: 30d
```

#### 环境变量管理
```bash
# 生产环境变量示例
export PROD_DB_USERNAME="game_prod_user"
export PROD_DB_PASSWORD="$(vault kv get -field=password secret/db/prod)"
export PROD_ALIYUN_ACCESS_KEY_ID="$(vault kv get -field=access_key_id secret/aliyun/prod)"
export PROD_ALIYUN_ACCESS_KEY_SECRET="$(vault kv get -field=access_key_secret secret/aliyun/prod)"
export APP_ENCRYPTION_KEY="$(vault kv get -field=encryption_key secret/app/prod)"
```

## 3. 部署策略

### 3.1 容器化部署

#### Docker构建脚本
```dockerfile
# 多阶段构建优化
FROM maven:3.8.4-openjdk-11-slim AS builder

WORKDIR /app
COPY pom.xml .
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests -Dmaven.repo.local=/tmp/.m2

# 生产运行镜像
FROM openjdk:11-jre-slim

# 添加应用用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

WORKDIR /app

# 安装运行时依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends curl netcat-openbsd && \
    rm -rf /var/lib/apt/lists/*

# 复制应用文件
COPY --from=builder /app/target/*.jar app.jar
COPY docker/entrypoint.sh /entrypoint.sh

# 设置权限
RUN chmod +x /entrypoint.sh && \
    chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

EXPOSE 8080

ENTRYPOINT ["/entrypoint.sh"]
```

#### Kubernetes部署配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: game-launch-service
  namespace: mega-cloud
spec:
  replicas: 3
  selector:
    matchLabels:
      app: game-launch-service
  template:
    metadata:
      labels:
        app: game-launch-service
    spec:
      containers:
      - name: app
        image: mega-cloud/game-launch-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: PROD_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: game-launch-service
  namespace: mega-cloud
spec:
  selector:
    app: game-launch-service
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
```

### 3.2 灰度发布策略

#### 蓝绿部署配置
```yaml
# 蓝绿部署脚本
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: game-launch-service
spec:
  replicas: 5
  strategy:
    blueGreen:
      activeService: game-launch-service-active
      previewService: game-launch-service-preview
      autoPromotionEnabled: false
      scaleDownDelaySeconds: 30
      prePromotionAnalysis:
        templates:
        - templateName: success-rate
        args:
        - name: service-name
          value: game-launch-service-preview.mega-cloud.svc.cluster.local
      postPromotionAnalysis:
        templates:
        - templateName: success-rate
        args:
        - name: service-name
          value: game-launch-service-active.mega-cloud.svc.cluster.local
```

## 4. 运维和监控

### 4.1 监控指标定义

#### 业务指标
```yaml
# Prometheus监控规则
groups:
- name: game-launch-business
  rules:
  # 任务成功率
  - alert: TaskSuccessRateLow
    expr: |
      (
        rate(game_launch_task_success_total[5m]) /
        rate(game_launch_task_created_total[5m])
      ) < 0.9
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "游戏开服任务成功率过低"
      description: "最近5分钟任务成功率为 {{ $value | humanizePercentage }}"

  # 任务执行时间过长
  - alert: TaskExecutionTimeHigh
    expr: |
      histogram_quantile(0.95, rate(game_launch_task_execution_time_bucket[5m])) > 1800
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "游戏开服任务执行时间过长"
      description: "95%分位数执行时间超过30分钟"

  # 并发任务数过多
  - alert: ConcurrentTasksHigh
    expr: game_launch_concurrent_tasks > 15
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "并发任务数过多"
      description: "当前并发任务数: {{ $value }}"
```

#### 系统指标
```yaml
# 系统资源监控
- name: game-launch-system
  rules:
  # 内存使用率
  - alert: HighMemoryUsage
    expr: |
      (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.8
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "内存使用率过高"
      description: "内存使用率: {{ $value | humanizePercentage }}"

  # CPU使用率
  - alert: HighCPUUsage
    expr: |
      100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "CPU使用率过高"
      description: "CPU使用率: {{ $value }}%"
```

### 4.2 日志管理策略

#### 日志分级配置
```yaml
# logback-spring.xml
<configuration>
    <springProfile name="dev">
        <logger name="com.mega.platform.cloud" level="DEBUG"/>
        <logger name="org.springframework" level="INFO"/>
    </springProfile>
    
    <springProfile name="test">
        <logger name="com.mega.platform.cloud" level="INFO"/>
        <logger name="org.springframework" level="WARN"/>
    </springProfile>
    
    <springProfile name="prod">
        <logger name="com.mega.platform.cloud" level="INFO"/>
        <logger name="org.springframework" level="WARN"/>
        <logger name="org.hibernate" level="WARN"/>
    </springProfile>
    
    <!-- 任务执行日志单独配置 -->
    <logger name="com.mega.platform.cloud.task" level="INFO" additivity="false">
        <appender-ref ref="TASK_FILE"/>
    </logger>
    
    <!-- 错误日志单独配置 -->
    <logger name="ERROR" level="ERROR" additivity="false">
        <appender-ref ref="ERROR_FILE"/>
    </logger>
</configuration>
```

#### 日志收集配置
```yaml
# Fluent Bit配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush         1
        Log_Level     info
        Daemon        off
        Parsers_File  parsers.conf

    [INPUT]
        Name              tail
        Path              /var/log/containers/*game-launch*.log
        Parser            docker
        Tag               game.launch.*
        Refresh_Interval  5

    [FILTER]
        Name                kubernetes
        Match               game.launch.*
        Kube_URL            https://kubernetes.default.svc:443
        Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token

    [OUTPUT]
        Name  es
        Match game.launch.*
        Host  elasticsearch.logging.svc.cluster.local
        Port  9200
        Index game-launch-logs
```


## 6. 待确认的业务细节

基于前面章节的技术实现，以下业务细节仍需要您的确认：

### 6.1 资源管理策略
1. **MySQL资源复用阈值**：当前建议CPU使用率70%，是否合适？
2. **Redis内存使用阈值**：当前建议内存使用率80%，是否需要调整？
3. **失败资源清理延时**：当前设置1小时，是否合适排查问题？

### 6.2 任务管理策略
4. **全局并发限制**：建议最大10个，是否需要根据系统资源动态调整？
5. **项目级并发限制**：建议最大3个，是否满足业务需求？
6. **任务超时时间**：MySQL 20分钟、Redis 30分钟、微服务25分钟是否合理？

### 6.3 通知和审批
7. **通知方式优先级**：建议邮件+飞书，是否需要短信通知？
8. **生产环境审批**：是否需要多级审批？审批超时如何处理？

### 6.4 监控和告警
9. **告警通知对象**：任务失败是否需要通知项目负责人和运维团队？
10. **关键指标阈值**：任务成功率90%、执行时间30分钟是否合适？

