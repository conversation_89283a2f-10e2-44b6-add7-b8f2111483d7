package com.mega.platform.cloud.client.auth;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.auth.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "mega-cloud-auth-" + "${spring.profiles.active}", contextId = "mega-cloud-auth-verification-client")
@Api(tags = {"验证码接口", "/auth/api/verification"})
public interface AuthVerificationClient {
    @ApiOperation("获取手机验证码")
    @PostMapping("/auth/api/verification/sms/code")
    public Result<AuthSendSmsCodeRespVO> sendSmsCode(@Validated @RequestBody AuthSendSmsCodeReqVO vo) throws Exception;

    @ApiOperation("验证手机验证码")
    @PostMapping("/auth/api/verification/sms/verify")
    public Result<Boolean> verifySmsCode(@Validated @RequestBody AuthVerifySmsCodeReqVO vo);

    @ApiOperation("验证苹果")
    @PostMapping("/auth/api/verification/apple/verify")
    public Result<AuthVerifyAppleTokenRespVO> verifyAppleToken(@Validated @RequestBody AuthVerifyAppleTokenReqVO vo);

    @ApiOperation("验证微信code")
    @PostMapping("/auth/api/verification/wechat/verify")
    public Result<AuthWeChatUserInfoRespVO> verifyWechatCode(@Validated @RequestBody AuthWeChatUserInfoReqVO vo);

    @ApiOperation("验证微信小程序code")
    @PostMapping("/auth/api/verification/wechat/mini/verify")
    public Result<AuthWeChatMiniUserInfoRespVO> verifyWechatMiniCode(@Validated @RequestBody AuthWeChatUserInfoReqVO vo);

    @ApiOperation("微信小程序检验登录态")
    @PostMapping("/auth/api/verification/wechat/mini/check/session")
    public Result<Boolean> wechatMiniCodeCheckSession(@Validated @RequestBody AuthWeChatCheckSessionReqVO vo);

    @ApiOperation("微信小程序删除用户存储消息")
    @PostMapping("/auth/api/verification/wechat/mini/user/storage/remove")
    public Result<Boolean> wechatMiniRemoveUserStorage(@Validated @RequestBody AuthWeChatRemoveUserStorageReqVO vo);

    @ApiOperation("微信小程序token获取")
    @PostMapping("/auth/api/verification/wechat/mini/token")
    public Result<String> wechatMiniToken(@Validated @RequestBody AuthMiniTokenReqVO vo);

    @ApiOperation("验证抖音小程序code")
    @PostMapping("/auth/api/verification/douyin/mini/verify")
    public Result<AuthDouyinMiniUserInfoRespVO> verifyDouyinMiniCode(@Validated @RequestBody AuthWeChatUserInfoReqVO vo);

    @ApiOperation("抖音小程序token获取")
    @PostMapping("/auth/api/verification/douyin/mini/token")
    public Result<String> douyinMiniToken(@Validated @RequestBody AuthMiniTokenReqVO vo) throws Exception;
}
