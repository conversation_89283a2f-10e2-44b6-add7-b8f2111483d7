# 接口设计-ECS修改付费

mega-cloud-admin模组的controller目录下已经新建了AdminEcsController.java文件，作为ECS相关接口的统一入口。
其中`AdminEcsService.java`，作为ECS相关接口的业务处理类。
如果设计到数据库操作，则在AdminEcsDao.java和对应的AdminEcsDao.xml，作为ECS相关的数据库操作类。

提供如下接口：

## ECS按需转按月 POST
/admin/api/{projectId}/microservice/ecs/period
/admin/api/system/microservice/ecs/period

## 入参

```java
public class AdminEcsReqVO  {
    @NotBlank(message = "ECS ID不能为空")
    private Long ecsServerId;          // ECS ID

    private Long projectId;          // 项目ID
}
```
AdminEcsService完成与controller的对接，调用`AdminAliEcsService`的`changeToPeriod`方法完成按需转按月的业务逻辑。




### ECS按月转自动续费 POST 
/admin/api/{projectId}/microservice/ecs/autorenew
/admin/api/system/microservice/ecs/autorenew

## 入参使用 AdminEcsReqVO

AdminEcsService完成与controller的对接，调用`AdminAliEcsService`的`changeToAutoRenew`方法完成按月转自动续费的业务逻辑。