package com.mega.platform.cloud.auth.service;

import com.mega.platform.cloud.auth.client.AppleTokenVerifierClient;
import com.mega.platform.cloud.auth.client.DouyinVerifierClient;
import com.mega.platform.cloud.auth.client.WeChatVerifierClient;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.data.entity.*;
import com.mega.platform.cloud.data.vo.auth.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthVerificationService {
    private final AuthSmsService authSmsService;
    private final AppleTokenVerifierClient appleTokenVerifierClient;
    private final WeChatVerifierClient weChatVerifierClient;
    private final RedisProduceService redisProduceService;
    private final DouyinVerifierClient douyinVerifierClient;
    public AuthSendSmsCodeRespVO sendSmsCode(AuthSendSmsCodeReqVO vo) throws Exception {
        AuthSendSmsCodeRespVO respVO = new AuthSendSmsCodeRespVO();
        String code = authSmsService.sendAndCachePhoneCode(vo.getAppId(), vo.getPhoneNum(), vo.getAreaCode(), vo.getType());
        respVO.setCode(code);
        return respVO;
    }

    public boolean verifySmsCode(AuthVerifySmsCodeReqVO vo) {
        boolean verifyResult = authSmsService.verifyPhoneCode(vo.getAppId(), vo.getPhoneNum(), vo.getCode());
        AuthVerifyLog authVerifyLog = new AuthVerifyLog();
        authVerifyLog.setProjectAppId(vo.getAppId()).setChannelCode(ThirdPlatformEnum.ALIPAY.getPlatformCode())
                .setLoginId(vo.getPhoneNum()).setVerifyResult(verifyResult?"success":"fail")
                .setDeviceId(vo.getDeviceId()).setClientIp(vo.getClientIp());
        if (!verifyResult) {
            authVerifyLog.setErrorMsg("验证码不存在");
        }
        redisProduceService.pushAuthVerifyLog(authVerifyLog);
        return verifyResult;
    }

    public AuthVerifyAppleTokenRespVO verifyAppleToken(AuthVerifyAppleTokenReqVO vo) {
        AuthVerifyAppleTokenRespVO respVO = new AuthVerifyAppleTokenRespVO();
        AuthVerifyLog authVerifyLog = new AuthVerifyLog();
        authVerifyLog.setProjectAppId(vo.getAppId()).setChannelCode(ThirdPlatformEnum.WECHAT.getPlatformCode())
                .setDeviceId(vo.getDeviceId()).setClientIp(vo.getClientIp());
        try {
            String subject = appleTokenVerifierClient.verifyAndExtractSub(vo.getAppId(), vo.getIdToken());
            respVO.setSubject(subject);
            respVO.setSuccess(true);
            authVerifyLog.setLoginId(subject).setVerifyResult("success");
        } catch (Exception e) {
            authVerifyLog.setVerifyResult("fail").setErrorMsg(e.getMessage());
            respVO.setSuccess(false);
        }
        redisProduceService.pushAuthVerifyLog(authVerifyLog);
        return respVO;
    }

    public AuthWeChatUserInfoRespVO verifyWeChatCode(AuthWeChatUserInfoReqVO vo) {
        AuthWeChatUserInfoRespVO respVO = new AuthWeChatUserInfoRespVO();
        AuthWeChatAccessTokenResp tokenResp = weChatVerifierClient.getAccessToken(vo.getAppId(), vo.getCode());
        AuthVerifyLog authVerifyLog = new AuthVerifyLog();
        authVerifyLog.setProjectAppId(vo.getAppId()).setChannelCode(ThirdPlatformEnum.WECHAT.getPlatformCode())
                .setDeviceId(vo.getDeviceId()).setClientIp(vo.getClientIp());
        if (tokenResp.getErrcode() != null) {
            authVerifyLog.setVerifyResult("fail").setErrorMsg(tokenResp.getErrmsg());
            respVO.setSuccess(false);
        } else {
            authVerifyLog.setLoginId(tokenResp.getUnionid()).setVerifyResult("success");
            respVO.setSuccess(true);
        }
        redisProduceService.pushAuthVerifyLog(authVerifyLog);

        AuthWeChatUserInfoResp userInfo = weChatVerifierClient.getUserInfo(tokenResp.getAccessToken(), tokenResp.getOpenid());
        BeanUtils.copyProperties(userInfo, respVO);
        return respVO;
    }

    public AuthWeChatMiniUserInfoRespVO verifyWeChatMiniCode(AuthWeChatUserInfoReqVO vo) {
        AuthWeChatMiniUserInfoRespVO respVO = new AuthWeChatMiniUserInfoRespVO();
        AuthWeChatMiniProgramSessionResp sessionResp = weChatVerifierClient.getMiniProgramSession(vo.getAppId(), vo.getCode());
        AuthVerifyLog authVerifyLog = new AuthVerifyLog();
        authVerifyLog.setProjectAppId(vo.getAppId()).setChannelCode(ThirdPlatformEnum.WECHAT.getPlatformCode())
                .setDeviceId(vo.getDeviceId()).setClientIp(vo.getClientIp());
        if (sessionResp.getErrCode() != null) {
            authVerifyLog.setVerifyResult("fail").setErrorMsg(sessionResp.getErrMsg());
            respVO.setSuccess(false);
        } else {
            authVerifyLog.setLoginId(sessionResp.getUnionId()).setVerifyResult("success");
            respVO.setSuccess(true);
        }
        redisProduceService.pushAuthVerifyLog(authVerifyLog);

        BeanUtils.copyProperties(sessionResp, respVO);
        return respVO;
    }

    public Boolean checkWeChatMiniSession(AuthWeChatCheckSessionReqVO reqVO) {
        try {
            return weChatVerifierClient.checkSession(reqVO.getAppId(), reqVO.getOpenId(), reqVO.getSessionKey());
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean removeWeChatUserStorage(AuthWeChatRemoveUserStorageReqVO reqVO) {
        try {
            return weChatVerifierClient.removeUserStorage(reqVO.getAppId(), reqVO.getOpenId(), reqVO.getSessionKey(), reqVO.getKeys());
        } catch (Exception e) {
            return false;
        }
    }

    public String getWeChatGeneralAccessToken(Long appId) {
        return weChatVerifierClient.getGeneralAccessToken(appId);
    }

    public String getDouyinGeneralAccessToken(Long appId) throws Exception {
        return douyinVerifierClient.getGeneralAccessToken(appId);
    }

    public AuthDouyinMiniUserInfoRespVO verifyDouyinMiniCode(AuthWeChatUserInfoReqVO vo) {
        AuthDouyinMiniUserInfoRespVO respVO = new AuthDouyinMiniUserInfoRespVO();
        try {
            AuthDouyinMiniSessionResp authDouyinMiniSessionResp = douyinVerifierClient.getMiniProgramSession(vo.getAppId(), vo.getCode());
            respVO.setSuccess(true).setSessionKey(authDouyinMiniSessionResp.getSessionKey())
                    .setOpenId(authDouyinMiniSessionResp.getOpenId()).setUnionId(authDouyinMiniSessionResp.getUnionId());
        } catch (Exception e) {
            log.error("verifyDouyinMiniCode error:",  e);
            respVO.setSuccess(false);
        }
        return respVO;
    }
}
