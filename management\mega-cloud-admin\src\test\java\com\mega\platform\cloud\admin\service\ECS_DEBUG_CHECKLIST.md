## ECS实例创建配置检查清单

### 🔧 修复后的关键配置对比

| 配置项 | 问题配置 | 修复后配置 | 说明 |
|--------|----------|------------|------|
| InternetMaxBandwidthOut | 0 | Math.max(config.getBandwidth(), 1) | 确保至少1Mbps带宽 |
| CreditSpecification | "Unlimited" | 保持不变 | 突发性能实例配置 |
| SecurityGroupIds | 检查安全组规则 | 确保允许必要的出站流量 | 网络访问权限 |

### 🚨 实例自动停掉的可能原因

1. **网络带宽为0** - 主要原因
2. **安全组过严** - 阻止了必要的网络通信
3. **镜像问题** - 自定义镜像启动脚本异常
4. **资源不足** - 账户余额或配额限制
5. **系统监控** - 阿里云检测到异常自动停止

### 🛠️ 排查步骤

1. 检查实例创建日志
2. 查看阿里云控制台的实例事件
3. 验证安全组规则
4. 确认镜像是否正常
5. 检查账户余额和配额

### 💡 建议

- 始终设置至少1Mbps的公网带宽
- 使用阿里云官方镜像进行测试
- 检查安全组规则，确保允许必要的出站流量
- 监控实例的系统日志和事件日志
