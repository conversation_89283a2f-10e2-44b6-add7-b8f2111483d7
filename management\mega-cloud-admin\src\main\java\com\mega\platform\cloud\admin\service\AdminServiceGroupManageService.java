package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.common.constant.MicroserviceConstants;
import com.mega.platform.cloud.common.enums.ServiceGroupBuildActionEnum;
import com.mega.platform.cloud.admin.dao.AdminServiceGroupDao;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.client.microservice.ServicesGroupClient;
import com.mega.platform.cloud.common.mapper.JenkinsTaskGroupMapper;
import com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO;
import com.mega.platform.cloud.data.dto.microservice.BuildServicesDTO;
import com.mega.platform.cloud.data.entity.JenkinsTaskGroup;
import com.mega.platform.cloud.data.entity.ServicesGroup;
import com.mega.platform.cloud.data.entity.ServicesGroupTagRelation;
import com.mega.platform.cloud.data.vo.microservice.BuildServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupRespVO;
import com.mega.platform.cloud.data.vo.microservice.UpdateServicesGroupJenkinsParamReqVO;
import com.mega.platform.cloud.microservice.service.ServicesService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.AdminErrorCode;
import tk.mybatis.mapper.entity.Example;

import java.math.BigInteger;
import java.util.*;
import com.mega.platform.cloud.common.utils.EnvUtil;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdminServiceGroupManageService {
    private final AdminServiceGroupDao adminServiceGroupDao;
    private final ServicesService servicesService;
    private final JenkinsTaskGroupMapper jenkinsTaskGroupMapper;
    private final ServicesGroupClient servicesGroupClient;

    public AdminServiceGroupManageService(AdminServiceGroupDao adminServiceGroupDao,
                                          ServicesService servicesService, JenkinsTaskGroupMapper jenkinsTaskGroupMapper, ServicesGroupClient servicesGroupClient) {
        this.adminServiceGroupDao = adminServiceGroupDao;
        this.servicesService = servicesService;
        this.jenkinsTaskGroupMapper = jenkinsTaskGroupMapper;
        this.servicesGroupClient = servicesGroupClient;
    }

    /**
     * 创建服务组
     * @param projectId 项目ID
     * @param reqVO 创建请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void createServicesGroup(Long projectId, AdminServicesGroupCreateReqVO reqVO) {

        // 检查服务组名称是否重复
        ServicesGroup existGroup = adminServiceGroupDao.selectServicesGroupByProjectIdAndName(
                projectId, reqVO.getServicesGroupName(), null);
        if (existGroup != null) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组名称已存在");
        }

        // 构建远程调用参数
        CreateServicesGroupReqVO clientReqVO = buildCreateServicesGroupReqVO(projectId, reqVO);
        // 调用远程服务创建服务组
        CreateServicesGroupRespVO result;
        try {
            result = servicesService.createServicesGroup(clientReqVO);
        } catch (Exception e) {
            log.error("创建服务组失败：", e);
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "创建服务组失败：" + e.getMessage());
        }
        if (result == null || result.getServicesGroupId() == null) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "创建服务组失败：");
        }

        Long servicesGroupId = result.getServicesGroupId();
        log.info("远程创建服务组成功，servicesGroupId: {}", servicesGroupId);

        // 更新本地扩展字段
        updateLocalServicesGroupFields(servicesGroupId, reqVO);

        // 插入services_group_tag_relation表
        if(reqVO.getTags() != null && reqVO.getTags().size() > 0) {
            List<ServicesGroupTagRelation> tagRelations = new ArrayList<>();
            for (BigInteger tagId : reqVO.getTags()) {
                ServicesGroupTagRelation tagRelation = new ServicesGroupTagRelation();
                tagRelation.setServiceGroupId(servicesGroupId);
                tagRelation.setServiceTagId(tagId.longValue());
                tagRelation.setCreateTime(new Date());
                tagRelation.setUpdateTime(new Date());
                tagRelation.setDelsign((byte) 0);
                tagRelations.add(tagRelation);
            }
            adminServiceGroupDao.insertServicesGroupTagRelation(tagRelations);
        }
    }

    /**
     * 编辑服务组
     * @param projectId 项目ID
     * @param reqVO 编辑请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void editServicesGroup(Long projectId, AdminServicesGroupEditReqVO reqVO) throws Exception {
        // 1 检查服务组是否存在
        ServicesGroup existGroup = adminServiceGroupDao.selectServicesGroupById(reqVO.getServicesGroupId());
        if (existGroup == null) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组不存在或已删除");
        }
        // 检查项目权限
        if (!projectId.equals(existGroup.getProjectId())) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "无权限操作该服务组");
        }
        // 检查服务组名称是否重复
        if(StringUtils.hasText(reqVO.getServicesGroupName())) {
            ServicesGroup nameGroup = adminServiceGroupDao.selectServicesGroupByProjectIdAndName(projectId, reqVO.getServicesGroupName(), reqVO.getServicesGroupId());
            if (nameGroup != null) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组名称已存在");
            }
        }

        // 2 检测模块
        // 2.1 检测 Jenkins模板参数是否符合要求
        Map<String, String> templateParams = jenkinsParamsToMap(reqVO.getJenkinsParams());
        templateParams.put(MicroserviceConstants.JENKINS_TEMPLATE_KEY_ACTIVE_PROFILE, reqVO.getServicesEnv());
        List<JenkinsTemplateParamDTO> templateParamsList = servicesService.checkTemplateParamKey(existGroup.getJenkinsTemplateId(),  MicroserviceConstants.SERVICES_DATA_TYPE_SERVICES_GROUP, templateParams);

        // 2.2 检查是否有运行中的服务，限制可编辑字段
        Integer onlineServicesCount = adminServiceGroupDao.countOnlineServicesByServicesGroupId(reqVO.getServicesGroupId());
        Integer totalServicesCount = adminServiceGroupDao.countServicesByServicesGroupId(reqVO.getServicesGroupId());
        if(totalServicesCount < 1){
            // 如果没有服务，允许编辑所有字段, 更新jenkins参数
            this.updateJenkinsTemplateParams(existGroup.getId(), templateParamsList);
        }

        // 3 构建更新对象
        ServicesGroup updateGroup = buildUpdateServicesGroup(reqVO, onlineServicesCount > 0, totalServicesCount > 0);
        adminServiceGroupDao.updateServicesGroupSelective(updateGroup);
        // 3.2 更新组不可见参数
        UpdateServicesGroupJenkinsParamReqVO updateReqVO = new UpdateServicesGroupJenkinsParamReqVO();
        updateReqVO.setServicesGroupId(reqVO.getServicesGroupId());
        updateReqVO.setAdminUserId(reqVO.getAdminUserId());
        servicesService.updateServicesGroupJenkinsInvisibleParam(updateReqVO);

        // 4 处理标签关系变更
        if(reqVO.getTags() != null && reqVO.getTags().size() > 0) {
            List<ServicesGroupTagRelation> updateTagRelations = new ArrayList<>();
            List<ServicesGroupTagRelation> existingTags = adminServiceGroupDao.selectServicesGroupTagRelation(reqVO.getServicesGroupId());
            // 将新标签和要删除的标签添加到更新列表
            for (BigInteger tagId : reqVO.getTags()) {
                // 如果新标签不在现有标签中，则添加到更新列表
                if (existingTags.stream().noneMatch(tag -> tag.getServiceTagId().equals(tagId.longValue()))) {
                    updateTagRelations.add(new ServicesGroupTagRelation()
                                                .setServiceGroupId(reqVO.getServicesGroupId())
                                                .setServiceTagId(tagId.longValue())
                                                .setCreateTime(new Date())
                                                .setUpdateTime(new Date())
                                                .setDelsign((byte) 0)
                                        );
                } 
            }

            // 将要删除的标签添加到更新列表
            existingTags.stream()
                .filter(tag -> reqVO.getTags().stream().noneMatch(newTag -> tag.getServiceTagId().equals(newTag.longValue())))
                .forEach(tag -> {
                    tag.setDelsign((byte) 1);
                    tag.setUpdateTime(new Date());
                    updateTagRelations.add(tag);
                });

            // 执行更新
            if(updateTagRelations.size() > 0) {
                adminServiceGroupDao.insertServicesGroupTagRelation(updateTagRelations);
            }
        }
       

        log.info("编辑服务组成功，servicesGroupId: {}, servicesGroupName: {}", reqVO.getServicesGroupId(), reqVO.getServicesGroupName());
    }

    private void updateJenkinsTemplateParams(Long servicesGroupId, List<JenkinsTemplateParamDTO> jenkinsParams) {
        // 更新Jenkins模板参数
        adminServiceGroupDao.updateJenkinsTemplateParams(servicesGroupId, jenkinsParams);
    }

    /**
     * 更改服务组状态
     * @param projectId 项目ID
     * @param reqVO 状态编辑请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void editServicesGroupStatus(Long projectId, AdminServicesGroupStatusEditReqVO reqVO) {

        // 检查服务组是否存在
        ServicesGroup existGroup = adminServiceGroupDao.selectServicesGroupById(reqVO.getServicesGroupId());
        if (existGroup == null) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组不存在或已删除");
        }

        // 检查项目权限
        if (!projectId.equals(existGroup.getProjectId())) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "无权限操作该服务组");
        }

        Integer onlineServicesCount = adminServiceGroupDao.countOnlineServicesByServicesGroupId(reqVO.getServicesGroupId());

        // 下线检查：有正在运行的服务不让下线
        if (reqVO.getStatus() == 0) {
            if (onlineServicesCount > 0) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组下有正在运行的服务，请先停止所有服务后再下线");
            }
        }

        // 上线检查：有运行中的服务才能上线
        if (reqVO.getStatus() == 1) {
//            Integer totalServicesCount = adminServiceGroupDao.countServicesByServicesGroupId(reqVO.getServicesGroupId());
            if (onlineServicesCount < 1) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组下没有在线的服务，无法上线");
            }
        }

        // 更新状态
        int result = adminServiceGroupDao.updateServicesGroupStatus(reqVO.getServicesGroupId(), reqVO.getStatus());
        if (result <= 0) {
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"更新服务组状态失败");
        }

        log.info("更新服务组状态成功，servicesGroupId: {}, status: {}", reqVO.getServicesGroupId(), reqVO.getStatus());
    }

    /**
     * 重启服务组
     * @param projectId 项目ID
     * @param reqVO 操作请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void restartServicesGroup(Long projectId, AdminServicesGroupActionReqVO reqVO) {
        executeServicesGroupAction(projectId, reqVO, ServiceGroupBuildActionEnum.RESTART.getAction(), "重启");
    }

    /**
     * 停止服务组
     * @param projectId 项目ID
     * @param reqVO 操作请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void stopServicesGroup(Long projectId, AdminServicesGroupActionReqVO reqVO) {
        executeServicesGroupAction(projectId, reqVO, ServiceGroupBuildActionEnum.STOP.getAction(), "停止");
    }

    /**
     * 删除服务组
     * @param projectId 项目ID
     * @param reqVO 操作请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteServicesGroup(Long projectId, AdminServicesGroupActionReqVO reqVO) {

        // 检查服务组是否存在
        ServicesGroup existGroup = adminServiceGroupDao.selectServicesGroupById(reqVO.getServicesGroupId());
        if (existGroup == null) {
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"服务组不存在或已删除");
        }

        // 检查项目权限
        if (!projectId.equals(existGroup.getProjectId())) {
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"无权限操作该服务组");
        }
        // microservice删除jenkinsJob
        try {
            servicesService.deleteServicesGroupJenkinsJob(reqVO.getServicesGroupId(), reqVO.getAdminUserId());
        } catch (Exception e) {
            log.error("删除服务组失败，请重试", e);
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"删除服务组失败，请重试");
        }

        // 执行软删除
        adminServiceGroupDao.deleteServicesGroupById(reqVO.getServicesGroupId());
        log.info("删除服务组成功，servicesGroupId: {}, servicesGroupName: {}", reqVO.getServicesGroupId(), existGroup.getName());
    }

    /**
     * 服务组列表
     * @param projectId
     * @param reqVO
     * @return
     */
    public List<AdminServicesGroupRespVO> listServicesGroups(Long projectId, AdminServicesGroupQueryReqVO reqVO) {
        List<AdminServicesGroupRespVO> respVOS = adminServiceGroupDao.selectServicesGroups(projectId, reqVO);
        return respVOS;
    }

    /**
     * 服务组最近历史
     * @param servicesGroupId
     * @return
     */
    public AdminJenkinsTaskGroupVO lastTaskGroupByGroupId(Long servicesGroupId) {
        Example example = new Example(JenkinsTaskGroup.class);
        example.createCriteria().andEqualTo("servicesGroupId", servicesGroupId);
        example.setOrderByClause("create_time desc limit 1");
        List<JenkinsTaskGroup> jenkinsTaskGroups  = jenkinsTaskGroupMapper.selectByExample(example);
        // action对应 1=build、2=restart、3=stop、4=getlog、5=update
        Map<Integer, String> actionNameMap = new HashMap<>();
        actionNameMap.put(1, "构建");
        actionNameMap.put(2, "重启");
        actionNameMap.put(3, "停止");
        actionNameMap.put(4, "获取日志");
        actionNameMap.put(5, "更新");
        jenkinsTaskGroups.forEach(jenkinsTaskGroup -> {
            jenkinsTaskGroup.setActionName(actionNameMap.get(jenkinsTaskGroup.getAction()));
        });

        return jenkinsTaskGroups.isEmpty() ? null : jenkinsTaskGroups.get(0);
    }

    /**
     * 正在执行的任务组列表
     * @param projectId
     * @return
     */
    public List<AdminServicesGroupRunningJobRespVO> listServicesJobGroups(Long projectId) {
        List<AdminServicesGroupRunningJobRespVO> servicesGroupRunningJobRespVOS = adminServiceGroupDao.selectListServicesJobGroups(projectId);
        return servicesGroupRunningJobRespVOS;
    }

    public List<AdminServicesGroupRunningJobRespVO> listServiceGroupsJobHistory(Long projectId, AdminServicesGroupQueryReqVO reqVO) {
        List<AdminServicesGroupRespVO> respVOS = adminServiceGroupDao.selectServicesGroups(projectId, reqVO);
        List<Long> servicesGroupIds = respVOS.stream().map(AdminServicesGroupRespVO::getId).collect(Collectors.toList());
        List<AdminServicesGroupRunningJobRespVO> servicesGroupRunningJobRespVOS = adminServiceGroupDao.selectListServicesJobHistoryGroups(servicesGroupIds);
        return servicesGroupRunningJobRespVOS;
    }

    // ==================== 私有方法 ====================

    /**
     * 执行服务组操作（重启/停止）
     */
    private void executeServicesGroupAction(Long projectId, AdminServicesGroupActionReqVO reqVO, Integer action, String actionName) {   
        // 检查服务组是否存在
        ServicesGroup existGroup = adminServiceGroupDao.selectServicesGroupById(reqVO.getServicesGroupId());
        if (existGroup == null) {
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"服务组不存在或已删除");
        }

        // 检查项目权限
        if (!projectId.equals(existGroup.getProjectId())) {
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"无权限操作该服务组");
        }

        // 构建远程调用参数
        BuildServicesGroupReqVO clientReqVO = new BuildServicesGroupReqVO();
        clientReqVO.setServicesGroupId(reqVO.getServicesGroupId());
        clientReqVO.setAdminUserId(reqVO.getAdminUserId());
        clientReqVO.setAction(action);
        try {
            // TODO 调用远程服务
            servicesGroupClient.buildServicesGroup(clientReqVO);
            log.info("{}服务组成功，servicesroupId: {}", actionName, reqVO.getServicesGroupId());
        } catch (Exception e) {
            log.error("{}服务组失败，servicesGroupId: {}", actionName, reqVO.getServicesGroupId(), e);
            throw new AdminException(AdminErrorCode.ERR_0.getCode(),actionName + "服务组失败：" + e.getMessage());
        }
    }



    /**
     * 构建远程调用参数
     */
    private CreateServicesGroupReqVO buildCreateServicesGroupReqVO(Long projectId, AdminServicesGroupCreateReqVO reqVO) {
        CreateServicesGroupReqVO clientReqVO = new CreateServicesGroupReqVO();

        // 手动设置基本属性
        clientReqVO.setAdminUserId(reqVO.getAdminUserId());
        clientReqVO.setJenkinsTemplateId(reqVO.getJenkinsTemplateId());
        clientReqVO.setJenkinsServiceId(reqVO.getJenkinsServiceId());
        clientReqVO.setProjectAppId(reqVO.getProjectAppId());
        clientReqVO.setServicesGroupName(reqVO.getServicesGroupName());
        clientReqVO.setServiceUpdateId(reqVO.getServicesUpdateId());
        clientReqVO.setServiceAliveNum(reqVO.getServicesAliveNum());
        clientReqVO.setCheckAliveType(reqVO.getCheckAliveType());
        clientReqVO.setProjectId(projectId);// 设置项目相关信息
        clientReqVO.setServiceEnv(reqVO.getServicesEnv());// 设计环境

        // 处理jenkinsParams JSON字符串
        clientReqVO.setJenkinsParams(jenkinsParamsToMap(reqVO.getJenkinsParams()));
        return clientReqVO;
    }

    /**
     * 将jenkinsParams JSON字符串转换为Map
     * @param jenkinsParams JSON字符串
     * @return Map<String, String>
     */
    private Map<String, String> jenkinsParamsToMap(String jenkinsParams) {
        if (!StringUtils.hasText(jenkinsParams)) {
            return new HashMap<>();
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jenkinsParams, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            log.warn("解析jenkinsParams JSON失败，使用空Map: {}", jenkinsParams, e);
            return new HashMap<>();
        }
    }

    /**
     * 更新本地扩展字段
     */
    private void updateLocalServicesGroupFields(Long servicesGroupId, AdminServicesGroupCreateReqVO reqVO) {
        // 查询刚创建的服务组
        ServicesGroup servicesGroup = adminServiceGroupDao.selectServicesGroupById(servicesGroupId);
        if (servicesGroup == null) {
            log.warn("创建服务组后查询不到记录，servicesGroupId: {}", servicesGroupId);
            return;
        }

        // 更新扩展字段（在CreateServicesGroupReqVO中没有但在AdminServicesGroupCreateReqVO中有的字段）
        ServicesGroup updateGroup = new ServicesGroup();
        updateGroup.setId(servicesGroupId);
        updateGroup.setRemark(reqVO.getRemark());
        updateGroup.setIsSelf(reqVO.getIsSelf());
        updateGroup.setUseJenkins(reqVO.getUseJenkins().byteValue());
        updateGroup.setSort(reqVO.getSort());
        // 执行更新
        adminServiceGroupDao.updateServicesGroupSelective(updateGroup);
        log.info("更新服务组扩展字段成功，servicesGroupId: {}", servicesGroupId);
    }

    /**
     * 构建编辑时的更新对象
     */
    private ServicesGroup buildUpdateServicesGroup(AdminServicesGroupEditReqVO reqVO, boolean hasOnlineServices, boolean hasServices) {
        ServicesGroup updateGroup = new ServicesGroup();
        updateGroup.setId(reqVO.getServicesGroupId());

        if (!hasServices) {
            // 无服务时，所有字段都可改
            updateGroup.setName(reqVO.getServicesGroupName());
            updateGroup.setServicesUpdateType(reqVO.getServicesUpdateId());
            updateGroup.setServicesEnv(reqVO.getServicesEnv());
            updateGroup.setServicesAliveNum(reqVO.getServicesAliveNum());
            updateGroup.setCheckAliveType(reqVO.getCheckAliveType());
            updateGroup.setJenkinsServicesId(reqVO.getJenkinsServiceId());
            updateGroup.setJenkinsTemplateId(reqVO.getJenkinsTemplateId());
            updateGroup.setRemark(reqVO.getRemark());
            updateGroup.setIsSelf(reqVO.getIsSelf());
            updateGroup.setUseJenkins(reqVO.getUseJenkins().byteValue());
            updateGroup.setServicesLogFormatId(reqVO.getServicesLogFormatId());
        } else {
            // 有服务时，只能改部分字段：备注、保活数量、管理员、is_self、services_log_format_id
            updateGroup.setRemark(reqVO.getRemark());
            updateGroup.setServicesAliveNum(reqVO.getServicesAliveNum());
            updateGroup.setIsSelf(reqVO.getIsSelf());
            updateGroup.setServicesLogFormatId(reqVO.getServicesLogFormatId());
        }
        updateGroup.setSort(reqVO.getSort());
        updateGroup.setAdminUserId(reqVO.getAdminUserId());

        updateGroup.setUpdateTime(new Date());
        return updateGroup;
    }



}


