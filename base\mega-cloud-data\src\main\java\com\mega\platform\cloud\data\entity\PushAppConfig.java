package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "push_app_config")
public class PushAppConfig {
    /**
     * 配置ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Integer id;

    /**
     * 应用ID
     */
    @Column(name = "app_id")
    private Integer appId;

    /**
     * 设备操作系统id
     */
    @Column(name = "device_os_id")
    private Integer deviceOsId;

    /**
     * 推送渠道id
     */
    @Column(name = "third_platform_id")
    private Integer thirdPlatformId;

    /**
     * 环境: 0=开发环境, 1=生产环境
     */
    @Column(name = "environment")
    private Byte environment;

    /**
     * 推送配置相关
     */
    @Column(name = "config")
    private String config;

    /**
     * 回调url，平台推送成功回调app 如：http://app.io-game-demo.com/push/callback
     */
    @Column(name = "callback_url")
    private String callbackUrl;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 删除标识: 0=未删除, 1=已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}