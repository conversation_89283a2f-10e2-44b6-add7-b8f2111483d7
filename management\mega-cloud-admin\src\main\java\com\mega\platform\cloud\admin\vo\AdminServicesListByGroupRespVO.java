package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Map;

/**
 * 基于组的services查询响应参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "基于组的services查询响应参数", description = "基于组的services查询响应参数")
public class AdminServicesListByGroupRespVO {

    // services基本信息
    @ApiModelProperty("服务ID")
    private Long servicesId;

    @ApiModelProperty("服务名称")
    private String servicesName;

    @ApiModelProperty("服务备注")
    private String servicesRemark;

    @ApiModelProperty("服务状态")
    private Integer servicesStatus;

    @ApiModelProperty("服务运行状态")
    private Integer servicesRunningStatus;

    @ApiModelProperty("服务真实运行状态")
    private Integer servicesRealRunningStatus;

    @ApiModelProperty("服务创建时间")
    private Date servicesCreateTime;

    private Map<String, String> servicesParams;

    @ApiModelProperty("公网IP")
    private String publicIp;
    @ApiModelProperty("内网IP")
    private String innerIp;

    // 最后一次jenkins_task信息
    @ApiModelProperty("最后一次任务ID")
    private Long lastTaskId;

    @ApiModelProperty("最后一次任务操作类型")
    private Integer lastTaskAction;

    @ApiModelProperty("最后一次任务是否成功")
    private Integer lastTaskIsSuccess;

    @ApiModelProperty("最后一次任务完成时间")
    private Date lastTaskCompleteTime;

    @ApiModelProperty("最后一次任务Jenkins链接")
    private String lastTaskJenkinsJobUrl;

    @ApiModelProperty("最后一次任务Git提交号")
    private String lastTaskGitCommit;
}