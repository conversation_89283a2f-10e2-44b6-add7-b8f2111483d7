package com.mega.platform.cloud.admin.constant;

public class AdminServerEnum {
    public enum ServerStatus {

        ERROR(0, "异常"),
        NORMAL(1, "正常"),
        CREATING(2, "正在创建"),
        INITIALIZING(3, "正在初始化"),
        PENDING_SHUTDOWN(4, "待关闭"),
        EXITED(5, "已退"),
        PENDING_UPGRADE(6, "待升级"), //升级镜像
        UPGRADING(7, "升级中");

        private final Integer code;
        private final String desc;

        ServerStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static ServerStatus getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (ServerStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

}