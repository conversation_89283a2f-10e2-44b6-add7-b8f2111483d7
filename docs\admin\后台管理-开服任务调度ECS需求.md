# 后台管理-开服任务调用ECS需求


## 游戏开服获得需要的资源
   生成开服任务
    * 1. 生成mysql子任务
    * 2. 生成redis子任务
    * 3. 生成微服务子任务
    
###  1 mysql服务器

  * 生成mysql子任务
  * 分支1不需要新建，获得运行中的mysql ecs实例
  * 分支2新建mysql ecs
     1. 生成新建 mysql ecs子任务
     2. 获得ecs config 信息，调用ecs api 创建ecs
     3. 定时轮询 ecs 创建状态
     4. ecs 创建成功，mysql 运行成功
     5. 生成 mysql 连接信息，mysql初始化，并同步数据结构和基础数据
     6. mysql 初始化成功，更新 mysql ecs 任务状态为成功

### 2 redis服务器

    * 生成redis子任务
    * 分支1不需要新建，获得运行中的redis ecs实例
    * 分支2新建redis ecs
         1. 生成新建 redis ecs子任务
         2. 获得ecs config 信息，调用ecs api 创建ecs
         3. 定时轮询 ecs 创建状态
         4. ecs 创建成功，redis 运行成功
         5. redis主从和哨兵模式启动成功
         6. redis 初始化成功，更新 redis ecs 任务状态为成功


### 3 微服务服务器
    * 生成微服务子任务
    * 分支1获得运行中的微服务 ecs 实例
    * 分支2新建微服务 ecs
         1. 生成新建 微服务 ecs子任务
         2. 获得ecs config 信息，调用ecs api 创建ecs
         3. 定时轮询 ecs 创建状态
         4. ecs 创建成功，微服务 运行成功
         5. 微服务ces 初始化成功 
         6. 根据mysql redis以及任务配置，初始化微服务的配置
         7. 启动各项微服务
         8. 微服务健康检查通过，初始化成功，更新 微服务 ecs 任务状态为成功
         9. 开服成功
         10. 调用接口插入区服信息，比如game-mining库的`game_region`和`game_region_group`表


## 已存在的表结构

* ecs_server  云服务器表,记录创建的ecs实例
* ecs_server_config ecs云服务器配置表，创建实例时候使用
* ecs_server_region esc云服务器区域表，创建阿里云ecs实例时需要用的配置
* esc_server_image esc云服务器镜像表，所有服务流行的镜像都配置在这里 


## 思考

1. 根据需求，可以规划ecs镜像有3种类型，分别是mysql镜像，redis镜像，微服务镜像
2. redis资源创建的流程，会比较负责，因为采用的是master-salve模式，需要3台ecs,并且每台还要部署sentinel节点
3. mysql资源创建的流程，会比较简单，只需要创建一台ecs，并且部署mysql服务
4. 微服务资源创建的流程，会比较复杂，需要根据提供的mysql和redis资源，来配置微服务的启动命令。

