package com.mega.platform.cloud.admin.service;


import com.aliyun.sdk.service.ecs20140526.models.DescribeInstancesRequest;
import com.aliyun.sdk.service.ecs20140526.models.DescribeInstancesResponse;
import com.google.gson.Gson;

import java.util.List;

public class AliyunRunInstancesExample {
//
//  private static final int INSTANCE_STATUS_CHECK_INTERVAL_MILLISECOND = 3000;
//
//  private static final long INSTANCE_STATUS_TOTAL_CHECK_TIME_ELAPSE_MILLISECOND =
//    60000 * 3;
//
//  private static final String INSTANCE_STATUS_RUNNING = "Running";
//
//  private static final String ALIBABA_CLOUD_ACCESS_KEY_ID =
//    "ALIBABA_CLOUD_ACCESS_KEY_ID";
//
//  private static final String ALIBABA_CLOUD_ACCESS_KEY_SECRET =
//    "ALIBABA_CLOUD_ACCESS_KEY_SECRET";
//
//  private com.aliyun.ecs20140526.Client ecsClient;
//
//  private Gson gson = new Gson();
//
//  /**
//   * 实例的计费方式
//   */
//  private String instanceChargeType = "PostPaid";
//
//  /**
//   * 实例所属的地域ID
//   */
//  private String regionId = "cn-hangzhou";
//
//  /**
//   * 实例所属的可用区ID
//   */
//  private String zoneId = "cn-hangzhou-i";
//
//  /**
//   * 实例的资源规格
//   */
//  private String instanceType = "ecs.e-c1m1.large";
//
//  /**
//   * 指定CPU选项
//   */
//  private RunInstancesRequestCpuOptions cpuOptions =
//    new RunInstancesRequestCpuOptions().setCore(1).setThreadsPerCore(2);
//
//  /**
//   * 是否为I/O优化实例
//   */
//  private String ioOptimized = "optimized";
//
//  /**
//   * 镜像ID
//   */
//  private String imageId = "m-bp15kyrnd24wnnm2fe6j";
//
//  /**
//   * 系统盘相关参数
//   */
//  private RunInstancesRequestSystemDisk systemDisk =
//    new RunInstancesRequestSystemDisk()
//      .setSize("40")
//      .setCategory("cloud_auto")
//      .setProvisionedIops(0)
//      .setBurstingEnabled(true);
//
//  /**
//   * 网络计费类型
//   */
//  private String internetChargeType = "PayByTraffic";
//
//  /**
//   * 公网出带宽最大值（单位：Mbps）
//   */
//  private Integer internetMaxBandwidthOut = 100;
//
//  /**
//   * 虚拟交换机ID
//   */
//  private String vSwitchId = "vsw-bp1lw1longj7zoxy5txez";
//
//  /**
//   * 将实例同时加入多个安全组
//   */
//  private List<String> securityGroupIds = Arrays.asList(
//    "sg-bp1fvp7j3mibt0kxcs0p",
//    "sg-bp1atg4jn10vcvy39lch"
//  );
//
//  /**
//   * 镜像相关属性信息
//   */
//  private RunInstancesRequestImageOptions imageOptions =
//    new RunInstancesRequestImageOptions().setLoginAsNonRoot(false);
//
//  /**
//   * 是否使用镜像预设的密码
//   */
//  private Boolean passwordInherit = true;
//
//  /**
//   * 实例名称
//   */
//  private String instanceName = "launch-advisor-20250912";
//
//  private RunInstancesRequestPrivateDnsNameOptions privateDnsNameOptions =
//    new RunInstancesRequestPrivateDnsNameOptions().setHostnameType("Custom");
//
//  /**
//   * 当创建多台实例时，是否为HostName和InstanceName自动添加有序后缀
//   */
//  private Boolean uniqueSuffix = false;
//
//  /**
//   * 访问实例元数据时是否强制使用加固模式（IMDSv2）
//   */
//  private String httpTokens = "optional";
//
//  /**
//   * 是否在专有宿主机上创建实例
//   */
//  private String tenancy = "default";
//
//  /**
//   * 专有宿主机实例是否与专有宿主机关联
//   */
//  private String affinity = "default";
//
//  /**
//   * 指定创建ECS实例的数量
//   */
//  private Integer amount = 1;
//
//  public static void main(String[] args) throws Exception {
//    /**
//     * 调用创建实例API后会查询实例的状态，直到变成Running为止
//     */
//    AliyunRunInstancesExample example = new AliyunRunInstancesExample();
//    example.initClients();
//    example.callToRunInstances();
//  }
//
//  private void initClients() throws Exception {
//    initECSClient();
//  }
//
//  /**
//   * 使用AK，SK初始化账号的ecs Client
//   */
//  private void initECSClient() throws Exception {
//    /*
//      工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
//      建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
//      请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID、ALIBABA_CLOUD_ACCESS_KEY_SECRET
//    */
//    Config ecsConfig = new Config()
//      .setAccessKeyId(System.getenv(ALIBABA_CLOUD_ACCESS_KEY_ID))
//      .setAccessKeySecret(System.getenv(ALIBABA_CLOUD_ACCESS_KEY_SECRET))
//      .setRegionId(regionId);
//    ecsClient = new com.aliyun.ecs20140526.Client(ecsConfig);
//  }
//
//  /**
//   * 调用创建实例的API，得到实例ID后继续查询实例状态
//   */
//  public void callToRunInstances() throws Exception {
//    RunInstancesResponse response = callRunInstancesApi();
//    if (response == null) {
//      return;
//    }
//    List<String> instanceIds = response
//      .getBody()
//      .getInstanceIdSets()
//      .getInstanceIdSet();
//
//    print(
//      String.format(
//        "Success. Instance creation succeed. InstanceIds: %s",
//        gson.toJson(instanceIds)
//      )
//    );
//
//    callToDescribeInstances(instanceIds);
//  }
//
//  private RunInstancesRequest composeRunInstancesRequest() {
//    return new RunInstancesRequest()
//      .setInstanceChargeType(instanceChargeType)
//      .setRegionId(regionId)
//      .setZoneId(zoneId)
//      .setInstanceType(instanceType)
//      .setCpuOptions(cpuOptions)
//      .setIoOptimized(ioOptimized)
//      .setImageId(imageId)
//      .setSystemDisk(systemDisk)
//      .setInternetChargeType(internetChargeType)
//      .setInternetMaxBandwidthOut(internetMaxBandwidthOut)
//      .setVSwitchId(vSwitchId)
//      .setSecurityGroupIds(securityGroupIds)
//      .setImageOptions(imageOptions)
//      .setPasswordInherit(passwordInherit)
//      .setInstanceName(instanceName)
//      .setPrivateDnsNameOptions(privateDnsNameOptions)
//      .setUniqueSuffix(uniqueSuffix)
//      .setHttpTokens(httpTokens)
//      .setTenancy(tenancy)
//      .setAffinity(affinity)
//      .setAmount(amount);
//  }
//
//  private RunInstancesResponse callRunInstancesApi() throws Exception {
//    RunInstancesRequest runInstancesRequest = composeRunInstancesRequest();
//    try {
//      return ecsClient.runInstances(runInstancesRequest);
//    } catch (TeaException error) {
//      // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
//      // 错误码
//      print(error.getCode());
//      // 错误 message
//      print(error.getMessage());
//
//      if (error.getData() != null && error.getData().get("Recommend") != null) {
//        // 诊断地址
//        print(error.getData().get("Recommend"));
//      }
//    }
//
//    return null;
//  }
//
//  /**
//   * 每3秒中检查一次实例的状态，超时时间设为3分钟。
//   *
//   * @param instanceIds 需要检查的实例ID
//   */
//
//  private void callToDescribeInstances(List<String> instanceIds)
//    throws Exception {
//    long startTime = System.currentTimeMillis();
//    while (true) {
//      sleepSomeTime();
//      DescribeInstancesResponse describeInstancesResponse =
//        callDescribeInstancesApi(instanceIds);
//      if (describeInstancesResponse != null) {
//        for (DescribeInstancesResponseBody.DescribeInstancesResponseBodyInstancesInstance instance : describeInstancesResponse.body
//          .getInstances()
//          .getInstance()) {
//          if (INSTANCE_STATUS_RUNNING.equals(instance.getStatus())) {
//            instanceIds.remove(instance.getInstanceId());
//            print(
//              String.format(
//                "Instance boot successfully: %s",
//                instance.getInstanceId()
//              )
//            );
//          }
//        }
//      }
//      if (instanceIds.isEmpty()) {
//        print("Instances all boot successfully.");
//        return;
//      }
//      if (
//        System.currentTimeMillis() - startTime >
//        INSTANCE_STATUS_TOTAL_CHECK_TIME_ELAPSE_MILLISECOND
//      ) {
//        print(
//          String.format(
//            "Instances boot failed within %s mins: %s",
//            INSTANCE_STATUS_TOTAL_CHECK_TIME_ELAPSE_MILLISECOND / 60000,
//            gson.toJson(instanceIds)
//          )
//        );
//        return;
//      }
//    }
//  }
//
//  private DescribeInstancesResponse callDescribeInstancesApi(
//    List<String> instanceIds
//  ) throws Exception {
//    DescribeInstancesRequest describeInstancesRequest =
//      new DescribeInstancesRequest()
//        .setRegionId(regionId)
//        .setInstanceIds(gson.toJson(instanceIds));
//    try {
//      return ecsClient.describeInstances(describeInstancesRequest);
//    } catch (TeaException error) {
//      // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
//      // 错误码
//      print(error.getCode());
//      // 错误 message
//      print(error.getMessage());
//
//      if (error.getData() != null && error.getData().get("Recommend") != null) {
//        // 诊断地址
//        print(error.getData().get("Recommend"));
//      }
//    }
//
//    return null;
//  }
//
//  private static void sleepSomeTime() {
//    try {
//      Thread.sleep(INSTANCE_STATUS_CHECK_INTERVAL_MILLISECOND);
//    } catch (InterruptedException e) {
//      e.printStackTrace();
//    }
//  }
//
//  private static void print(Object message) {
//    System.out.println(message);
//  }
}


