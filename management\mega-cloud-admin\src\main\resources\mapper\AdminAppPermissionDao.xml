<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminAppPermissionDao">
    
    <!-- 查询路由配置列表 -->
    <select id="selectUrlPatternList" resultType="java.lang.String">
        SELECT url_pattern FROM project_url_pattern
        WHERE delsign = 0
        UNION 
				SELECT DISTINCT url_pattern FROM project_app_permission
				WHERE delsign = 0;
    </select>
    
    <!-- 查询App权限列表 -->
    <select id="selectAppPermissionList" resultType="com.mega.platform.cloud.admin.vo.AppPermissionVO">
        SELECT 
            pap.url_pattern,
            pup.name,
            pup.remark,
            pap.delsign
        FROM 
            project_app_permission pap
        INNER JOIN 
            project_url_pattern pup ON pap.url_pattern = pup.url_pattern
        WHERE 
            pap.project_app_id = #{projectAppId}
            AND pap.delsign = 0
            AND pup.delsign = 0
        UNION ALL
        SELECT 
            pup.url_pattern,
            pup.name,
            pup.remark,
            1 AS delsign
        FROM 
            project_url_pattern pup
        LEFT JOIN 
            project_app_permission pap ON pup.url_pattern = pap.url_pattern 
            AND pap.project_app_id = #{projectAppId}
            AND pap.delsign = 0
        WHERE 
            pup.delsign = 0
            AND pap.url_pattern IS NULL
        UNION ALL
        SELECT 
            pap.url_pattern,
            '未知' AS name,
            '历史遗留' AS remark,
            pap.delsign
        FROM 
            project_app_permission pap
        LEFT JOIN 
            project_url_pattern pup ON pap.url_pattern = pup.url_pattern
        WHERE 
            pap.project_app_id = #{projectAppId}
            AND pap.delsign = 0
            AND pup.url_pattern IS NULL;
    </select>

    <!-- 插入或更新App权限 -->
    <insert id="insertOrUpdateAppPermission">
        INSERT INTO project_app_permission (
            project_app_id,
            url_pattern,
            delsign,
            create_time,
            update_time
        ) VALUES (
            #{appProjectId},
            #{urlPattern},
            #{delsign},
            NOW(),
            NOW()
        )
        ON DUPLICATE KEY UPDATE
            delsign = #{delsign},
            update_time = NOW()
    </insert>

</mapper>
