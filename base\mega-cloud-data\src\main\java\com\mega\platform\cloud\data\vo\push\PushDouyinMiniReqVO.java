package com.mega.platform.cloud.data.vo.push;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;
@Data
@ApiModel("抖音推送订阅信息")
public class PushDouyinMiniReqVO extends BaseReqVO {
    @ApiModelProperty("用户 openId")
    private String openId;

    @ApiModelProperty("模板 ID")
    private String tplId;

    @ApiModelProperty("模板数据（key-value）")
    private Map<String, String> data;

    @ApiModelProperty("点击跳转页面路径")
    private String page;
}
