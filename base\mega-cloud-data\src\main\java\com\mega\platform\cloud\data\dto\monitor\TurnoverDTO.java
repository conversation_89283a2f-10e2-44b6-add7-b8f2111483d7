package com.mega.platform.cloud.data.dto.monitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@ApiModel
public class TurnoverDTO {

    @ApiModelProperty("道具id")
    @NotNull
    private Long itemDicId;
    @ApiModelProperty("道具分类id")
    @NotNull
    private Long itemCateId;
    @ApiModelProperty("道具名称")
    @NotNull
    private String itemName;
    @ApiModelProperty("合并总数")
    @NotNull
    private Long sum;
}
