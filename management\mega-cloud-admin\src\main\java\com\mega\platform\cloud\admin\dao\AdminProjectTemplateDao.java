package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.data.entity.ProjectAppPermission;
import com.mega.platform.cloud.data.entity.ProjectAppPermissionTemplate;
import com.mega.platform.cloud.data.entity.ProjectAppTemplate;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 项目模板数据访问层接口
 * 提供项目模板相关的数据库操作方法
 * 
 * <AUTHOR>
 * @since 2025-09-05
 */
@Repository
public interface AdminProjectTemplateDao {

    /**
     * 查询所有有效的应用模板
     * 获取项目创建时需要创建的应用模板列表
     * 
     * @return 应用模板列表
     */
    List<ProjectAppTemplate> selectAllAppTemplates();

    /**
     * 根据应用名称查询权限模板
     * 获取指定应用名称对应的权限URL模式列表
     * 
     * @param appName 应用名称
     * @return 权限模板列表
     */
    List<ProjectAppPermissionTemplate> selectPermissionTemplatesByAppName(@Param("appName") String appName);

    /**
     * 根据多个应用名称批量查询权限模板
     * 用于批量获取多个应用的权限模板数据
     * 
     * @param appNames 应用名称列表
     * @return 权限模板列表
     */
    List<ProjectAppPermissionTemplate> selectPermissionTemplatesByAppNames(@Param("appNames") List<String> appNames);

    /**
     * 批量插入应用权限
     * 使用一条SQL语句批量插入多条权限记录
     * 
     * @param permissions 权限列表
     * @return 插入成功的记录数
     */
    int batchInsertAppPermissions(@Param("permissions") List<ProjectAppPermission> permissions);
}