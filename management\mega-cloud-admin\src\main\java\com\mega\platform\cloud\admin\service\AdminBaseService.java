package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.AdminErrorCode;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.dao.AdminBaseDao;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.common.mapper.*;
import com.mega.platform.cloud.data.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

import static com.mega.platform.cloud.common.constant.CommonConstant.BYTE_0;
import static com.mega.platform.cloud.common.constant.CommonConstant.BYTE_1;


@Service
@RequiredArgsConstructor
@Slf4j
public class AdminBaseService {
    private final AdminBaseDao adminBaseDao;
    private final DicMapper dicMapper;
    private final ServicesLogFormatMapper servicesLogFormatMapper;
    private final JenkinsServicesMapper jenkinsServicesMapper;
    private final JenkinsJobTemplateMapper jenkinsJobTemplateMapper;
    private final JenkinsJobTemplateParamMapper jenkinsJobTemplateParamMapper;
    private final EcsServerMapper ecsServerMapper;
    private final ProjectAppMapper projectAppMapper;
    private final AdminUserMapper adminUserMapper;
    
    private String[] colors = new String[]{"magenta", "red", "volcano", "orange", "gold", "lime", "green", "cyan", "blue", "geekblue", "purple", "grep"};

    public AdminBaseDicListRespVO getDicList(AdminBaseDicListReqVO reqVO) {
        // 判空
        List<Long> dicCateIds = reqVO.getDicCateIds();
        if (CollectionUtils.isEmpty(dicCateIds)) {
            return new AdminBaseDicListRespVO().setDicMap(Collections.emptyMap());
        }

        // 构造 Example 查询条件
        Example example = new Example(Dic.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("dicCateId",  dicCateIds).andEqualTo(new Dic().setDelsign((byte) 0));
        example.orderBy("sort").asc().orderBy("id").asc();
        List<Dic> dicList = dicMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(dicList)) {
            return new AdminBaseDicListRespVO().setDicMap(Collections.emptyMap());
        }
        Map<Long, List<Dic>> dicMap = dicList.stream().collect(Collectors.groupingBy(Dic::getDicCateId));
        return new AdminBaseDicListRespVO().setDicMap(dicMap);
    }

    public AdminBaseLogFormatListRespVO getLogFormatList() {
        List<ServicesLogFormat> servicesLogFormats = servicesLogFormatMapper.select(new ServicesLogFormat().setDelsign((byte) 0));
        return new AdminBaseLogFormatListRespVO().setServicesLogFormats(servicesLogFormats);
    }

    public AdminBaseJenkinsInstancesListRespVO getJenkinsInstancesList() {
        List<JenkinsServices> jenkinsServices =  jenkinsServicesMapper.select(new JenkinsServices().setDelsign((byte) 0));
        return new AdminBaseJenkinsInstancesListRespVO().setJenkinsServices(jenkinsServices);
    }

    public AdminBaseJenkinsTemplateListRespVO getJenkinsTemplateList() {
        List<JenkinsJobTemplate> jenkinsJobTemplates = jenkinsJobTemplateMapper.select(new JenkinsJobTemplate().setDelsign((byte) 0));
        return new AdminBaseJenkinsTemplateListRespVO().setJenkinsJobTemplates(jenkinsJobTemplates);
    }

    public AdminBaseJenkinsTemplateParamListRespVO getJenkinsTemplateListParam(AdminBaseJenkinsTemplateParamListReqVO reqVO) {
        List<JenkinsJobTemplateParam> jenkinsJobTemplateParams = jenkinsJobTemplateParamMapper.select(new JenkinsJobTemplateParam()
                .setJenkinsTemplateId(reqVO.getJenkinsTemplateId()).setServicesDataType(reqVO.getServicesDataType()).setDelsign((byte) 0).setIsVisible(BYTE_1));
        return new AdminBaseJenkinsTemplateParamListRespVO().setJenkinsJobTemplateParams(jenkinsJobTemplateParams);
    }

    public AdminBaseEcsServerListRespVO getEcsServerList(Long projectId) {
        List<EcsServer> ecsServers;
        if (projectId == -1) {
            ecsServers = ecsServerMapper.select(new EcsServer().setDelsign((byte) 0));
        } else {
            ecsServers = ecsServerMapper.select(new EcsServer().setProjectId(projectId).setDelsign((byte) 0));
        }
        return new AdminBaseEcsServerListRespVO().setEcsServerList(ecsServers);
    }

    public List<AdminBaseUserListRespVO> adminUserList() {
        List<AdminUser> adminUsers = adminUserMapper.select(new AdminUser().setDelsign(false));
        return adminUsers.stream().map(user -> {
            AdminBaseUserListRespVO respVO = new AdminBaseUserListRespVO();
            respVO.setAdminUserId(user.getId());
            respVO.setUsername(user.getUsername());
            return respVO;
        }).collect(Collectors.toList());
    }

    public List<AdminBaseAppListRespVO> getAppList(Long projectId) {
        ProjectApp projectApp = new ProjectApp();
        Example example = new Example(ProjectApp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(new Dic().setDelsign((byte) 0));
        if (projectId != null) {
            criteria.andEqualTo("projectId", projectId);
        }
        example.orderBy("id").desc();
        projectApp.setDelsign((byte) 0);
        List<ProjectApp> projectApps = projectAppMapper.selectByExample(example);
        return projectApps.stream().map(tempProjectApp -> {
            AdminBaseAppListRespVO  respVO = new AdminBaseAppListRespVO();
            respVO.setProjectAppId(tempProjectApp.getId()).setProjectAppName(tempProjectApp.getName());
            return respVO;
        }).collect(Collectors.toList());
    }

    /** 
     * 建立自定义标签
     */
    @Transactional(rollbackFor = Exception.class)
    public AdminBaseTagCreateRespVO createTag(AdminBaseTagCreateReqVO reqVO) {
        
        // 1 查询标签是否存在（检查dic_cate_id为2008或2009的记录）
        int existCount = adminBaseDao.checkTagNameExists(reqVO.getTagName());
        if (existCount > 0) {
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"标签已存在");
        }

        // 2 查询dic_cate_id 2009标签的最大值为dic_id
        Long maxDicId = adminBaseDao.getMaxDicIdByCateId(2009L);

        // 3 dic_id +1
        Long nextDicId = (maxDicId != null ? maxDicId : 0L) + 1;
        
        // 4 从colors中随机出一个颜色
        int randomIndex = (int)(Math.random() * colors.length);
        String randomColor = colors[randomIndex];
        
        // 5 插入dic表
        Dic adminTag = new Dic();
        adminTag.setId(nextDicId);
        adminTag.setName(reqVO.getTagName());
        adminTag.setValue(nextDicId.toString());
        adminTag.setDicCateId(2009L);
        adminTag.setStyle(randomColor);
        adminTag.setSort(0);
        adminTag.setRemark("services_group_tag_relation.service_tag_id");
        adminTag.setDelsign(BYTE_0);
        dicMapper.insertSelective(adminTag);
        return new AdminBaseTagCreateRespVO().setTagId(adminTag.getId());
    }
}
