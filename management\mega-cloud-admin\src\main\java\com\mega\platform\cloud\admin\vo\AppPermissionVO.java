package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@ApiModel("App权限VO")
@Accessors(chain = true)
public class AppPermissionVO {
      // @ApiModelProperty(value = "主键ID", example = "1")
      //   private Long id;

      //   @ApiModelProperty(value = "所属App的ID", example = "1")
      //   private Long projectAppId;

      //   @ApiModelProperty(value = "所属App的名称", example = "auth")
      //   private String projectAppName;

        @ApiModelProperty(value = "Ant风格路径匹配", example = "/api/v1/user/**")
        private String urlPattern;

        @ApiModelProperty(value = "路由名称", example = "auth路由")
        private String name;

        @ApiModelProperty(value = "描述")
        private String remark;

        // @ApiModelProperty(value = "创建时间")
        // private Date createTime;

        // @ApiModelProperty(value = "更新时间")
        // private Date updateTime;

        @ApiModelProperty(value = "是否删除：0=可用，1=已删除", example = "0")
        private Integer delsign;
}
