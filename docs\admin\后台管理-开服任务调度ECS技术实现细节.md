# 后台管理-开服任务调度ECS技术实现细节

## 1. 概述

### 1.1 技术架构
本文档详细描述游戏开服任务调度系统的技术实现细节，包括阿里云API集成、数据库初始化、Redis集群配置、微服务部署等核心技术方案。

### 1.2 技术栈
- **后端框架**：Spring Boot 2.5.9
- **云服务**：阿里云ECS API
- **数据库**：MySQL 8.0
- **缓存**：Redis 6.2
- **容器化**：Docker（可选）
- **监控**：Micrometer + Prometheus

## 2. 阿里云ECS API集成

### 2.1 SDK配置

**依赖配置**：
```xml
<dependency>
    <groupId>com.aliyun</groupId>
    <artifactId>ecs20140526</artifactId>
    <version>3.0.11</version>
</dependency>
<dependency>
    <groupId>com.aliyun</groupId>
    <artifactId>tea-openapi</artifactId>
    <version>0.2.8</version>
</dependency>
```

**配置类**：
```java
@Configuration
@ConfigurationProperties(prefix = "aliyun.ecs")
@Data
public class AliyunEcsConfig {
    
    private String accessKeyId;
    private String accessKeySecret;
    private String regionId = "cn-hangzhou";
    
    @Bean
    public EcsClient ecsClient() {
        Config config = new Config()
            .setAccessKeyId(accessKeyId)
            .setAccessKeySecret(accessKeySecret)
            .setRegionId(regionId);
        
        try {
            return new EcsClient(config);
        } catch (Exception e) {
            throw new RuntimeException("初始化阿里云ECS客户端失败", e);
        }
    }
}
```

### 2.2 ECS服务封装

```java
@Service
@Slf4j
public class AliyunEcsService {
    
    @Autowired
    private EcsClient ecsClient;
    
    /**
     * 创建ECS实例
     */
    public String createInstance(EcsServerConfig config, String instanceName) {
        try {
            RunInstancesRequest request = new RunInstancesRequest()
                .setImageId(config.getImageId())
                .setInstanceType(config.getInstanceType())
                .setRegionId(config.getRegionId())
                .setInstanceName(instanceName)
                .setSystemDiskSize(config.getSystemDiskSize());
            
            RunInstancesResponse response = ecsClient.runInstances(request);
            String instanceId = response.getBody().getInstanceIdSets().getInstanceIdSet().get(0);
            
            log.info("ECS实例创建成功，实例ID：{}", instanceId);
            return instanceId;
            
        } catch (Exception e) {
            log.error("创建ECS实例失败", e);
            throw new RuntimeException("创建ECS实例失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询实例状态
     */
    public InstanceStatus getInstanceStatus(String instanceId) {
        try {
            DescribeInstancesRequest request = new DescribeInstancesRequest()
                .setInstanceIds("[\"" + instanceId + "\"]");
            
            DescribeInstancesResponse response = ecsClient.describeInstances(request);
            Instance instance = response.getBody().getInstances().getInstance().get(0);
            
            return InstanceStatus.fromString(instance.getStatus());
            
        } catch (Exception e) {
            log.error("查询ECS实例状态失败，实例ID：{}", instanceId, e);
            return InstanceStatus.UNKNOWN;
        }
    }
}
```

## 3. MySQL数据库初始化

### 3.1 初始化脚本模板

```sql
-- mysql_init_template.sql
CREATE DATABASE IF NOT EXISTS game_db_${regionCode} 
DEFAULT CHARSET utf8mb4;

CREATE USER IF NOT EXISTS 'game_user_${regionCode}'@'%' 
IDENTIFIED BY '${randomPassword}';

GRANT ALL PRIVILEGES ON game_db_${regionCode}.* 
TO 'game_user_${regionCode}'@'%';

FLUSH PRIVILEGES;

USE game_db_${regionCode};
SOURCE /opt/sql/schema.sql;
SOURCE /opt/sql/base_data.sql;
```

### 3.2 数据库初始化服务

```java
@Service
@Slf4j
public class MysqlInitializer {
    
    @Value("${mysql.root.password}")
    private String rootPassword;
    
    /**
     * 初始化MySQL数据库
     */
    public ConnectionInfo initDatabase(String host, int port, String regionCode) {
        String randomPassword = generateRandomPassword();
        String database = "game_db_" + regionCode;
        String username = "game_user_" + regionCode;
        
        try {
            // 1. 连接MySQL实例
            String rootUrl = String.format("jdbc:mysql://%s:%d", host, port);
            DataSource rootDataSource = createDataSource(rootUrl, "root", rootPassword);
            
            // 2. 执行初始化脚本
            executeInitScript(rootDataSource, regionCode, randomPassword);
            
            // 3. 返回连接信息
            return ConnectionInfo.builder()
                .host(host)
                .port(port)
                .database(database)
                .username(username)
                .password(randomPassword)
                .build();
                
        } catch (Exception e) {
            log.error("MySQL数据库初始化失败，区服：{}", regionCode, e);
            throw new RuntimeException("MySQL数据库初始化失败：" + e.getMessage());
        }
    }
    
    private void executeInitScript(DataSource dataSource, String regionCode, String password) {
        try (Connection connection = dataSource.getConnection()) {
            String scriptContent = loadScriptTemplate("mysql_init_template.sql");
            scriptContent = scriptContent
                .replace("${regionCode}", regionCode)
                .replace("${randomPassword}", password);
            
            ScriptUtils.executeSqlScript(connection, new ByteArrayResource(scriptContent.getBytes()));
            log.info("MySQL初始化脚本执行成功，区服：{}", regionCode);
            
        } catch (Exception e) {
            throw new RuntimeException("执行MySQL初始化脚本失败", e);
        }
    }
}
```

## 4. Redis集群配置

### 4.1 Redis配置模板

**主节点配置**：
```bash
# redis-master.conf
bind 0.0.0.0
port 6379
requireauth ${REDIS_PASSWORD}
masterauth ${REDIS_PASSWORD}
save 900 1
maxmemory 2gb
maxmemory-policy allkeys-lru
```

**从节点配置**：
```bash
# redis-slave.conf
bind 0.0.0.0
port 6379
requireauth ${REDIS_PASSWORD}
masterauth ${REDIS_PASSWORD}
replicaof ${MASTER_IP} 6379
replica-read-only yes
```

### 4.2 Redis部署服务

```java
@Service
@Slf4j
public class RedisClusterDeployer {
    
    @Autowired
    private SshCommandExecutor sshExecutor;
    
    /**
     * 部署Redis集群
     */
    public RedisClusterInfo deployCluster(String regionCode, List<String> instanceIds) {
        try {
            // 1. 获取实例IP地址
            List<String> ipAddresses = getInstanceIpAddresses(instanceIds);
            String masterIp = ipAddresses.get(0);
            List<String> slaveIps = ipAddresses.subList(1, 3);
            
            // 2. 配置Redis主节点
            configureRedisMaster(masterIp, regionCode);
            
            // 3. 配置Redis从节点
            for (String slaveIp : slaveIps) {
                configureRedisSlave(slaveIp, masterIp, regionCode);
            }
            
            // 4. 部署Sentinel哨兵
            deploySentinel(ipAddresses, masterIp, regionCode);
            
            // 5. 返回集群信息
            return RedisClusterInfo.builder()
                .masterIp(masterIp)
                .slaveIps(slaveIps)
                .password(getRedisPassword(regionCode))
                .build();
                
        } catch (Exception e) {
            log.error("Redis集群部署失败，区服：{}", regionCode, e);
            throw new RuntimeException("Redis集群部署失败：" + e.getMessage());
        }
    }
    
    private void configureRedisMaster(String masterIp, String regionCode) {
        String password = getRedisPassword(regionCode);
        String config = loadTemplate("redis-master.conf")
            .replace("${REDIS_PASSWORD}", password);
        
        sshExecutor.uploadFile(masterIp, "/etc/redis/redis.conf", config);
        sshExecutor.executeCommand(masterIp, "systemctl start redis");
        
        log.info("Redis主节点配置完成，IP：{}", masterIp);
    }
}
```

## 5. 微服务部署策略

### 5.1 Docker化部署

**Dockerfile模板**：
```dockerfile
FROM openjdk:11-jre-slim

WORKDIR /app

RUN apt-get update && \
    apt-get install -y curl envsubst && \
    rm -rf /var/lib/apt/lists/*

COPY target/*.jar app.jar
COPY config-template/ /app/config-template/
COPY start.sh /app/start.sh

RUN chmod +x /app/start.sh

HEALTHCHECK --interval=30s --timeout=10s \
    CMD curl -f http://localhost:8080/actuator/health

EXPOSE 8080
ENTRYPOINT ["/app/start.sh"]
```

### 5.2 配置动态生成

**启动脚本**：
```bash
#!/bin/bash
set -e

# 生成配置文件
envsubst < /app/config-template/application.yml > /app/application.yml

# 设置JVM参数
JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"

# 启动应用
exec java $JAVA_OPTS -jar \
    -Dspring.config.location=file:/app/application.yml \
    app.jar
```

### 5.3 微服务部署服务

```java
@Service
@Slf4j
public class MicroserviceDeployer {
    
    /**
     * 部署微服务
     */
    public MicroserviceInfo deployMicroservice(String instanceIp, 
                                               ConnectionInfo mysqlInfo, 
                                               RedisClusterInfo redisInfo,
                                               String regionCode) {
        try {
            // 1. 环境初始化
            initializeEnvironment(instanceIp);
            
            // 2. 生成配置文件
            generateConfiguration(instanceIp, mysqlInfo, redisInfo, regionCode);
            
            // 3. 部署应用
            deployApplication(instanceIp, regionCode);
            
            // 4. 健康检查
            performHealthCheck(instanceIp);
            
            return MicroserviceInfo.builder()
                .instanceIp(instanceIp)
                .regionCode(regionCode)
                .status("running")
                .build();
                
        } catch (Exception e) {
            log.error("微服务部署失败，实例IP：{}", instanceIp, e);
            throw new RuntimeException("微服务部署失败：" + e.getMessage());
        }
    }
    
    private String generateSpringBootConfig(ConnectionInfo mysqlInfo, 
                                           RedisClusterInfo redisInfo, 
                                           String regionCode) {
        return String.format("""
            server:
              port: 8080
            
            spring:
              datasource:
                url: jdbc:mysql://%s:%d/%s
                username: %s
                password: %s
              
              redis:
                host: %s
                port: 6379
                password: %s
            
            logging:
              file:
                name: /opt/game-server/logs/application.log
            """, 
            mysqlInfo.getHost(), mysqlInfo.getPort(), mysqlInfo.getDatabase(),
            mysqlInfo.getUsername(), mysqlInfo.getPassword(),
            redisInfo.getMasterIp(), redisInfo.getPassword());
    }
}
```

## 6. 异步任务调度器

### 6.1 任务调度器设计

```java
@Component
@Slf4j
public class GameLaunchTaskScheduler {
    
    @Autowired
    private GameLaunchService gameLaunchService;
    
    /**
     * 定时扫描待处理任务
     */
    @Scheduled(fixedDelay = 10000)
    public void scanPendingTasks() {
        try {
            List<GameLaunchTask> pendingTasks = gameLaunchService.getPendingTasks();
            
            for (GameLaunchTask task : pendingTasks) {
                if (canExecuteTask(task)) {
                    executeTaskAsync(task);
                }
            }
        } catch (Exception e) {
            log.error("扫描待处理任务失败", e);
        }
    }
    
    /**
     * 定时检查运行中任务状态
     */
    @Scheduled(fixedDelay = 30000)
    public void checkRunningTasks() {
        try {
            List<GameLaunchTask> runningTasks = gameLaunchService.getRunningTasks();
            
            for (GameLaunchTask task : runningTasks) {
                checkTaskProgressAsync(task);
            }
        } catch (Exception e) {
            log.error("检查运行中任务失败", e);
        }
    }
    
    @Async("taskExecutor")
    public void executeTaskAsync(GameLaunchTask task) {
        try {
            log.info("开始执行开服任务，任务ID：{}", task.getId());
            gameLaunchService.executeTask(task);
        } catch (Exception e) {
            log.error("执行开服任务失败，任务ID：{}", task.getId(), e);
            gameLaunchService.markTaskFailed(task.getId(), e.getMessage());
        }
    }
}
```

### 6.2 线程池配置

```java
@Configuration
@EnableAsync
public class AsyncConfig {
    
    @Bean("taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("GameLaunch-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        executor.initialize();
        return executor;
    }
}
```
