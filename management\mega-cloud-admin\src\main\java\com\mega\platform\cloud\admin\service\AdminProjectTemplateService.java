package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.admin.dao.AdminProjectTemplateDao;
import com.mega.platform.cloud.admin.vo.AdminAppCreateReqVO;
import com.mega.platform.cloud.admin.vo.AdminAppRespVO;
import com.mega.platform.cloud.common.mapper.ProjectAppPermissionMapper;
import com.mega.platform.cloud.common.mapper.ProjectAppPermissionTemplateMapper;
import com.mega.platform.cloud.common.mapper.ProjectAppTemplateMapper;
import com.mega.platform.cloud.data.entity.ProjectAppPermission;
import com.mega.platform.cloud.data.entity.ProjectAppPermissionTemplate;
import com.mega.platform.cloud.data.entity.ProjectAppTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 项目模板服务类
 * 用于处理在新项目创建后，直接创建项目对应的应用，并赋予应用的权限
 * 
 * <AUTHOR>
 * @since 2025-09-05
 */
@Service
@Slf4j
public class AdminProjectTemplateService {

    private final AdminAppService adminAppService;
    private final AdminProjectTemplateDao adminProjectTemplateDao;
    private final ProjectAppTemplateMapper projectAppTemplateMapper;
    private final ProjectAppPermissionTemplateMapper projectAppPermissionTemplateMapper;
    private final ProjectAppPermissionMapper projectAppPermissionMapper;

    @Autowired
    public AdminProjectTemplateService(AdminAppService adminAppService,
                                       AdminProjectTemplateDao adminProjectTemplateDao,
                                       ProjectAppTemplateMapper projectAppTemplateMapper,
                                       ProjectAppPermissionTemplateMapper projectAppPermissionTemplateMapper,
                                       ProjectAppPermissionMapper projectAppPermissionMapper) {
        this.adminAppService = adminAppService;
        this.adminProjectTemplateDao = adminProjectTemplateDao;
        this.projectAppTemplateMapper = projectAppTemplateMapper;
        this.projectAppPermissionTemplateMapper = projectAppPermissionTemplateMapper;
        this.projectAppPermissionMapper = projectAppPermissionMapper;
    }

    /**
     * 创建模板应用
     * 在新项目创建后，根据模板自动创建应用并赋予权限
     * 
     * @param projectId 项目ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void createTemplateApp(Long projectId) {
        if (projectId == null) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        log.info("开始为项目[{}]创建模板应用", projectId);
        
        try {
            // 1. 查询所有应用模板
            List<ProjectAppTemplate> appTemplates = queryAppTemplates();
            if (appTemplates.isEmpty()) {
                log.warn("未找到任何应用模板，跳过创建");
                return;
            }
            
            // 2. 为每个模板创建应用
            List<AdminAppRespVO> createdApps = createAppsFromTemplates(projectId, appTemplates);
            if (createdApps.isEmpty()) {
                log.warn("未成功创建任何应用");
                return;
            }
            
            // 3. 建立应用名称到ID的映射
            Map<String, Long> appNameToIdMap = buildAppNameToIdMap(createdApps);
            
            // 4. 批量查询权限模板
            List<ProjectAppPermissionTemplate> permissionTemplates = queryPermissionTemplates(appNameToIdMap.keySet());
            
            // 5. 批量插入权限数据
            insertAppPermissions(appNameToIdMap, permissionTemplates);
            
            log.info("成功为项目[{}]创建了{}个模板应用，插入了{}条权限记录", 
                    projectId, createdApps.size(), permissionTemplates.size());
                    
        } catch (Exception e) {
            log.error("为项目[{}]创建模板应用失败", projectId, e);
            throw e;
        }
    }
    
    // =============== 私有方法 ===============
    
    /**
     * 查询所有应用模板
     */
    private List<ProjectAppTemplate> queryAppTemplates() {
        try {
            List<ProjectAppTemplate> templates = adminProjectTemplateDao.selectAllAppTemplates();
            log.info("查询到{}个应用模板", templates.size());
            return templates;
        } catch (Exception e) {
            log.error("查询应用模板失败", e);
            throw new RuntimeException("查询应用模板失败", e);
        }
    }
    
    /**
     * 根据模板创建应用
     */
    private List<AdminAppRespVO> createAppsFromTemplates(Long projectId, List<ProjectAppTemplate> appTemplates) {
        List<AdminAppRespVO> createdApps = new ArrayList<>();
        
        for (ProjectAppTemplate template : appTemplates) {
            try {
                // 构造创建应用的请求参数
                AdminAppCreateReqVO createReq = new AdminAppCreateReqVO()
                        .setName(template.getAppName())
                        .setRemark(template.getRemark() != null ? template.getRemark() : "系统模板自动创建");
                
                // 调用AdminAppService创建应用
                AdminAppRespVO createdApp = adminAppService.createApp(projectId, createReq);
                createdApps.add(createdApp);
                
                log.info("成功为项目[{}]创建应用[{}], ID: {}", 
                        projectId, template.getAppName(), createdApp.getId());
                        
            } catch (Exception e) {
                log.error("为项目[{}]创建应用[{}]失败", projectId, template.getAppName(), e);
                // 这里可以选择是否继续或者抛出异常
                // 目前选择继续创建其他应用，不因为一个应用失败而全部失败
                // 如果需要严格模式，可以改为: throw new RuntimeException("创建应用失败", e);
            }
        }
        
        log.info("共尝试创建{}个应用，成功创建{}个", appTemplates.size(), createdApps.size());
        return createdApps;
    }
    
    /**
     * 建立应用名称到ID的映射
     */
    private Map<String, Long> buildAppNameToIdMap(List<AdminAppRespVO> createdApps) {
        Map<String, Long> appNameToIdMap = new HashMap<>();
        
        for (AdminAppRespVO app : createdApps) {
            appNameToIdMap.put(app.getName(), app.getId());
        }
        
        log.info("建立了{}个应用的名称到ID映射: {}", 
                appNameToIdMap.size(), appNameToIdMap.keySet());
                
        return appNameToIdMap;
    }
    
    /**
     * 查询权限模板
     */
    private List<ProjectAppPermissionTemplate> queryPermissionTemplates(Set<String> appNames) {
        if (appNames.isEmpty()) {
            log.warn("应用名称集合为空，跳过查询权限模板");
            return new ArrayList<>();
        }
        
        try {
            // 将Set转为List传给DAO方法
            List<String> appNameList = new ArrayList<>(appNames);
            List<ProjectAppPermissionTemplate> permissionTemplates = 
                    adminProjectTemplateDao.selectPermissionTemplatesByAppNames(appNameList);
                    
            log.info("为{}个应用查询到{}条权限模板", appNames.size(), permissionTemplates.size());
            
            // 打印详细信息供调试
            if (log.isDebugEnabled()) {
                for (ProjectAppPermissionTemplate template : permissionTemplates) {
                    log.debug("应用[{}]权限模板: {}", template.getAppName(), template.getUrlPattern());
                }
            }
            
            return permissionTemplates;
            
        } catch (Exception e) {
            log.error("查询权限模板失败", e);
            throw new RuntimeException("查询权限模板失败", e);
        }
    }
    
    /**
     * 批量插入应用权限
     */
    private void insertAppPermissions(Map<String, Long> appNameToIdMap, 
                                     List<ProjectAppPermissionTemplate> permissionTemplates) {
        if (permissionTemplates.isEmpty()) {
            log.warn("权限模板为空，跳过插入权限数据");
            return;
        }
        
        List<ProjectAppPermission> permissions = new ArrayList<>();
        Date now = new Date();
        
        // 构造权限数据
        for (ProjectAppPermissionTemplate template : permissionTemplates) {
            String appName = template.getAppName();
            Long projectAppId = appNameToIdMap.get(appName);
            
            if (projectAppId == null) {
                log.warn("未找到应用[{}]对应的ID，跳过权限[{}]", appName, template.getUrlPattern());
                continue;
            }
            
            ProjectAppPermission permission = new ProjectAppPermission()
                    .setProjectAppId(projectAppId)
                    .setUrlPattern(template.getUrlPattern())
                    .setCreateTime(now)
                    .setUpdateTime(now)
                    .setDelsign((byte) 0);
                    
            permissions.add(permission);
        }
        
        if (permissions.isEmpty()) {
            log.warn("没有有效的权限数据需要插入");
            return;
        }
        
        try {
            // 使用批量插入方法，一次SQL操作插入所有数据
            int insertCount = adminProjectTemplateDao.batchInsertAppPermissions(permissions);
            
            log.info("批量插入成功，共插入{}条权限数据", insertCount);
            
            // 打印详细信息
            if (log.isDebugEnabled()) {
                for (ProjectAppPermission permission : permissions) {
                    log.debug("插入权限: 应用ID[{}] -> URL模式[{}]", 
                            permission.getProjectAppId(), permission.getUrlPattern());
                }
            }
            
        } catch (Exception e) {
            log.error("批量插入权限数据失败", e);
            throw new RuntimeException("批量插入权限数据失败", e);
        }
    }
}