package com.mega.platform.cloud.admin.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 基础数据访问层
 */
@Mapper
public interface AdminBaseDao {
    
    /**
     * 检查标签名称是否存在
     * 查询dic表中dic_cate_id为2008或2009的记录中是否存在指定的name
     *
     * @param tagName 标签名称
     * @return 存在的记录数量
     */
    int checkTagNameExists(@Param("tagName") String tagName);

    /**
     * 获取指定分类下的最大dic_id
     * 查询dic表中指定dic_cate_id的最大dic_id值
     *
     * @param dicCateId 字典分类ID
     * @return 最大的dic_id值，如果没有记录则返回null
     */
    Long getMaxDicIdByCateId(@Param("dicCateId") Long dicCateId);
}
