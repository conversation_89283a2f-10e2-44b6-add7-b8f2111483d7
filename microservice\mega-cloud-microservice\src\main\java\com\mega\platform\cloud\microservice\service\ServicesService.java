package com.mega.platform.cloud.microservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mega.platform.cloud.MicroServiceErrorCode;
import com.mega.platform.cloud.MicroServiceException;
import com.mega.platform.cloud.common.enums.ServiceGroupBuildActionEnum;
import com.mega.platform.cloud.common.mapper.*;
import com.mega.platform.cloud.common.utils.StringUtils;
import com.mega.platform.cloud.core.utils.JsonUtils;
import com.mega.platform.cloud.data.dto.jenkins.BaseJenkinsDTO;
import com.mega.platform.cloud.data.dto.jenkins.JenkinsTaskDTO;
import com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO;
import com.mega.platform.cloud.data.dto.microservice.BuildServicesDTO;
import com.mega.platform.cloud.data.entity.*;
import com.mega.platform.cloud.data.vo.microservice.*;
import com.mega.platform.cloud.microservice.dao.JenkinsDao;
import com.mega.platform.cloud.microservice.dao.ServicesDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mega.platform.cloud.MicroServiceErrorCode.*;
import static com.mega.platform.cloud.common.constant.CommonConstant.BYTE_0;
import static com.mega.platform.cloud.common.constant.CommonConstant.BYTE_1;
import static com.mega.platform.cloud.common.constant.MicroserviceConstants.*;

@Slf4j
@Service
public class ServicesService {

    private final JenkinsUserMapper jenkinsUserMapper;
    private final ServicesGroupMapper servicesGroupMapper;
    private final EcsServerMapper ecsServerMapper;
    private final JenkinsService jenkinsService;
    private final JenkinsServicesMapper jenkinsServicesMapper;
    private final ServicesDao servicesDao;
    private final JenkinsDao jenkinsDao;
    private final JenkinsJobTemplateParamValueMapper jenkinsJobTemplateParamValueMapper;
    private final ServicesMapper servicesMapper;
    private final MicroserviceCommonService microserviceCommonService;
    private final AdminUserMapper adminUserMapper;
    private final JenkinsJobMapper jenkinsJobMapper;

    @Autowired
    public ServicesService(JenkinsUserMapper jenkinsUserMapper, ServicesGroupMapper servicesGroupMapper, EcsServerMapper ecsServerMapper, JenkinsService jenkinsService, JenkinsServicesMapper jenkinsServicesMapper, ServicesDao servicesDao, JenkinsDao jenkinsDao,
                           JenkinsJobTemplateParamValueMapper jenkinsJobTemplateParamValueMapper, ServicesMapper servicesMapper, MicroserviceCommonService microserviceCommonService, AdminUserMapper adminUserMapper, JenkinsJobMapper jenkinsJobMapper) {
        this.jenkinsUserMapper = jenkinsUserMapper;
        this.servicesGroupMapper = servicesGroupMapper;
        this.ecsServerMapper = ecsServerMapper;
        this.jenkinsService = jenkinsService;
        this.jenkinsServicesMapper = jenkinsServicesMapper;
        this.servicesDao = servicesDao;
        this.jenkinsDao = jenkinsDao;
        this.jenkinsJobTemplateParamValueMapper = jenkinsJobTemplateParamValueMapper;
        this.servicesMapper = servicesMapper;
        this.microserviceCommonService = microserviceCommonService;
        this.adminUserMapper = adminUserMapper;
        this.jenkinsJobMapper = jenkinsJobMapper;
    }

    /**
     * 创建服务
     *
     * @param reqVO
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public CreateServicesRespVO createServices(CreateServicesReqVO reqVO) throws Exception {
        CreateServicesRespVO respVO = new CreateServicesRespVO();
        checkCreateServicesParams(reqVO);
        EcsServer ecsServer = ecsServerMapper.selectByPrimaryKey(reqVO.getTargetEcsServerId());
        ServicesGroup servicesGroup = servicesGroupMapper.selectByPrimaryKey(reqVO.getServiceGroupId());
        Boolean useJenkins = servicesGroup.getUseJenkins().equals(BYTE_1);
        JenkinsServices jenkinsServices = jenkinsServicesMapper.selectByPrimaryKey(servicesGroup.getJenkinsServicesId());
        Services services = new Services().setName(reqVO.getServicesName()).setServicesGroupId(servicesGroup.getId()).setEcsServerId(ecsServer.getId());
        servicesDao.insertServices(services);
        // 检查模板动态参数key
        Map<String, String> jenkinsParams = reqVO.getJenkinsParams();
        if (useJenkins) {
            // 0.创建jenkinsUser
            JenkinsUser jenkinsUser = jenkinsService.createJenkinsUser(jenkinsServices, adminUserMapper.selectByPrimaryKey(reqVO.getAdminUserId()));
            // 1.创建jenkinsSshServer
            JenkinsSshServer jenkinsSshServer = jenkinsService.createJenkinsSshServer(jenkinsServices, jenkinsUser, ecsServer);
            // 2.创建jenkinsView
            JenkinsView jenkinsView = jenkinsService.createJenkinsView(jenkinsServices, jenkinsUser, servicesGroup);
            // 3.创建jenkinsJob
            JenkinsJob jenkinsJob = jenkinsService.createJenkinsJob(jenkinsServices, jenkinsUser, jenkinsView, jenkinsSshServer, servicesGroup, services);
            // service参数更新
            services.setJenkinsJobId(jenkinsJob.getId());
            fillInvisibleParam(jenkinsParams, services.getId(), SERVICES_DATA_TYPE_SERVICES, reqVO.getAdminUserId());
            List<JenkinsTemplateParamDTO> templateServicesParams = checkTemplateParamKey(servicesGroup.getJenkinsTemplateId(), SERVICES_DATA_TYPE_SERVICES, jenkinsParams);
            // param入库
            for (JenkinsTemplateParamDTO templateServicesParam : templateServicesParams) {
                JenkinsJobTemplateParamValue paramValue = new JenkinsJobTemplateParamValue();
                paramValue.setJenkinsJobTempleteParamId(templateServicesParam.getJenkinsTemplateParamId());
                paramValue.setServicesDataId(services.getId());
                paramValue.setServicesDataType(SERVICES_DATA_TYPE_SERVICES);
                paramValue.setParamValue(jenkinsParams.get(templateServicesParam.getParamKey()));
                jenkinsJobTemplateParamValueMapper.insertSelective(paramValue);
            }
            servicesMapper.updateByPrimaryKeySelective(services);
        }
        respVO.setServicesId(services.getId());
        return respVO;
    }

    /**
     * 为jenkinsParams添加不可见字段值
     *
     * @param jenkinsParams
     * @param servicesDataId
     * @param serviceDataType
     * @param adminUserId
     * @throws Exception
     */
    public void fillInvisibleParam(Map<String, String> jenkinsParams, Long servicesDataId, Integer serviceDataType, Long adminUserId) throws Exception {
        if (serviceDataType.equals(SERVICES_DATA_TYPE_SERVICES_GROUP)) {
            ServicesGroup servicesGroup = servicesGroupMapper.selectByPrimaryKey(servicesDataId);
            jenkinsParams.put(JENKINS_TEMPLATE_KEY_ACTIVE_PROFILE, servicesGroup.getServicesEnv());
        } else if (serviceDataType.equals(SERVICES_DATA_TYPE_SERVICES)) {
            Services services = servicesMapper.selectByPrimaryKey(servicesDataId);
            ServicesGroup servicesGroup = servicesGroupMapper.selectByPrimaryKey(services.getServicesGroupId());
            JenkinsServices jenkinsServices = jenkinsServicesMapper.selectByPrimaryKey(servicesGroup.getJenkinsServicesId());
            JenkinsUser jenkinsUser = jenkinsService.createJenkinsUser(jenkinsServices, adminUserMapper.selectByPrimaryKey(adminUserId));
            EcsServer ecsServer = ecsServerMapper.selectByPrimaryKey(services.getEcsServerId());
            JenkinsSshServer jenkinsSshServer = jenkinsService.createJenkinsSshServer(jenkinsServices, jenkinsUser, ecsServer);
            jenkinsParams.put(JENKINS_TEMPLATE_KEY_SSH_SERVER_NAME, jenkinsSshServer.getServerName());
            jenkinsParams.put(JENKINS_TEMPLATE_KEY_ECS_SERVER_IP, ecsServer.getPrivateIp());
        }
    }

    /**
     * 更新内部模板字段
     *
     * @param reqVO
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateServicesGroupJenkinsInvisibleParam(UpdateServicesGroupJenkinsParamReqVO reqVO) throws Exception {
        ServicesGroup servicesGroup = servicesGroupMapper.selectByPrimaryKey(reqVO.getServicesGroupId());
        String servicesEnv = servicesGroup.getServicesEnv();
        updateJenkinsTemplateParamValue(servicesGroup.getJenkinsTemplateId(), JENKINS_TEMPLATE_KEY_ACTIVE_PROFILE, servicesGroup.getId(), SERVICES_DATA_TYPE_SERVICES_GROUP, servicesEnv);
    }

    /**
     * 更新内部模板字段
     *
     * @param reqVO
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateServicesJenkinsInvisibleParam(UpdateServicesJenkinsParamReqVO reqVO) throws Exception {
        Services services = servicesMapper.selectByPrimaryKey(reqVO.getServicesId());
        ServicesGroup servicesGroup = servicesGroupMapper.selectByPrimaryKey(services.getServicesGroupId());
        JenkinsServices jenkinsServices = jenkinsServicesMapper.selectByPrimaryKey(servicesGroup.getJenkinsServicesId());
        // 换了机器
        EcsServer ecsServer = ecsServerMapper.selectByPrimaryKey(services.getEcsServerId());
        // 0.创建jenkinsUser
        JenkinsUser jenkinsUser = jenkinsService.createJenkinsUser(jenkinsServices, adminUserMapper.selectByPrimaryKey(reqVO.getAdminUserId()));
        // 1.创建jenkinsSshServer
        JenkinsSshServer jenkinsSshServer = jenkinsService.createJenkinsSshServer(jenkinsServices, jenkinsUser, ecsServer);
        // 2.创建jenkinsView
        JenkinsView jenkinsView = jenkinsService.createJenkinsView(jenkinsServices, jenkinsUser, servicesGroup);
        JenkinsJob jenkinsJob = jenkinsJobMapper.selectOne(new JenkinsJob().setJenkinsServicesId(jenkinsServices.getId()).setJenkinsViewId(jenkinsView.getId()).setJenkinsSshServerId(jenkinsSshServer.getId()));
        if (jenkinsJob == null) {
            // 3.创建jenkinsJob
            jenkinsJob = jenkinsService.createJenkinsJob(jenkinsServices, jenkinsUser, jenkinsView, jenkinsSshServer, servicesGroup, services);
        }
        // service参数更新
        services.setJenkinsJobId(jenkinsJob.getId());
        updateJenkinsTemplateParamValue(servicesGroup.getJenkinsTemplateId(), JENKINS_TEMPLATE_KEY_SSH_SERVER_NAME, services.getId(), SERVICES_DATA_TYPE_SERVICES, jenkinsSshServer.getServerName());
        updateJenkinsTemplateParamValue(servicesGroup.getJenkinsTemplateId(), JENKINS_TEMPLATE_KEY_ECS_SERVER_IP, services.getId(), SERVICES_DATA_TYPE_SERVICES, ecsServer.getPrivateIp());
        servicesMapper.updateByPrimaryKeySelective(services);
    }

    /**
     * 创建服务组
     *
     * @param reqVO
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public CreateServicesGroupRespVO createServicesGroup(CreateServicesGroupReqVO reqVO) throws Exception {
        CreateServicesGroupRespVO respVO = new CreateServicesGroupRespVO();
        // 检查参数
        checkCreateServicesGroupParams(reqVO);
        // 创建服务组
        ServicesGroup servicesGroup = new ServicesGroup();
        servicesGroup.setName(reqVO.getServicesGroupName());
        servicesGroup.setProjectId(reqVO.getProjectId());
        servicesGroup.setProjectAppId(reqVO.getProjectAppId());
        servicesGroup.setServicesUpdateType(reqVO.getServiceUpdateId());
        servicesGroup.setServicesAliveNum(reqVO.getServiceAliveNum());
        servicesGroup.setJenkinsServicesId(reqVO.getJenkinsServiceId());
        servicesGroup.setJenkinsTemplateId(reqVO.getJenkinsTemplateId());
        servicesGroup.setAdminUserId(reqVO.getAdminUserId());
        servicesGroup.setServicesEnv(reqVO.getServiceEnv());
        servicesGroup.setCheckAliveType(reqVO.getCheckAliveType());
        servicesDao.insertServicesGroup(servicesGroup);
        // 检查模板动态参数key
        Map<String, String> jenkinsParams = reqVO.getJenkinsParams();
        fillInvisibleParam(jenkinsParams, servicesGroup.getId(), SERVICES_DATA_TYPE_SERVICES_GROUP, reqVO.getAdminUserId());
        List<JenkinsTemplateParamDTO> templateServicesParams = checkTemplateParamKey(reqVO.getJenkinsTemplateId(), SERVICES_DATA_TYPE_SERVICES_GROUP, jenkinsParams);
        // param入库
        for (JenkinsTemplateParamDTO templateServicesParam : templateServicesParams) {
            JenkinsJobTemplateParamValue paramValue = new JenkinsJobTemplateParamValue();
            paramValue.setJenkinsJobTempleteParamId(templateServicesParam.getJenkinsTemplateParamId());
            paramValue.setServicesDataId(servicesGroup.getId());
            paramValue.setServicesDataType(SERVICES_DATA_TYPE_SERVICES_GROUP);
            paramValue.setParamValue(jenkinsParams.get(templateServicesParam.getParamKey()));
            jenkinsJobTemplateParamValueMapper.insertSelective(paramValue);
        }
        respVO.setServicesGroupId(servicesGroup.getId());
        return respVO;
    }

    /**
     * 服务组构建
     *
     * @param buildServicesDTO
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void buildServicesGroup(BuildServicesDTO buildServicesDTO) throws Exception {
        ServicesGroup servicesGroup = servicesGroupMapper.selectByPrimaryKey(buildServicesDTO.getServicesGroupId());
        if (!microserviceCommonService.buildTryLock(buildServicesDTO.getServicesGroupId())) {
            // 获取锁失败
            throw new MicroServiceException(MicroServiceErrorCode.BUILD_JENKINS_JOB_FAILED_SERVICES_GROUP_BUILDING);
        }
        try {
            checkServicesGroupCanBuild(servicesGroup, buildServicesDTO.getCheckRunningStatus());
            ServiceGroupBuildActionEnum buildActionEnum = ServiceGroupBuildActionEnum.findByAction(buildServicesDTO.getAction());
            JenkinsUser jenkinsUser = jenkinsUserMapper.selectOne(new JenkinsUser().setJenkinsServicesId(servicesGroup.getJenkinsServicesId()).setAdminUserId(buildServicesDTO.getAdminUserId()));
            JenkinsServices jenkinsServices = jenkinsServicesMapper.selectByPrimaryKey(servicesGroup.getJenkinsServicesId());
            List<JenkinsTaskDTO> jenkinsTaskDTOS = jenkinsService.createJenkinsTaskDTO(servicesGroup, buildActionEnum);
            BaseJenkinsDTO baseDto = new BaseJenkinsDTO(jenkinsServices, jenkinsUser);
            createAndBuildJenkinsTask(servicesGroup, buildActionEnum, baseDto, jenkinsTaskDTOS);
        } catch (Exception e) {
            // 数据准备阶段出错 直接放锁
            microserviceCommonService.buildReleaseLock(buildServicesDTO.getServicesGroupId());
            throw e;
        }
    }

    /**
     * 服务构建 仅支持stop
     *
     * @param buildServicesDTO
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void buildServices(BuildServicesDTO buildServicesDTO) throws Exception {
        Services services = servicesMapper.selectByPrimaryKey(buildServicesDTO.getServicesId());
        ServicesGroup servicesGroup = servicesGroupMapper.selectByPrimaryKey(services.getServicesGroupId());
        if (servicesGroup.getUseJenkins().equals(BYTE_0)) {
            // 不用jenkins 直接return
            return;
        }
        if (!microserviceCommonService.buildTryLock(buildServicesDTO.getServicesGroupId())) {
            // 获取锁失败
            throw new MicroServiceException(MicroServiceErrorCode.BUILD_JENKINS_JOB_FAILED_SERVICES_GROUP_BUILDING);
        }
        try {
            checkServicesGroupCanBuild(servicesGroup, buildServicesDTO.getCheckRunningStatus());
            checkServicesCanBuild(services, buildServicesDTO.getCheckRunningStatus());
            ServiceGroupBuildActionEnum buildActionEnum = ServiceGroupBuildActionEnum.findByAction(buildServicesDTO.getAction());
            JenkinsUser jenkinsUser = jenkinsUserMapper.selectOne(new JenkinsUser().setJenkinsServicesId(servicesGroup.getJenkinsServicesId()).setAdminUserId(buildServicesDTO.getAdminUserId()));
            JenkinsServices jenkinsServices = jenkinsServicesMapper.selectByPrimaryKey(servicesGroup.getJenkinsServicesId());
            List<JenkinsTaskDTO> jenkinsTaskDTOS = jenkinsService.createJenkinsTaskDTO(services, buildActionEnum);
            BaseJenkinsDTO baseDto = new BaseJenkinsDTO(jenkinsServices, jenkinsUser);
            createAndBuildJenkinsTask(servicesGroup, buildActionEnum, baseDto, jenkinsTaskDTOS);
        } catch (Exception e) {
            // 数据准备阶段出错 直接放锁
            microserviceCommonService.buildReleaseLock(buildServicesDTO.getServicesGroupId());
            throw e;
        }
    }

    private void createAndBuildJenkinsTask(ServicesGroup servicesGroup, ServiceGroupBuildActionEnum buildActionEnum, BaseJenkinsDTO baseDto, List<JenkinsTaskDTO> jenkinsTaskDTOS) throws JsonProcessingException {
        if (jenkinsTaskDTOS.isEmpty()) {
            return;
        }
        // 创建jenkinsTaskGroup组
        JenkinsTaskGroup taskGroup = new JenkinsTaskGroup();
        taskGroup.setAdminUserId(baseDto.getAdminUserId());
        taskGroup.setServicesGroupId(servicesGroup.getId());
        taskGroup.setAction(buildActionEnum.getAction());
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("aliveNum", servicesGroup.getServicesAliveNum());
        taskGroup.setRequestData(JsonUtils.toJson(requestMap));
        taskGroup.setTaskNum(jenkinsTaskDTOS.size());
        jenkinsDao.insertJenkinsTaskGroup(taskGroup);
        // 创建jenkinsTask
        microserviceCommonService.logTaskGroupContent(taskGroup.getServicesGroupId(), servicesGroup.getName(), "创建任务组成功，指令: %s, 保活数量: %s", buildActionEnum.getShowStr(), servicesGroup.getServicesAliveNum());
        for (JenkinsTaskDTO taskDTO : jenkinsTaskDTOS) {
            JenkinsTask task = new JenkinsTask();
            task.setJenkinsTaskGroupId(taskGroup.getId());
            task.setJenkinsJobId(taskDTO.getJenkinsJobId());
            task.setAction(taskDTO.getRealAction());
            Map<String, String> dataMap = microserviceCommonService.getServicesDataJenkinsParamValues(taskDTO.getServicesId(), SERVICES_DATA_TYPE_SERVICES);
            dataMap.put(JENKINS_TEMPLATE_KEY_RESTART_TYPE, ServiceGroupBuildActionEnum.findByAction(taskDTO.getRealAction()).getJenkinsRestartType());
            dataMap.put(JENKINS_TEMPLATE_KEY_SERVICES_ID, taskDTO.getServicesId().toString());
            task.setRequestData(JsonUtils.toJson(dataMap));
            jenkinsDao.insertJenkinsTask(task);
            taskDTO.setJenkinsTaskId(task.getId());
            taskDTO.setJenkinsTaskGroupId(taskGroup.getId());
            taskDTO.setJenkinsTaskParams(dataMap);
            taskDTO.setServicesUpdateType(servicesGroup.getServicesUpdateType());
            taskDTO.setTaskGroupTaskNum(taskGroup.getTaskNum());
            taskDTO.setServicesGroupId(servicesGroup.getId());
            taskDTO.setServicesGroupName(servicesGroup.getName());
            taskDTO.setCheckAliveType(servicesGroup.getCheckAliveType());
            taskDTO.setTaskGroupAction(taskGroup.getAction());
            microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_IN_QUEUE);
            microserviceCommonService.logTaskGroupContent(taskDTO, "分配任务结果: 任务id: %s, servicesId: %s, 服务名: %s, 序号: %s, 实际指令: %s", taskDTO.getJenkinsTaskId(), taskDTO.getServicesId(), taskDTO.getServicesName(), taskDTO.getIndex(),
                    ServiceGroupBuildActionEnum.findByAction(taskDTO.getRealAction()).getShowStr());
        }
        microserviceCommonService.updateServicesGroupRunningStatus(servicesGroup.getId(), SERVICES_GROUP_RUNNING_STATUS_BUILDING);
        // 防止这个事务没结束就开启异步执行任务
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    jenkinsService.executeJenkinsTask(baseDto, jenkinsTaskDTOS);
                } catch (Exception e) {
                    log.error("executeJenkinsTask occur exception. taskGroupId {}", taskGroup.getId(), e);
                }
            }
        });
    }

    /**
     * 更新jenkinsParamValue
     */
    public void updateJenkinsTemplateParamValue(Long templateId, String paramKey, Long servicesDataId, Integer servicesDataType, String paramValue) {
        jenkinsDao.updateJenkinsTemplateParamValue(templateId, paramKey, servicesDataId, servicesDataType, paramValue);
    }

    /**
     * 检查模板参数是否完全
     *
     * @param jenkinsTemplateId
     * @param servicesDataType
     * @param jenkinsParams
     * @return
     */
    public List<JenkinsTemplateParamDTO> checkTemplateParamKey(Long jenkinsTemplateId, Integer servicesDataType, Map<String, String> jenkinsParams) {
        List<JenkinsTemplateParamDTO> templateServicesParams = jenkinsDao.getTemplateParamKeyListByDataType(jenkinsTemplateId, servicesDataType);
        List<String> errorParamKeys = jenkinsService.checkTemplateParamKey(templateServicesParams, jenkinsParams);
        if (!errorParamKeys.isEmpty()) {
            log.warn("checkTemplateParamKey failed, errorParamKeys {}", errorParamKeys);
            throw new MicroServiceException(CREATE_JENKINS_JOB_FAILED_SERVICES_PARAM_ERROR);
        }
        return templateServicesParams;
    }

    /**
     * 检查服务组是否可以构建任务
     *
     * @param servicesGroup
     */
    private void checkServicesGroupCanBuild(ServicesGroup servicesGroup, Boolean checkRunningStatus) {
        if (!servicesGroup.getStatus().equals(SERVICES_DATA_STATUS_ONLINE)) {
            throw new MicroServiceException(BUILD_JENKINS_JOB_FAILED_SERVICES_GROUP_NOT_ONLINE);
        }
        if (!servicesGroup.getUseJenkins().equals(BYTE_1)) {
            throw new MicroServiceException(BUILD_JENKINS_JOB_FAILED_NOT_USE_JENKINS);
        }
        if (checkRunningStatus) {
            List<Integer> canBuildRunningStatus = Arrays.asList(SERVICES_GROUP_RUNNING_STATUS_NOT_RUNNING, SERVICES_GROUP_RUNNING_STATUS_RUNNING, SERVICES_GROUP_RUNNING_STATUS_BUILD_ERROR);
            if (!canBuildRunningStatus.contains(servicesGroup.getRunningStatus())) {
                throw new MicroServiceException(BUILD_JENKINS_JOB_FAILED_SERVICES_GROUP_RUNNING_STATUS_ERROR);
            }
        }
    }

    /**
     * 检查服务是否可以构建任务
     *
     * @param services
     */
    private void checkServicesCanBuild(Services services, Boolean checkRunningStatus) {
        if (!services.getStatus().equals(SERVICES_DATA_STATUS_ONLINE)) {
            throw new MicroServiceException(BUILD_JENKINS_JOB_FAILED_SERVICES_NOT_ONLINE);
        }
        if (checkRunningStatus) {
            List<Integer> canBuildRunningStatus = Arrays.asList(SERVICES_RUNNING_STATUS_NOT_RUNNING, SERVICES_RUNNING_STATUS_RUNNING, SERVICES_RUNNING_STATUS_BUILD_ERROR);
            if (!canBuildRunningStatus.contains(services.getRunningStatus())) {
                throw new MicroServiceException(BUILD_JENKINS_JOB_FAILED_SERVICES_RUNNING_STATUS_ERROR);
            }
        }

    }

    /**
     * 创建服务组参数检查
     *
     * @param reqVO
     */
    private void checkCreateServicesGroupParams(CreateServicesGroupReqVO reqVO) {
        // 应用内组名不能重复
        int count = servicesGroupMapper.selectCount(new ServicesGroup().setProjectId(reqVO.getProjectId()).setName(reqVO.getServicesGroupName()).setDelsign(BYTE_0));
        if (count > 0) {
            throw new MicroServiceException(CREATE_SERVICES_GROUP_FAILED_NAME_DUPLICATE);
        }
    }

    /**
     * 创建服务参数检查
     *
     * @param reqVO
     */
    private void checkCreateServicesParams(CreateServicesReqVO reqVO) {
        // 组内服务名不能重复
        int count = servicesMapper.selectCount(new Services().setServicesGroupId(reqVO.getServiceGroupId()).setName(reqVO.getServicesName()).setDelsign(BYTE_0));
        if (count > 0) {
            throw new MicroServiceException(CREATE_SERVICES_FAILED_NAME_DUPLICATE);
        }
        String port = reqVO.getJenkinsParams().get(JENKINS_TEMPLATE_KEY_PORT);
        if (StringUtils.isNotEmpty(port)) {
            // 全局不能相同机器相同端口号
            count = servicesDao.getSameServicesNumByEcsServerIdAndPort(reqVO.getTargetEcsServerId(), Integer.valueOf(port));
            if (count > 0) {
                throw new MicroServiceException(CREATE_SERVICES_FAILED_SAME_ECS_PORT);
            }
        }
    }

    /**
     * 启动重新加载未完成的发布任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void reloadUncompletedJenkinsTask() {
        List<ServicesGroup> uncompletedServicesGroups = servicesGroupMapper.select(new ServicesGroup().setDelsign(BYTE_0).setRunningStatus(SERVICES_RUNNING_STATUS_BUILDING));
        for (ServicesGroup servicesGroup : uncompletedServicesGroups) {
            try {
                log.info("reloadUncompletedJenkinsTask servicesGroupId {}", servicesGroup.getId());
                BuildServicesDTO buildServicesDTO = new BuildServicesDTO();
                JenkinsTaskGroup taskGroup = jenkinsDao.getLastJenkinsTaskGroupByServicesGroupId(servicesGroup.getId());
                buildServicesDTO.setServicesGroupId(servicesGroup.getId());
                buildServicesDTO.setAction(taskGroup.getAction());
                buildServicesDTO.setAdminUserId(taskGroup.getAdminUserId());
                buildServicesDTO.setCheckRunningStatus(false);
                this.buildServicesGroup(buildServicesDTO);
            } catch (Exception e) {
                log.error("reloadUncompletedJenkinsTask error, servicesGroupId {}", servicesGroup.getId(), e);
            }
        }
    }

    /**
     * 回收jenkinsJob资源
     *
     * @param servicesId
     * @param adminUserId
     * @throws Exception
     */
    public void deleteServicesJenkinsJob(Long servicesId, Long adminUserId) throws Exception {
        Services services = servicesMapper.selectByPrimaryKey(servicesId);
        jenkinsService.deleteJenkinsJob(services, adminUserId, true);
    }

    /**
     * 回收jenkinsJob资源
     *
     * @param servicesGroupId
     * @param adminUserId
     * @throws Exception
     */
    public void deleteServicesGroupJenkinsJob(Long servicesGroupId, Long adminUserId) throws Exception {
        List<Services> servicesList = servicesMapper.select(new Services().setServicesGroupId(servicesGroupId).setDelsign(BYTE_0));
        for (Services services : servicesList) {
            jenkinsService.deleteJenkinsJob(services, adminUserId, false);
        }
    }
}
