package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * App权限更新请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel("App权限更新请求参数")
public class AdminAppPermissionUpdateReqVO {

    /**
     * 应用项目ID
     */
    @NotNull(message = "应用项目ID不能为空")
    @ApiModelProperty(value = "应用项目ID", required = true, example = "123")
    private Long appProjectId;

    /**
     * URL模式
     */
    @NotBlank(message = "URL模式不能为空")
    @ApiModelProperty(value = "URL模式", required = true, example = "/api/user/*")
    private String urlPattern;

    /**
     * 删除标识：0=未删除，1=已删除
     */
    @NotNull(message = "删除标识不能为空")
    @ApiModelProperty(value = "删除标识：0=未删除，1=已删除", required = true, example = "0")
    private Integer delsign;
}
