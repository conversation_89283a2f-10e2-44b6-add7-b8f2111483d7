package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - 系统项目编辑 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminSystemProjectEditReqVO extends AdminProjectEditReqVO {

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull(message = "项目ID不能为空")
    private Long projectId;
}