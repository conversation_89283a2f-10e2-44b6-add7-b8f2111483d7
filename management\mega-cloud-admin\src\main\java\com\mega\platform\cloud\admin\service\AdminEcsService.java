package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.constant.AdminServerEnum;
import com.mega.platform.cloud.admin.dao.AdminEcsDao;
import com.mega.platform.cloud.admin.dao.AdminEcsConfigDao;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.admin.vo.AdminEcsConfigListRespVO.EcsConfigItemVO;
import com.mega.platform.cloud.common.mapper.EcsServerMapper;
import com.mega.platform.cloud.data.entity.EcsServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * ECS管理服务
 */
@Service
@Slf4j
public class AdminEcsService {

    private final AdminEcsDao adminEcsDao;
    private final AdminEcsConfigDao adminEcsConfigDao;
    private final EcsServerMapper ecsServerMapper;
    private final AdminAliEcsService adminAliEcsService;

    @Autowired
    public AdminEcsService(AdminEcsDao adminEcsDao, AdminEcsConfigDao adminEcsConfigDao, EcsServerMapper ecsServerMapper, AdminAliEcsService adminAliEcsService) {
        this.adminEcsDao = adminEcsDao;
        this.adminEcsConfigDao = adminEcsConfigDao;
        this.ecsServerMapper = ecsServerMapper;
        this.adminAliEcsService = adminAliEcsService;
    }


    /**
     * 查询ECS列表
     *
     * @param projectId 项目ID
     * @param reqVO     查询条件
     * @return ECS列表
     */
    public AdminEcsListRespVO getEcsList(String projectId, AdminEcsListReqVO reqVO) {
        log.info("查询ECS列表，projectId: {}, reqVO: {}", projectId, reqVO);

        // 设置项目ID
        if (StringUtils.isNotBlank(projectId)) {
            reqVO.setProjectId(projectId);
        }

        // 查询ECS列表
        List<AdminEcsListRespVO.EcsServerVO> ecsList = adminEcsDao.selectEcsListByCondition(reqVO);

        // 构造响应对象
        AdminEcsListRespVO respVO = new AdminEcsListRespVO();
        respVO.setEcsList(ecsList);

        log.info("查询ECS列表完成，返回{}条记录", ecsList.size());
        return respVO;
    }

    /**
     * 查询ECS配置列表
     *
     * @param projectId 项目ID
     * @param reqVO     查询条件
     * @return ECS配置列表
     */
    public AdminEcsConfigListRespVO getEcsConfigList(String projectId, AdminEcsConfigListReqVO reqVO) {
        log.info("查询ECS配置列表，projectId: {}, reqVO: {}", projectId, reqVO);

        // 设置项目ID
        if (StringUtils.isNotBlank(projectId)) {
            reqVO.setProjectId(projectId);
        }

        // 查询ECS配置列表
        List<AdminEcsConfigListRespVO.EcsConfigItemVO> configList = adminEcsConfigDao.selectEcsConfigListByCondition(reqVO);

        // 构造响应对象
        AdminEcsConfigListRespVO respVO = new AdminEcsConfigListRespVO();
        respVO.setConfigList(configList);

        log.info("查询ECS配置列表完成，返回{}条记录", configList.size());
        return respVO;
    }

    /**
     * 创建ECS实例
     *
     * @param reqVO       创建请求
     * @param adminUserId 管理员ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void createEcs(AdminEcsCreateReqVO reqVO, Long adminUserId) {
        log.info("创建ECS实例，projectId: {}, reqVO: {}, adminUserId: {}", reqVO.getProjectId(), reqVO, adminUserId);

        try {
            // 查询ECS配置
            AdminEcsConfigListReqVO req = new AdminEcsConfigListReqVO();
            req.setEcsConfigId(reqVO.getEcsConfigId());
            List<AdminEcsConfigListRespVO.EcsConfigItemVO> configList = adminEcsConfigDao.selectEcsConfigListByCondition(req);
            if (configList == null || configList.isEmpty()) {
                throw new AdminException(0, "ECS配置不存在");
            }
            AdminEcsConfigListRespVO.EcsConfigItemVO config = configList.get(0);
            // 2 调用云服务商API创建ECS实例
            String instanceId = adminAliEcsService.createEcs(config, reqVO.getName());

            // 3 构建ECS实体
            EcsServer ecsServer = new EcsServer();
            ecsServer.setInstanceId(instanceId);
            ecsServer.setStatus(2); // 正在创建
            ecsServer.setEcsType(config.getEcsType().byteValue());
            ecsServer.setEcsServerConfigId(config.getId());
            ecsServer.setEcsServerImageId(config.getEcsServerImageId());
            ecsServer.setProjectId(reqVO.getProjectId() != null ? Long.parseLong(reqVO.getProjectId()) : config.getProjectId());
            ecsServer.setProjectAppId(reqVO.getProjectAppId() != null ? Long.parseLong(reqVO.getProjectAppId()) : config.getProjectAppId());
            ecsServer.setName(reqVO.getName());
            ecsServer.setRemark(reqVO.getRemark());
            ecsServer.setDelsign((byte) 0);

            // 插入数据库记录
            int result = ecsServerMapper.insertSelective(ecsServer);
            if (result <= 0) {
                throw new AdminException(0, "ECS创建失败");
            }
            // 4 插入tag表
            List<String> tagIdList = new ArrayList<>();
            // 4.1 处理普通tag
            if (StringUtils.isNotBlank(reqVO.getTagList())) {
                tagIdList.addAll(Arrays.asList(reqVO.getTagList().split(",")));
            }
            // 4.2 处理项目tag
            if (StringUtils.isNotBlank(reqVO.getCustomTagList())) {
                tagIdList.addAll(Arrays.asList(reqVO.getCustomTagList().split(",")));
            }

            // 5 插入tag关系
            if (!tagIdList.isEmpty()) {
                adminEcsDao.insertEcsTags(ecsServer.getId(), tagIdList);
            }

            log.info("ECS创建成功，ID: {}", ecsServer.getId());

        } catch (Exception e) {
            log.error("创建ECS失败", e);
            throw new AdminException(0, "创建ECS失败: " + e.getMessage());
        }
    }

    /**
     * 删除ECS实例
     *
     * @param reqVO       删除请求
     * @param adminUserId 管理员ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteEcs(AdminEcsDeleteReqVO reqVO, Long adminUserId) {
        log.info("删除ECS实例，projectId: {}, reqVO: {}, adminUserId: {}", reqVO.getProjectId(), reqVO, adminUserId);

        // 查询ECS详情
        AdminEcsListReqVO req = new AdminEcsListReqVO();
        req.setEcsServerId(reqVO.getEcsServerId());
        List<AdminEcsListRespVO.EcsServerVO> ecsDetailList = adminEcsDao.selectEcsListByCondition(req);
        if (ecsDetailList == null || ecsDetailList.isEmpty()) {
            throw new AdminException(0, "ECS不存在");
        }
        AdminEcsListRespVO.EcsServerVO ecsDetail = ecsDetailList.get(0);

        // 检查是否有运行中的微服务
        int runningServicesCount = adminEcsDao.countRunningServicesByEcsId(ecsDetail.getId());
        if (runningServicesCount > 0) {
            throw new AdminException(0, "ECS上还有运行中的微服务，无法删除");
        }

        adminEcsDao.updateEcsStatusById(reqVO.getEcsServerId(), AdminServerEnum.ServerStatus.PENDING_SHUTDOWN.getCode());
        log.info("ECS更新为待关闭状态，ID: {}", reqVO.getEcsServerId());
    }

    /**
     * ECS按需转按月
     *
     * @param reqVO       请求参数
     * @param adminUserId 管理员ID
     */
    public void changeToPeriod(AdminEcsReqVO reqVO, Long adminUserId) {
        log.info("ECS按需转按月，reqVO: {}, adminUserId: {}", reqVO, adminUserId);
        // 查询ECS详情
        AdminEcsListReqVO req = new AdminEcsListReqVO();
        req.setEcsServerId(reqVO.getEcsServerId());
        req.setProjectId(req.getProjectId());
        List<AdminEcsListRespVO.EcsServerVO> ecsDetailList = adminEcsDao.selectEcsListByCondition(req);
        if (ecsDetailList == null || ecsDetailList.isEmpty()) {
            throw new AdminException(0, "ECS不存在");
        }
        AdminEcsListRespVO.EcsServerVO ecsDetail = ecsDetailList.get(0);

        // 调用阿里云服务进行按需转按月
        adminAliEcsService.changeToPeriod(ecsDetail.getInstanceId(), ecsDetail.getRegionStr());
        // 更新数据库
        adminEcsDao.updateEcsChargeType(reqVO.getEcsServerId(), "PrePaid", 1);
        log.info("ECS按需转按月成功，ID: {}", reqVO.getEcsServerId());

    }

    /**
     * ECS按月转自动续费
     *
     * @param reqVO       请求参数
     * @param adminUserId 管理员ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeToAutoRenew(AdminEcsReqVO reqVO, Long adminUserId) {
        log.info("ECS按月转自动续费，reqVO: {}, adminUserId: {}", reqVO, adminUserId);

        // 查询ECS详情
        AdminEcsListReqVO req = new AdminEcsListReqVO();
        req.setEcsServerId(reqVO.getEcsServerId());
        req.setProjectId(req.getProjectId());
        List<AdminEcsListRespVO.EcsServerVO> ecsDetailList = adminEcsDao.selectEcsListByCondition(req);
        if (ecsDetailList == null || ecsDetailList.isEmpty()) {
            throw new AdminException(0, "ECS不存在");
        }
        AdminEcsListRespVO.EcsServerVO ecsDetail = ecsDetailList.get(0);

        // 调用阿里云服务进行按月转自动续费
        adminAliEcsService.changeToAutoRenew(ecsDetail.getInstanceId(), ecsDetail.getRegionStr());
        // 更新数据库
        adminEcsDao.updateEcsChargeType(reqVO.getEcsServerId(), "PrePaid", 12);
        log.info("ECS按月转自动续费成功，ID: {}", reqVO.getEcsServerId());
    }


}