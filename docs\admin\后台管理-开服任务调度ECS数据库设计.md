# 后台管理-开服任务调度ECS数据库设计

## 1. 概述

### 1.1 设计目标
本文档详细描述游戏开服任务调度系统的数据库设计，包括实体关系图、表结构定义和索引设计。


## 2. 实体关系图

### 2.1 核心实体ER图

## 3. 表结构设计

### 3.1 游戏开服主任务表 (game_launch_task_group)

**功能描述**：存储游戏开服的主任务信息，包括任务配置、执行状态和时间信息。

```sql
CREATE TABLE `game_launch_task_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) DEFAULT NULL,
  `admin_user_id` bigint(20) DEFAULT NULL,
  `region_id` bigint(20) DEFAULT NULL,
  `task_config_group_id` bigint(20) DEFAULT NULL,
  `origin_req_json` json DEFAULT NULL COMMENT '原始请求参数',
  `status` int(4) DEFAULT NULL COMMENT '-1-异常 1-发布中 2-发布完成',
  `message` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `failed_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `project_id` (`project_id`,`region_id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

```

**字段说明**：
- `task_config_group_id`：存储JSON格式的任务配置，包括MySQL、Redis、微服务的配置信息
- `task_status`：任务状态枚举值，便于状态过滤和统计
- `origin_req_json`：原始请求JSON数据，用于记录任务创建时的参数
- `total_sub_tasks`、`completed_sub_tasks`：用于计算任务进度

### 3.2 游戏开服主任务标记表 game_launch_task_group_config

```sql
CREATE TABLE `game_launch_task_group_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

```

### 3.3 主任务关联子任务表  game_launch_task_group_task_config
```sql
CREATE TABLE `game_launch_task_group_task_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_group_id` bigint(20) DEFAULT NULL,
  `task_id` bigint(20) DEFAULT NULL,
  `next_task_id` bigint(20) DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```
用来描述主任务有哪些子任务 


### 3.4 游戏开服子任务表 (game_launch_task)

**功能描述**：存储开服任务的子任务信息，支持MySQL、Redis、微服务等不同类型的子任务。

```sql
CCREATE TABLE `game_launch_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_group_id` bigint(20) DEFAULT NULL,
  `origin_req_json` json DEFAULT NULL COMMENT '原始请求参数',
  `result_json` json DEFAULT NULL,
  `status` int(4) DEFAULT NULL COMMENT '-1-异常 0-异常中断 1-执行中 2-执行完成',
  `failed_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**字段说明**：
- `sub_task_type`：1-MySQL，2-Redis，3-微服务，支持后续扩展新类型
- `task_result`：存储任务执行结果，如创建的ECS实例ID、连接信息等
- `retry_count`：重试机制支持，记录已重试次数

### 3.5 扩展ECS服务器配置表 (ecs_server_config)

**功能描述**：存储不同类型服务器的ECS配置模板，支持MySQL、Redis、微服务的差异化配置。

```sql
CREATE TABLE `ecs_server_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `server_type` int(11) NOT NULL COMMENT '服务器类型：1-MySQL，2-Redis，3-微服务',
  `instance_type` varchar(50) NOT NULL COMMENT '实例规格（如ecs.g6.large）',
  `region_id` varchar(50) NOT NULL COMMENT '地域ID',
  `zone_id` varchar(50) DEFAULT NULL COMMENT '可用区ID',
  `system_disk_size` int(11) DEFAULT '40' COMMENT '系统盘大小（GB）',
  `data_disk_size` int(11) DEFAULT NULL COMMENT '数据盘大小（GB）',
  `init_script` text COMMENT '初始化脚本',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_server_type` (`server_type`),
  KEY `idx_region_id` (`region_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ECS服务器配置表';
```

### 3.6 ECS服务器镜像表 (game_launch_project_config)

**功能描述**：管理不同类型服务的镜像配置，支持MySQL、Redis、微服务的专用镜像。

```sql
CREATE TABLE `ecs_server_image` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `image_name` varchar(100) NOT NULL COMMENT '镜像名称',
  `image_type` int(11) NOT NULL COMMENT '镜像类型：1-MySQL镜像，2-Redis镜像，3-微服务镜像',
  `image_id` varchar(100) NOT NULL COMMENT '阿里云镜像ID',
  `os_type` varchar(20) DEFAULT 'linux' COMMENT '操作系统类型',
  `software_list` text COMMENT '预装软件列表（JSON格式）',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_image_type` (`image_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ECS服务器镜像表';
```

### 3.7 服务实例集群表 ecs_server_cluster

```sql
CREATE TABLE `ecs_server_cluster` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cluster_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '如果是新建取 {type_task_id}，如果是迁移，自己定义一个',
  `ecs_server_id` bigint(20) DEFAULT NULL,
  `role` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色master或者slave',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ecs_type` tinyint(4) DEFAULT '0' COMMENT '1=mysql  2=redis 3=microservice 4=nginx',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) DEFAULT '0' COMMENT '删除标志位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


```

### 3.8 项目开新区配置表 game_launch_project_config

```sql
CREATE TABLE `game_launch_project_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) DEFAULT NULL,
  `task_group_config_id` bigint(20) DEFAULT NULL,
  `mysql_db_name_prefix` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'mysql库名前缀',
  `mysql_source_data_contain_tables` json DEFAULT NULL COMMENT '要进行数据复制的表 jsonArray',
  `mysql_ecs_config_id` bigint(20) DEFAULT NULL,
  `mysql_ecs_image_id` bigint(20) DEFAULT NULL,
  `redis_ecs_config_id` bigint(20) DEFAULT NULL,
  `redis_ecs_image_id` bigint(20) DEFAULT NULL,
  `services_ecs_config_id` bigint(20) DEFAULT NULL,
  `services_ecs_image_id` bigint(20) DEFAULT NULL,
  `launch_services_group_ids` json DEFAULT NULL COMMENT '服务组jsonArray',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


```


## 5. 数据字典

### 5.1 状态枚举定义

**任务状态 (task_status)**：
- 0：待处理
- 1：进行中
- 2：成功
- 3：失败
- 4：已取消

**子任务类型 (sub_task_type)**：
- 1：MySQL数据库
- 2：Redis缓存
- 3：微服务

**服务器类型 (server_type)**：
- 1：MySQL服务器
- 2：Redis服务器
- 3：微服务服务器

**区服状态 (status)**：
- 0：创建中
- 1：正常运行
- 2：维护中
- 3：已关闭

### 5.2 JSON字段结构

**任务配置 (task_config)**：
```json
{
  "mysqlConfig": {
    "useExisting": false,
    "configId": 1,
    "instanceType": "ecs.g6.large"
  },
  "redisConfig": {
    "useExisting": false,
    "configId": 2,
    "clusterMode": true
  },
  "microserviceConfig": {
    "configId": 3,
    "replicas": 2
  }
}
```

**连接信息 (connection_info)**：
```json
{
  "host": "*************",
  "port": 3306,
  "username": "game_user",
  "password": "encrypted_password",
  "database": "game_db_s001"
}
```

