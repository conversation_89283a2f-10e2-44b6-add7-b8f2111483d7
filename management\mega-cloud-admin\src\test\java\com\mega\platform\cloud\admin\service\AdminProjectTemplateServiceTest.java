package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.admin.dao.AdminProjectTemplateDao;
import com.mega.platform.cloud.admin.vo.AdminAppCreateReqVO;
import com.mega.platform.cloud.admin.vo.AdminAppRespVO;
import com.mega.platform.cloud.common.mapper.ProjectAppPermissionMapper;
import com.mega.platform.cloud.common.mapper.ProjectAppPermissionTemplateMapper;
import com.mega.platform.cloud.common.mapper.ProjectAppTemplateMapper;
import com.mega.platform.cloud.data.entity.ProjectAppPermission;
import com.mega.platform.cloud.data.entity.ProjectAppPermissionTemplate;
import com.mega.platform.cloud.data.entity.ProjectAppTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * AdminProjectTemplateService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class AdminProjectTemplateServiceTest {

    @Mock
    private AdminAppService adminAppService;

    @Mock
    private AdminProjectTemplateDao adminProjectTemplateDao;

    @Mock
    private ProjectAppTemplateMapper projectAppTemplateMapper;

    @Mock
    private ProjectAppPermissionTemplateMapper projectAppPermissionTemplateMapper;

    @Mock
    private ProjectAppPermissionMapper projectAppPermissionMapper;

    @InjectMocks
    private AdminProjectTemplateService adminProjectTemplateService;

    private Long testProjectId;
    private List<ProjectAppTemplate> mockAppTemplates;
    private List<ProjectAppPermissionTemplate> mockPermissionTemplates;
    private List<AdminAppRespVO> mockCreatedApps;

    @BeforeEach
    void setUp() {
        testProjectId = 100L;

        // 模拟应用模板数据
        mockAppTemplates = Arrays.asList(
                createAppTemplate(1, "BASE", "用于挂在无固定分类服务"),
                createAppTemplate(2, "ACCOUNT", "用于用户登录注册验证"),
                createAppTemplate(3, "PUSH", "用于用户推送服务")
        );

        // 模拟权限模板数据
        mockPermissionTemplates = Arrays.asList(
                createPermissionTemplate(1, "BASE", "/access/api/app/**"),
                createPermissionTemplate(2, "BASE", "/auth/api/verification/**"),
                createPermissionTemplate(3, "ACCOUNT", "/access/api/app/**"),
                createPermissionTemplate(4, "ACCOUNT", "/auth/api/verification/**"),
                createPermissionTemplate(5, "PUSH", "/push/api/push/**")
        );

        // 模拟创建的应用响应
        mockCreatedApps = Arrays.asList(
                createAppRespVO(1001L, "BASE"),
                createAppRespVO(1002L, "ACCOUNT"),
                createAppRespVO(1003L, "PUSH")
        );
    }

    @Test
    void testCreateTemplateApp_Success() {
        // 准备Mock行为
        when(adminProjectTemplateDao.selectAllAppTemplates()).thenReturn(mockAppTemplates);
        when(adminAppService.createApp(anyLong(), any(AdminAppCreateReqVO.class)))
                .thenReturn(mockCreatedApps.get(0), mockCreatedApps.get(1), mockCreatedApps.get(2));
        when(adminProjectTemplateDao.selectPermissionTemplatesByAppNames(anyList()))
                .thenReturn(mockPermissionTemplates);
        // 批量插入返回插入成功的记录数
        when(adminProjectTemplateDao.batchInsertAppPermissions(anyList()))
                .thenReturn(5);

        // 执行测试方法
        assertDoesNotThrow(() -> adminProjectTemplateService.createTemplateApp(testProjectId));

        // 验证调用次数和参数
        verify(adminProjectTemplateDao, times(1)).selectAllAppTemplates();
        verify(adminAppService, times(3)).createApp(anyLong(), any(AdminAppCreateReqVO.class));
        verify(adminProjectTemplateDao, times(1)).selectPermissionTemplatesByAppNames(anyList());
        // 验证批量插入被调用一次，而不是循环插入
        verify(adminProjectTemplateDao, times(1)).batchInsertAppPermissions(anyList());

        // 验证createApp的调用参数
        ArgumentCaptor<AdminAppCreateReqVO> createReqCaptor = ArgumentCaptor.forClass(AdminAppCreateReqVO.class);
        verify(adminAppService, times(3)).createApp(eq(testProjectId), createReqCaptor.capture());
        
        List<AdminAppCreateReqVO> capturedReqs = createReqCaptor.getAllValues();
        assertEquals("BASE", capturedReqs.get(0).getName());
        assertEquals("ACCOUNT", capturedReqs.get(1).getName());
        assertEquals("PUSH", capturedReqs.get(2).getName());

        // 验证批量插入的参数
        ArgumentCaptor<List<ProjectAppPermission>> permissionListCaptor = ArgumentCaptor.forClass(List.class);
        verify(adminProjectTemplateDao, times(1)).batchInsertAppPermissions(permissionListCaptor.capture());
        
        List<ProjectAppPermission> capturedPermissions = permissionListCaptor.getValue();
        assertEquals(5, capturedPermissions.size());
        
        // 验证权限数据的正确性
        assertEquals(Long.valueOf(1001L), capturedPermissions.get(0).getProjectAppId()); // BASE app
        assertEquals("/access/api/app/**", capturedPermissions.get(0).getUrlPattern());
    }

    @Test
    void testCreateTemplateApp_NullProjectId() {
        // 测试空项目ID
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class, 
                () -> adminProjectTemplateService.createTemplateApp(null)
        );
        assertEquals("项目ID不能为空", exception.getMessage());

        // 验证没有任何DAO调用
        verify(adminProjectTemplateDao, never()).selectAllAppTemplates();
        verify(adminAppService, never()).createApp(anyLong(), any());
    }

    @Test
    void testCreateTemplateApp_NoTemplates() {
        // 模拟没有应用模板的情况
        when(adminProjectTemplateDao.selectAllAppTemplates()).thenReturn(Arrays.asList());

        // 执行测试方法
        assertDoesNotThrow(() -> adminProjectTemplateService.createTemplateApp(testProjectId));

        // 验证只调用了查询模板，没有后续操作
        verify(adminProjectTemplateDao, times(1)).selectAllAppTemplates();
        verify(adminAppService, never()).createApp(anyLong(), any());
        verify(adminProjectTemplateDao, never()).batchInsertAppPermissions(anyList());
    }

    @Test
    void testCreateTemplateApp_CreateAppFailed() {
        // 模拟应用创建失败
        when(adminProjectTemplateDao.selectAllAppTemplates()).thenReturn(mockAppTemplates);
        when(adminAppService.createApp(anyLong(), any(AdminAppCreateReqVO.class)))
                .thenThrow(new RuntimeException("创建应用失败"));

        // 由于我们的实现是继续创建其他应用而不是抛出异常，所以应该不抛出异常
        assertDoesNotThrow(() -> adminProjectTemplateService.createTemplateApp(testProjectId));

        // 验证查询模板被调用了
        verify(adminProjectTemplateDao, times(1)).selectAllAppTemplates();
        // 验证尝试创建了应用（第一个就失败了）
        verify(adminAppService, times(1)).createApp(anyLong(), any());
    }

    @Test
    void testCreateTemplateApp_DatabaseException() {
        // 模拟数据库异常
        when(adminProjectTemplateDao.selectAllAppTemplates())
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(
                RuntimeException.class,
                () -> adminProjectTemplateService.createTemplateApp(testProjectId)
        );
        
        assertTrue(exception.getMessage().contains("查询应用模板失败"));
    }

    @Test
    void testBatchInsertPermissions_Success() {
        // 模拟批量插入成功的情况
        when(adminProjectTemplateDao.selectAllAppTemplates()).thenReturn(mockAppTemplates);
        when(adminAppService.createApp(anyLong(), any(AdminAppCreateReqVO.class)))
                .thenReturn(mockCreatedApps.get(0), mockCreatedApps.get(1), mockCreatedApps.get(2));
        when(adminProjectTemplateDao.selectPermissionTemplatesByAppNames(anyList()))
                .thenReturn(mockPermissionTemplates);
        when(adminProjectTemplateDao.batchInsertAppPermissions(anyList()))
                .thenReturn(5); // 批量插入成功返回5条记录

        // 执行测试
        assertDoesNotThrow(() -> adminProjectTemplateService.createTemplateApp(testProjectId));

        // 验证批量插入被调用一次，传入的参数包含5条权限记录
        ArgumentCaptor<List<ProjectAppPermission>> captor = ArgumentCaptor.forClass(List.class);
        verify(adminProjectTemplateDao, times(1)).batchInsertAppPermissions(captor.capture());
        
        List<ProjectAppPermission> capturedPermissions = captor.getValue();
        assertEquals(5, capturedPermissions.size());
        
        // 验证数据正确性
        assertNotNull(capturedPermissions.get(0).getProjectAppId());
        assertNotNull(capturedPermissions.get(0).getUrlPattern());
        assertNotNull(capturedPermissions.get(0).getCreateTime());
        assertNotNull(capturedPermissions.get(0).getUpdateTime());
        assertEquals((byte) 0, capturedPermissions.get(0).getDelsign());
    }

    @Test
    void testBatchInsertPermissions_Failed() {
        // 模拟批量插入失败的情况
        when(adminProjectTemplateDao.selectAllAppTemplates()).thenReturn(mockAppTemplates);
        when(adminAppService.createApp(anyLong(), any(AdminAppCreateReqVO.class)))
                .thenReturn(mockCreatedApps.get(0), mockCreatedApps.get(1), mockCreatedApps.get(2));
        when(adminProjectTemplateDao.selectPermissionTemplatesByAppNames(anyList()))
                .thenReturn(mockPermissionTemplates);
        when(adminProjectTemplateDao.batchInsertAppPermissions(anyList()))
                .thenThrow(new RuntimeException("数据库插入失败"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(
                RuntimeException.class,
                () -> adminProjectTemplateService.createTemplateApp(testProjectId)
        );
        
        assertTrue(exception.getMessage().contains("批量插入权限数据失败"));
        
        // 验证批量插入被调用了
        verify(adminProjectTemplateDao, times(1)).batchInsertAppPermissions(anyList());
    }

    // ===== 辅助方法 =====

    private ProjectAppTemplate createAppTemplate(Integer id, String appName, String remark) {
        ProjectAppTemplate template = new ProjectAppTemplate();
        template.setId(id);
        template.setAppName(appName);
        template.setRemark(remark);
        template.setCreateTime(new Date());
        template.setUpdateTime(new Date());
        template.setDelsign((byte) 0);
        return template;
    }

    private ProjectAppPermissionTemplate createPermissionTemplate(Integer id, String appName, String urlPattern) {
        ProjectAppPermissionTemplate template = new ProjectAppPermissionTemplate();
        template.setId(id);
        template.setAppName(appName);
        template.setUrlPattern(urlPattern);
        template.setCreateTime(new Date());
        template.setUpdateTime(new Date());
        template.setDelsign((byte) 0);
        return template;
    }

    private AdminAppRespVO createAppRespVO(Long id, String name) {
        AdminAppRespVO respVO = new AdminAppRespVO();
        respVO.setId(id);
        respVO.setName(name);
        respVO.setStatus(1);
        return respVO;
    }
}