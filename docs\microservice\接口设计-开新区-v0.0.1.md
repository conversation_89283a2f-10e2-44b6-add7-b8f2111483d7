

## 开新区配置

### 开新区配置查询
`/admin/api/{projectId}/microservice/region/config/detail`

### 创建开新区配置
#### 接口
`/admin/api/{projectId}/microservice/region/config/create`
#### 字段
- MySQL资源池配置(额外的后台管理体系)
- MySQL指定的ECS配置
- Redis资源池配置(额外的后台管理体系)
- Redis指定的ECS配置
- 服务组配置列表
- 服务组ECS配置(额外的后台管理体系)
- 版本

### 编辑开新区配置
#### 接口
`/admin/api/{projectId}/microservice/region/config/edit`
#### 字段
- 同创建

## 开新区任务
### 开新区任务查询
#### 接口
`/admin/api/{projectId}/microservice/region/job/list`
#### 排序
- 时间降序


### 开新区任务创建 
#### 接口
`/admin/api/{projectId}/microservice/region/job/create`
#### 字段
- 区服ID
- MySQL
  - 是否创建新的MySQL的ECS
  - MySQL的ECS资源ID(不开新的MySQL的ECS时)
- Redis
  - 是否创建新的Redis的ECS
  - Redis的ECS资源ID(不开新的Redis的ECS时)
- 服务组
  - 是否创建新的ECS
    [
    - ECS配置ID
    - 数量
      ]
  - 服务组1和ECS关联
    - 新ECS选择|ECS资源ID
  - 服务组2和ECS关联
    - 新ECS选择|ECS资源ID
  - 服务组n和ECS关联
    - 新ECS选择|ECS资源ID


## 任务进度
### 任务日志
#### 接口
`/admin/api/{projectId}/microservice/region/job/log`
#### 条件
- regionJobId
#### 返回子段
- 子任务数组
  - 子任务名称
  - 子任务日志

### 任务进度
#### 接口
`/admin/api/{projectId}/microservice/region/job/process`
#### 条件
- regionJobId
#### 返回字段
- 子任务数组
  - 完成步骤名称
  - 状态


### 任务定时器
1. MySQl
   1. 创建MySQL的ECS
   2. 监控ECS创建进度(状态: ECS开始创建，ECS启动成功，ECS购买失败，ECS启动失败)
   3. MySQL服务部署(状态: 部署中， 部署成功，部署失败)
   4. MySQL区服数据库创建和数据库接口数据同步
> 用资源池中MySQL服务只需要监控步骤1.4
2. Redis
   1. 创建Redis的ECS
   2. 监控ECS创建进度(状态: ECS开始创建，ECS启动成功，ECS购买失败，ECS启动失败)
   3. Redis集群服务部署(状态: 部署中， 部署成功，部署失败)
> 用资源池中Redis服务，无此步骤
3. 服务组1
    1. 创建服务组的ECS
    2. 监控ECS创建进度(状态: ECS开始创建，ECS启动成功，ECS购买失败，ECS启动失败)
    3. Docker服务部署(状态: 部署中， 部署成功，部署失败)
    4. 微服务创建
    5. 启动微服组
    6. 监控微服务组状态
> 用资源池中Services服务只需要监控步骤[3.4-3.6]
4. 服务组2
    1. 同微服务组1
5. 服务组n
    1. 同微服务组1