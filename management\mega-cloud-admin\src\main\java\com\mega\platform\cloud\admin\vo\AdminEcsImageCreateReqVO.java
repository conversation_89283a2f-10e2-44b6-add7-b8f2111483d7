package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * ECS镜像创建请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel("ECS镜像创建请求参数")
public class AdminEcsImageCreateReqVO {
    @ApiModelProperty("项目ID")
    private Long projectId;

    @ApiModelProperty("镜像名称")
    private String name;

    @NotNull(message = "ECS服务器ID不能为空")
    @ApiModelProperty("ECS服务器ID")
    private Long fromEcsServerId;

    @ApiModelProperty("ECS标签选择(多选)")
    private String tagList;

    @ApiModelProperty("ECS自定义标签选择(多选)")
    private String customTagList;

    @ApiModelProperty("镜像描述")
    private String remark;

    @ApiModelProperty("镜像版本")
    private Float version;
}
