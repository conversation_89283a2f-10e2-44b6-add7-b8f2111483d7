package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 查询Jenkins任务组列表请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "查询Jenkins任务组列表请求参数", description = "查询Jenkins任务组列表请求参数")
public class AdminJenkinsTaskGroupListReqVO {

    @NotNull(message = "服务组ID不能为空")
    @ApiModelProperty(value = "服务组ID", required = true, example = "17")
    private Long servicesGroupId;
}
