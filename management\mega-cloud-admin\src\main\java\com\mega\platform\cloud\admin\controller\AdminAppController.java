package com.mega.platform.cloud.admin.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mega.platform.cloud.admin.service.AdminAppService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.common.mapper.ThirdPlatformMapper;
import com.mega.platform.cloud.core.PageResult;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;

import com.mega.platform.cloud.data.entity.AuthAppConfig;
import com.mega.platform.cloud.data.entity.ThirdPlatform;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应用管理控制器
 */
@RestController
@RequestMapping("/admin/api")
@Api(tags = "项目应用管理")
@Slf4j
@RequiredArgsConstructor
public class AdminAppController {
    
    private final AdminAppService adminAppService;

    /**
     * 分页查询应用列表
     */
    @PostMapping("/{projectId}/app/list")
    public Result<PageResult<AdminAppRespVO>> list(@PathVariable Long projectId, @RequestBody AdminAppListReqVO reqVO) {
        log.info("查询应用列表，项目ID: {}, 请求参数: {}", projectId, reqVO);
        PageResult<AdminAppRespVO> result = adminAppService.findAppList(projectId, reqVO);
        return Results.success(result);
    }
    
    /**
     * 创建应用
     */
    @PostMapping("/{projectId}/app/create")
    public Result<AdminAppRespVO> create(@PathVariable Long projectId, @RequestBody AdminAppCreateReqVO reqVO) {
        log.info("创建应用，项目ID: {}, 请求参数: {}", projectId, reqVO);
        AdminAppRespVO result = adminAppService.createApp(projectId, reqVO);
        return Results.success(result);
    }
    
    /**
     * 获取应用详情
     */
    @PostMapping("/{projectId}/app/detail")
    public Result<AdminAppRespVO> detail(@PathVariable Long projectId, @RequestBody AdminAppDetailReqVO reqVO) {
        log.info("查询应用详情，项目ID: {}, 请求参数: {}", projectId, reqVO);
        AdminAppRespVO result = adminAppService.getAppDetail(projectId, reqVO);
        return Results.success(result);
    }
    
    /**
     * 编辑应用
     */
    @PostMapping("/{projectId}/app/edit")
    public Result<AdminAppRespVO> edit(@PathVariable Long projectId, @RequestBody AdminAppEditReqVO reqVO) {
        log.info("编辑应用，项目ID: {}, 请求参数: {}", projectId, reqVO);
        AdminAppRespVO result = adminAppService.editApp(projectId, reqVO);
        return Results.success(result);
    }
    
    /**
     * 删除应用
     */
    @PostMapping("/{projectId}/app/delete")
    public Result<?> delete(@PathVariable Long projectId, @RequestBody AdminAppDeleteReqVO reqVO) {
        log.info("删除应用，项目ID: {}, 请求参数: {}", projectId, reqVO);
        adminAppService.deleteApp(projectId, reqVO);
        return Results.success();
    }
}