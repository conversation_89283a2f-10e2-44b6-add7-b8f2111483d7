package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 查询Jenkins任务组日志请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "查询Jenkins任务组日志请求参数", description = "查询Jenkins任务组日志请求参数")
public class AdminJenkinsTaskGroupLogReqVO {
    @ApiModelProperty(value = "projectId", example = "1")
    private Long projectId;

    @ApiModelProperty(value = "任务组ID", example = "27")
    private Long jenkinsTaskGroupId = 0L;
}
