package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "查询Jenkins模版参数响应", description = "根据服务组ID或服务ID查询Jenkins模版参数返回值")
public class AdminJenkinsTemplateParamRespVO {
    @ApiModelProperty("参数ID")
    private Long paramId;

    @ApiModelProperty("参数Key（如 user、env、server_list）")
    private String paramKey;

    @ApiModelProperty("参数名称（用于页面显示）")
    private String paramName;

    @ApiModelProperty("参数类型：1-服务组上的参数，2-服务上的参数")
    private Integer servicesDataType;

    @ApiModelProperty("是否必填：1-必填，0-选填")
    private Integer required;

    @ApiModelProperty("参数默认值")
    private String defaultValue;

    @ApiModelProperty("实际参数值（优先取实际配置值，没有则取默认值）")
    private String finalValue;

    @ApiModelProperty("备注")
    private String remark;
}
