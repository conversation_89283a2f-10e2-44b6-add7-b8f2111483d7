package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

@ApiModel
@Data
@Accessors(chain = true)
public class AdminJenkinsTaskGroupVO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "操作用户ID，关联admin_user(id)")
    private Long adminUserId;

    @ApiModelProperty(value = "操作用户name")
    private String adminUserName;

    @ApiModelProperty(value = "微服务组id")
    private Long servicesGroupId;

    @ApiModelProperty(value = "微服务组name")
    private String servicesGroupName;

    @ApiModelProperty(value = "项目名")
    private String projectName;

    @ApiModelProperty(value = "操作类型（build、restart、stop、getlog、update等）")
    private Integer action;

    @ApiModelProperty(value = "操作类型名称")
    private String actionName;

    @ApiModelProperty(value = "操作参数或请求详情（如JSON）")
    private String requestData;

    @ApiModelProperty(value = "任务数量")
    private Integer taskNum;

    @ApiModelProperty(value = "是否成功（0-失败 1=成功）")
    private Byte isSuccess;

    @ApiModelProperty(value = "失败原因")
    private String failedReason;

    @ApiModelProperty(value = "执行完成时间")
    private Date completeTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "逻辑删除标志")
    private Byte delsign;
}
