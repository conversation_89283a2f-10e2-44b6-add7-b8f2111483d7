package com.mega.platform.cloud.data.vo.monitor;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mega.platform.cloud.data.dto.monitor.TurnoverDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class MetricsTurnoverCollectReqVO {
    @ApiModelProperty("服务唯一标识")
    @NotNull
    private String serverIdentifier;
    @ApiModelProperty("servicesId")
    @NotNull
    private Long servicesId;
    @ApiModelProperty("gameRegionId")
    @NotNull
    private Long gameRegionId;
    @ApiModelProperty("上报时间")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date collectTime;
    @ApiModelProperty("流水数据")
    @NotNull
    private List<TurnoverDTO> turnovers;
}
