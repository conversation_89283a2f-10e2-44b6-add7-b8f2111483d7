package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.constant.AdminAuthConstant;
import com.mega.platform.cloud.admin.service.AdminServiceGroupManageService;
import com.mega.platform.cloud.admin.service.AdminServiceManageService;
import com.mega.platform.cloud.admin.service.AdminServiceQueryService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.entity.JenkinsTaskGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/admin/api")
@Api(tags = "service管理")
@Slf4j
@RequiredArgsConstructor
@Validated
public class AdminServiceManageController {
    private final AdminServiceManageService adminServiceManageService;
    private final AdminServiceGroupManageService adminServiceGroupManageService;
    private final AdminServiceQueryService adminServiceQueryService;

    /**
     * 创建服务组
     */
    @ApiOperation("创建服务组")
    @PostMapping("/{projectId}/microservice/service-group/create")
    public Result<?> createServicesGroup(@PathVariable("projectId") Long projectId,
                                        @Valid @RequestBody AdminServicesGroupCreateReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.createServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 编辑服务组
     */
    @ApiOperation("编辑服务组")
    @PostMapping("/{projectId}/microservice/service-group/edit")
    public Result<?> editServicesGroup(@PathVariable("projectId") Long projectId,
                                      @Valid @RequestBody AdminServicesGroupEditReqVO reqVO) throws Exception {
        // Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        // reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.editServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 更改服务组状态
     */
    @ApiOperation("更改服务组状态")
    @PostMapping("/{projectId}/microservice/service-group/status/edit")
    public Result<?> editServicesGroupStatus(@PathVariable("projectId") Long projectId,
                                            @Valid @RequestBody AdminServicesGroupStatusEditReqVO reqVO) {
        adminServiceGroupManageService.editServicesGroupStatus(projectId, reqVO);
        return Results.success();
    }

    /**
     * 重启服务组
     */
    @ApiOperation("重启服务组")
    @PostMapping("/{projectId}/microservice/service-group/restart")
    public Result<?> restartServicesGroup(@PathVariable("projectId") Long projectId,
                                         @Valid @RequestBody AdminServicesGroupActionReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.restartServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 停止服务组
     */
    @ApiOperation("停止服务组")
    @PostMapping("/{projectId}/microservice/service-group/stop")
    public Result<?> stopServicesGroup(@PathVariable("projectId") Long projectId,
                                      @Valid @RequestBody AdminServicesGroupActionReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.stopServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 删除服务组
     */
    @ApiOperation("删除服务组")
    @PostMapping("/{projectId}/microservice/service-group/delete")
    public Result<?> deleteServicesGroup(@PathVariable("projectId") Long projectId,
                                        @Valid @RequestBody AdminServicesGroupActionReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.deleteServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 服务组列表
     */
    @ApiOperation("服务组列表")
    @PostMapping({"/{projectId}/microservice/service/group/list", "/system/microservice/service/group/list"})
    public Result<List<AdminServicesGroupRespVO>> listServiceGroups(
            @PathVariable(value = "projectId", required = false) Long projectId,
            @Validated @RequestBody AdminServicesGroupQueryReqVO reqVO) {
        List<AdminServicesGroupRespVO> groupList = adminServiceGroupManageService.listServicesGroups(projectId, reqVO);
        return Results.success(groupList);
    }

    /**
     * 查询服务组的最后一次 Jenkins 任务记录列表
     */
    @PostMapping("/{projectId}/microservice/job/group/last")
    @ApiOperation("查询服务组的最后一次 Jenkins 任务记录")
    public Result<AdminJenkinsTaskGroupVO> getLastJenkinsTaskGroup(
            @PathVariable("projectId") Long projectId,
            @Validated @RequestBody AdminServicesGroupLastTaskReqVO reqVO) {
        AdminJenkinsTaskGroupVO lastTask = adminServiceGroupManageService.lastTaskGroupByGroupId(reqVO.getServicesGroupId());
        return Results.success(lastTask);
    }

    /**
     * 正在执行的任务组列表
     */
    @ApiOperation("正在执行的任务组列表")
    @PostMapping({"/{projectId}/microservice/job/group/current/list", "/system/microservice/job/group/current/list"})
    public Result<List<AdminServicesGroupRunningJobRespVO>> listServiceGroupsJob(
            @PathVariable(value = "projectId", required = false) Long projectId) {
        List<AdminServicesGroupRunningJobRespVO> runningJobRespVOS = adminServiceGroupManageService.listServicesJobGroups(projectId);
        return Results.success(runningJobRespVOS);
    }

    /**
     * 历史任务组列表接口
     */
    @ApiOperation("历史任务组列表接口")
    @PostMapping({"/{projectId}/microservice/job/group/history/list", "/microservice/job/group/history/list"})
    public Result<List<AdminServicesGroupRunningJobRespVO>> listServiceGroupsJobHistory(
            @PathVariable(value = "projectId", required = false) Long projectId,
            @Validated @RequestBody AdminServicesGroupQueryReqVO reqVO) {
        List<AdminServicesGroupRunningJobRespVO> jobRespVOS = adminServiceGroupManageService.listServiceGroupsJobHistory(projectId, reqVO);
        return Results.success(jobRespVOS);
    }

    /**
     * 创建服务
     */
    @ApiOperation("创建服务")
    @PostMapping("/{projectId}/microservice/service/create")
    public Result<?> createService(@Validated @RequestBody AdminServicesCreateReqVO reqVO,
                                   HttpServletRequest request, @PathVariable String projectId) {
        try {
            Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
            reqVO.setAdminUserId(adminUserId);
            adminServiceManageService.createService(reqVO);
            return Results.success();
        } catch (Exception e) {
            log.error("创建服务失败，projectId: {}, servicesName: {}", projectId, reqVO.getServicesName(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 编辑服务
     */
    @ApiOperation("编辑服务")
    @PostMapping("/{projectId}/microservice/service/edit")
    public Result<?> editService(@Validated @RequestBody AdminServicesEditReqVO reqVO,
                                 @PathVariable String projectId, HttpServletRequest request) {
        try {
            Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
            adminServiceManageService.editService(reqVO, adminUserId);
            return Results.success();
        } catch (Exception e) {
            log.error("编辑服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 上线下线服务
     */
    @ApiOperation("上线下线服务")
    @PostMapping("/{projectId}/microservice/service/status/edit")
    public Result<?> editServiceStatus(@Validated @RequestBody AdminServicesStatusEditReqVO reqVO,
                                 @PathVariable String projectId) {
        try {
            adminServiceManageService.editServiceStatus(reqVO);
            return Results.success();
        } catch (Exception e) {
            log.error("上线下线服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }


    /**
     * 停止服务
     */
    @ApiOperation("停止服务")
    @PostMapping("/{projectId}/microservice/service/status/stop")
    public Result<?> stopService(@Validated @RequestBody AdminServicesHandleReqVO reqVO, HttpServletRequest request,
                                 @PathVariable String projectId) {
        try {
            Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
            adminServiceManageService.stopService(reqVO, adminUserId);
            return Results.success();
        } catch (Exception e) {
            log.error("停止服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 删除服务
     */
    @ApiOperation("删除服务")
    @PostMapping("/{projectId}/microservice/service/status/delete")
    public Result<?> deleteService(@Validated @RequestBody AdminServicesHandleReqVO reqVO, HttpServletRequest request,
                                 @PathVariable String projectId) {
        try {
            Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
            adminServiceManageService.deleteService(reqVO, adminUserId);
            return Results.success();
        } catch (Exception e) {
            log.error("删除服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 项目查询全部services列表
     */
    @PostMapping("/{projectId}/microservice/service/list")
    @ApiOperation(value = "项目查询全部services列表", notes = "支持多种条件筛选查询services列表")
    public Result<List<AdminServicesListRespVO>> getServicesList(@PathVariable("projectId") Long projectId, @RequestBody(required = false) AdminServicesListReqVO reqVO) {
        List<AdminServicesListRespVO> result = adminServiceQueryService.getServicesList(projectId, reqVO);
        return Results.success(result);
    }

    /**
     * 系统查询全部services列表
     */
    @PostMapping("/system/s-microservice/service/list")
    @ApiOperation(value = "系统查询全部services列表", notes = "支持多种条件筛选查询services列表")
    public Result<List<AdminServicesListRespVO>> getServicesListBySystem(@RequestBody(required = false) AdminServicesListReqVO reqVO) {
        List<AdminServicesListRespVO> result = adminServiceQueryService.getServicesList(null, reqVO);
        return Results.success(result);
    }


    /**
     * 基于组查询services列表
     */
    @PostMapping("/{projectId}/microservice/group/service/list")
    @ApiOperation(value = "基于组查询services列表", notes = "查询指定服务组下的所有services")
    public Result<List<AdminServicesListByGroupRespVO>> getServicesListByGroup(@PathVariable("projectId") Long projectId,@RequestBody @Valid AdminServicesListByGroupReqVO reqVO) {
        List<AdminServicesListByGroupRespVO> result = adminServiceQueryService.getServicesListByGroup(projectId, reqVO);
        return Results.success(result);
    }

    /**
     * 查询服务最后一次Jenkins任务
     */
    @PostMapping("/{projectId}/microservice/job/last")
    @ApiOperation(value = "查询服务最后一次Jenkins任务", notes = "查询指定服务的最后一次Jenkins任务执行情况")
    public Result<AdminServicesLastTaskRespVO> getServicesLastTask(@PathVariable("projectId") Object projectIdPath, @RequestBody @Valid AdminServicesLastTaskReqVO reqVO) {
        Long projectId = null;
        try {
            projectId = Long.valueOf(projectIdPath.toString());
        } catch (NumberFormatException ignored) {
        }
        AdminServicesLastTaskRespVO result = adminServiceQueryService.getServicesLastTask(projectId, reqVO);
        return Results.success(result);
    }

    /**
     * 查询Jenkins任务组列表
     */
    @PostMapping("/{projectId}/microservice/job/group/list")
    @ApiOperation(value = "查询Jenkins任务组列表", notes = "查询指定服务组的所有Jenkins任务组列表")
    public Result<List<AdminJenkinsTaskGroupListRespVO>> getJenkinsTaskGroupList(@PathVariable("projectId") Long projectId, @RequestBody @Valid AdminJenkinsTaskGroupListReqVO reqVO) {
        List<AdminJenkinsTaskGroupListRespVO> result = adminServiceQueryService.getJenkinsTaskGroupList(projectId, reqVO);
        return Results.success(result);
    }

    /**
     * 查询Jenkins任务组日志
     */
    @PostMapping("/{projectId}/microservice/job/group/log")
    @ApiOperation(value = "查询Jenkins任务组日志", notes = "查询指定任务组的详细日志内容")
    public Result<List<AdminJenkinsTaskGroupLogRespVO>> getJenkinsTaskGroupLog(@PathVariable("projectId") Long projectId, @RequestBody @Valid AdminJenkinsTaskGroupLogReqVO reqVO) {
        List<AdminJenkinsTaskGroupLogRespVO> result = adminServiceQueryService.getJenkinsTaskGroupLog(projectId, reqVO);
        return Results.success(result);
    }


    /**
     * 查询Jenkins任务组参数详情
     */
    @PostMapping("/microservice/job/param/value")
    @ApiOperation(value = "查询Jenkins任务组参数详情", notes = "查询Jenkins任务组参数详情")
    public Result<List<AdminJenkinsTemplateParamRespVO>> getJenkinsParamValue(@RequestBody @Validated AdminJenkinsTemplateParamReqVO reqVO) {
        List<AdminJenkinsTemplateParamRespVO> result = adminServiceQueryService.getJenkinsParamValue(reqVO);
        return Results.success(result);
    }

}

