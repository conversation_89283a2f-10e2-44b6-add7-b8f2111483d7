package com.mega.platform.cloud.client.push;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.payment.PaymentAppleVerifyTransactionReqVO;
import com.mega.platform.cloud.data.vo.payment.PaymentAppleVerifyTransactionRespVO;
import com.mega.platform.cloud.data.vo.push.PushDouyinMiniReqVO;
import com.mega.platform.cloud.data.vo.push.PushWeChatMiniReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;


@FeignClient(value = "mega-cloud-push-" + "${spring.profiles.active}", contextId = "mega-cloud-push-client")
@Api(tags = {"推送接口", "/push/api/push"})
public interface PushClient {
    @ApiOperation("微信小程序推送")
    @PostMapping("/push/api/push/wechat/mini/push")
    public Result<?> wechatMiniPush(@Validated @RequestBody PushWeChatMiniReqVO vo);

    @ApiOperation("抖音小程序推送")
    @PostMapping("/push/api/push/douyin/mini/push")
    public Result<?> douyinMiniPush(@Validated @RequestBody PushDouyinMiniReqVO vo) throws Exception;
}
