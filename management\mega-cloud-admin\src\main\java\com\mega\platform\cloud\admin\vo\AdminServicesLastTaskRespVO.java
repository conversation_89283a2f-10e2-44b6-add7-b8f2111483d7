package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 查询服务最后一次任务响应参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "查询服务最后一次任务响应参数", description = "查询服务最后一次任务响应参数")
public class AdminServicesLastTaskRespVO {

    // 服务基本信息
    @ApiModelProperty("服务ID")
    private Long servicesId;

    @ApiModelProperty("服务名称")
    private String servicesName;

    @ApiModelProperty("服务备注")
    private String servicesRemark;

    @ApiModelProperty("服务状态")
    private Integer servicesStatus;

    @ApiModelProperty("服务运行状态")
    private Integer servicesRunningStatus;

    @ApiModelProperty("服务真实运行状态")
    private Integer servicesRealRunningStatus;

    @ApiModelProperty("服务创建时间")
    private Date serviceCreateTime;

    // 最后一次任务信息
    @ApiModelProperty("最后一次任务ID")
    private Long lastTaskId;

    @ApiModelProperty("最后一次任务操作类型")
    private Integer lastTaskAction;

    @ApiModelProperty("最后一次任务是否成功")
    private Integer lastTaskIsSuccess;

    @ApiModelProperty("最后一次任务失败原因")
    private String lastFailedReason;

    @ApiModelProperty("最后一次任务Git提交")
    private String lastGitCommit;

    @ApiModelProperty("最后一次任务Jenkins Job ID")
    private Long lastJenkinsJobId;

    @ApiModelProperty("最后一次任务Jenkins Job URL")
    private String lastJenkinsJobUrl;

    @ApiModelProperty("最后一次任务Jenkins任务组ID")
    private Long lastJenkinsTaskGroupId;

    @ApiModelProperty("最后一次任务请求数据")
    private String lastTaskRequestData;

    @ApiModelProperty("最后一次任务备注")
    private String lastTaskRemark;

    @ApiModelProperty("最后一次任务完成时间")
    private Date lastTaskCompleteTime;
}
