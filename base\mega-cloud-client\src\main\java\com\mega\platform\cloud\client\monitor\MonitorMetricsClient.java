package com.mega.platform.cloud.client.monitor;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.monitor.MetricsEcsCollectReqVO;
import com.mega.platform.cloud.data.vo.monitor.MetricsServicesCollectReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;

@FeignClient(value = "mega-cloud-monitor-" + "${spring.profiles.active}", contextId = "mega-cloud-monitor-metrics-client")
@Api(tags = {"指标上报接口", "/monitor/api/metrics"})
public interface MonitorMetricsClient {
    @ApiOperation("服务器指标上报")
    @PostMapping("/monitor/api/metrics/ecs/collect")
    public Result<?> metricsEcsCollect(HttpServletRequest request, @Validated @RequestBody MetricsEcsCollectReqVO reqVO) throws Exception;

    @ApiOperation("业务指标上报")
    @PostMapping("/monitor/api/metrics/services/collect")
    public Result<?> metricsServicesCollect(HttpServletRequest request, @Validated @RequestBody MetricsServicesCollectReqVO reqVO) throws Exception;
}
