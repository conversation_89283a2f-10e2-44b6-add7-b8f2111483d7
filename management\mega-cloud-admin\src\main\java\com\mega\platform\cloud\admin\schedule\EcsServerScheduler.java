package com.mega.platform.cloud.admin.schedule;

import com.mega.platform.cloud.admin.service.AdminAliEcsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableScheduling
public class EcsServerScheduler {

    private final AdminAliEcsService aliEcsService;

    @Autowired
    public EcsServerScheduler(AdminAliEcsService aliEcsService) {

        this.aliEcsService = aliEcsService;
    }
    @Scheduled(fixedDelay = 30000)
    public void checkAliEcsAllStatus() {

        try {
            aliEcsService.checkEcsStatus();
        } catch (Exception e) {
            log.error("Failed to execute Ali Ecs status check", e);
        }

        try {
            aliEcsService.checkCreatingImage();
        } catch (Exception e) {
            log.error("Failed to execute Ali Image status check", e);
        }

        // 延迟删除
        try {
            aliEcsService.scheduleDealyDeleteServers();
        } catch (Exception e) {
            log.error("Failed to execute Ali ECS delay delete servers check", e);
        }
//
//        // 升级镜像
//        try {
//            aliEcsService.scheduleDealyUpgradeServers();
//        } catch (Exception e) {
//            log.error("Failed to execute Ali ECS upgrade image check", e);
//        }
    }
}
