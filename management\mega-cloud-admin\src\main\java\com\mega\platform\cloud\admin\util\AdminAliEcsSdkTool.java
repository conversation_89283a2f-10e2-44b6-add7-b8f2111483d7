package com.mega.platform.cloud.admin.util;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.ecs20140526.AsyncClient;
import com.aliyun.sdk.service.ecs20140526.models.*;
import com.aliyun.sdk.service.ecs20140526.models.DescribeAvailableResourceResponseBody.SupportedResource;
import com.google.gson.Gson;
import com.mega.platform.cloud.AdminErrorCode;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.dto.AdminAliEcsInstanceDTO;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class AdminAliEcsSdkTool {
    private StaticCredentialProvider provider;

    public AdminAliEcsSdkTool(String accessKeyId, String accessKeySecret){
        this.provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(accessKeyId)
                .accessKeySecret(accessKeySecret)
                .build());
    }

    public AsyncClient getClient(String regionStr){
        String endpoint = String.format("ecs.%s.aliyuncs.com", regionStr);
        // log.info("--------阿里云 endpoint {}-----------", endpoint);
        return AsyncClient.builder()
                .region(regionStr) // Region ID example: "cn-hangzhou"
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/Ecs
                                .setEndpointOverride(endpoint) // example: "ecs.cn-hangzhou.aliyuncs.com"
                                .setConnectTimeout(Duration.ofSeconds(60))) // 60秒
                .build();
    }

     // ----------------------------------- 请求ecs sdk 方法  -----------------------------------  

    /***
     * 获取阿里云ecs实例状态
     * @param instanceIds
     * @param regionStr
     * @return
     */
    public List<DescribeInstancesResponseBody.Instance> fetchDescribeInstance(String instanceIds, String regionStr){
        AsyncClient client = getClient(regionStr);
        DescribeInstancesRequest describeInstancesRequest = DescribeInstancesRequest.builder()
                .regionId(regionStr)
                .instanceIds(instanceIds)
                .build();

        // log.info("--------获取阿里云ecs实例状态start, instanceIds: {}-----------", instanceIds);
        try {
            CompletableFuture<DescribeInstancesResponse> response = client.describeInstances(describeInstancesRequest);
            DescribeInstancesResponse resp = response.get(); // Wait for and handle the response
            log.info("--------阿里云 实例状态success! instanceIds: {}, 实例数量: {}-----------", instanceIds, resp.getBody().getInstances().getInstance().size());
            if(resp.getStatusCode() != 200){
                throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(),resp.toString());
            }
            List<DescribeInstancesResponseBody.Instance> instances = resp.getBody().getInstances().getInstance();
            return instances;
        }catch (AdminException e){
            throw e;
        }catch (Exception e) {
            log.error("--------阿里云 实例状态error! instanceIds: {}, error: {}-----------", instanceIds, e.getMessage());
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), e.getMessage());
        } finally {
            client.close();
        }
    }

    /***
     * 获取阿里云ecs实例监控数据
     * @param instanceId
     * @param regionStr
     * @return
     */
    public DescribeInstanceMonitorDataResponseBody.InstanceMonitorData fetchDescribeInstanceMonitorData(String instanceId, String regionStr){
        AsyncClient client = getClient(regionStr);
        try {
            DescribeInstanceMonitorDataRequest monitorRequest = DescribeInstanceMonitorDataRequest.builder()
                    .instanceId(instanceId)
                    .period(60)
                    .startTime(ZonedDateTime.now(ZoneOffset.UTC).minusMinutes(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:00'Z'")))
                    .endTime(ZonedDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:00'Z'")))
                    .build();
            CompletableFuture<DescribeInstanceMonitorDataResponse> response = client.describeInstanceMonitorData(monitorRequest);
            DescribeInstanceMonitorDataResponse resp = response.get();
            if(resp.getStatusCode() != 200){
                throw new Exception(resp.toString());
            }
            List<DescribeInstanceMonitorDataResponseBody.InstanceMonitorData> list =  resp.getBody().getMonitorData().getInstanceMonitorData();
            if(list == null || list.isEmpty()){
                throw new Exception("list is empty");
            }else{
                return list.get(0);
            }
        } catch (Exception e) {
            log.error("--------阿里云 实例监控数据error! instanceId: {}, error: {}-----------", instanceId, e.getMessage());
            return null;
        }finally{
            client.close();
        }
    }

    /***
     * 删除阿里云ecs实例
     * @param instanceIds
     * @param regionStr
     * @throws Exception 
     */
    public void  fetchDeleteInstances(List<String> instanceIds, String regionStr) throws Exception{
        AsyncClient client = getClient(regionStr);
        try {
            DeleteInstancesRequest deleteInstanceRequest = DeleteInstancesRequest.builder()
                    .instanceId(instanceIds)
                    .regionId(regionStr)
                    .force(true) // 强制删除
                    .build();
            CompletableFuture<DeleteInstancesResponse> response = client.deleteInstances(deleteInstanceRequest);
            DeleteInstancesResponse resp = response.get();
            if(resp.getStatusCode() != 200){
                throw new Exception(resp.toString());
            }
            log.info("--------阿里云 释放 success! instanceIds: {}, regionStr: {}-----------", instanceIds, regionStr);
        } catch (Exception e) {
            log.error("--------阿里云 释放 error! instanceIds: {}, regionStr: {}, error: {}-----------", instanceIds, regionStr, e.getMessage());
            throw e;
        } finally {
            client.close();
        }
    }

    /**
     * 停止阿里云ecs实例
     * 
     * @param instanceId
     * @param regionStr
     * @throws Exception
     */
    public void fetchStopInstance(String instanceId, String regionStr) throws Exception {
        AsyncClient client = getClient(regionStr);
        try {
            StopInstanceRequest stopInstanceRequest = StopInstanceRequest.builder()
                    .instanceId(instanceId)
                    .forceStop(true)
                    .stoppedMode("StopCharging")
                    .build();
            CompletableFuture<StopInstanceResponse> response = client.stopInstance(stopInstanceRequest);
            StopInstanceResponse resp = response.get();
            if (resp.getStatusCode() != 200) {
                throw new Exception(resp.toString());
            }
            log.info("--------阿里云 停止实例 success! instanceId: {}, regionStr: {}-----------", instanceId, regionStr);
        } catch (Exception e) {
            log.error("--------阿里云 停止实例 error! instanceId: {}, regionStr: {}, error: {}-----------", instanceId,
                    regionStr, e.getMessage());
            throw e;
        } finally {
            client.close();
        }
    }

//
//    public void  fetchDescribeAvailableResource(AdminEcsConfigDTO ecsConfig){
//        AsyncClient client = getClient(ecsConfig.getRegionStr());
//        try {
//            String instanceChargeType = "prePaid".equalsIgnoreCase(ecsConfig.getChargingMode()) ? "PrePaid" : "PostPaid";
//            DescribeAvailableResourceRequest request = DescribeAvailableResourceRequest.builder()
//                    .regionId(ecsConfig.getRegionStr())
//                    .spotStrategy("NoSpot")
//                    .destinationResource("InstanceType")
//                    .zoneId(ecsConfig.getZoneStr())
//                    .instanceType(ecsConfig.getFlavorRef())
//                    .instanceChargeType(instanceChargeType)
//                    .resourceType("instance")
//                    .build();
//            log.info("阿里云 资源信息req ->: {}",new Gson().toJson(request));
//            CompletableFuture<DescribeAvailableResourceResponse> response = client.describeAvailableResource(request);
//            DescribeAvailableResourceResponse resp = response.get();
//            log.info("阿里云 资源信息resp<-: {}",new Gson().toJson(resp.getBody()));
//            if (resp.getStatusCode() != 200) {
//                throw new Exception("http status code: " + resp.getStatusCode());
//            }
//
//            List<DescribeAvailableResourceResponseBody.AvailableZone> availableZones = resp.getBody()
//                    .getAvailableZones().getAvailableZone();
//            //1 可用区域为空
//            if (availableZones == null || availableZones.isEmpty()) {
//                throw new Exception("可用区域为空");
//            }
//            DescribeAvailableResourceResponseBody.AvailableZone availableZone =   availableZones.get(0);
//            //2 可用区状态为Available
//            String zoneStatus = availableZone.getStatus();
//            if (!"Available".equals(zoneStatus)) {
//                throw new Exception("可用区状态" + zoneStatus +"," + availableZone.getStatusCategory());
//            }
//            //3 可用区资源类型
//            List<DescribeAvailableResourceResponseBody.AvailableResource> zoneResources = availableZone.getAvailableResources().getAvailableResource();
//            if (zoneResources == null || zoneResources.isEmpty()) {
//                throw new Exception("可用区资源类型为空");
//            }
//            SupportedResource resource = zoneResources.get(0).getSupportedResources().getSupportedResource().get(0);
//            if(!"Available".equals(resource.getStatus())){
//                throw new Exception("可用区资源类型状态为" + resource.getStatus() +"," + resource.getStatusCategory());
//            }
//
//
//            log.info("--------阿里云 资源信息success! id: {}, flavorRef: {}, regionShowName: {}, status: {}-----------",
//                    ecsConfig.getId(), ecsConfig.getFlavorRef(), ecsConfig.getRegionShowName(), resource.getStatus());
//
//        } catch (Exception e) {
//            String errorMsg = String.format("资源信息错误！id: %s, name: %s, flavorRef: %s, error: %s-----------",
//                    ecsConfig.getId(), ecsConfig.getRegionShowName(), ecsConfig.getFlavorRef(), e.getMessage());
//            log.error("--------阿里云 error! {} --------", errorMsg);
//            throw new AdminException(AdminErrorCode.ERR_20005.getCode(), errorMsg);
//        } finally {
//            client.close();
//        }
//    }

//
//    public String fetchCreateImage(AdminAliEcsInstanceDTO server, String regionStr, String imageName){
//        AsyncClient client = getClient(regionStr);
//        try {
//            // Parameter settings for API request
//            CreateImageRequest createImageRequest = CreateImageRequest.builder()
//                    .regionId(regionStr)
//                    .instanceId(server.getEcsServerId())
//                    .imageName(imageName)
//                    .build();
//            CompletableFuture<CreateImageResponse> response = client.createImage(createImageRequest);
//            CreateImageResponse resp = response.get();
//            if(resp.getStatusCode() != 200){
//                throw new Exception(resp.toString());
//            }
//            log.info("--------阿里云新建镜像 success! instanceId: {}, imageId: {} --------", server.getEcsServerId(), resp.getBody().getImageId());
//            String imageId =  resp.getBody().getImageId();
//            if(!StringUtils.hasLength(imageId)){
//                throw new Exception("镜像id为空");
//            }
//            return imageId;
//        }catch (Exception e) {
//            log.error("--------阿里云新建镜像 error! instanceId: {}, error: {} --------", server.getEcsServerId(), e.getMessage());
//            throw new AdminException(AdminErrorCode.ERR_20005.getCode(), e.getMessage());
//        } finally {
//            client.close();
//        }
//    }

    @SneakyThrows
    public DescribeImagesResponseBody fetchImageDetail(String imageId, String regionStr){
        AsyncClient client = getClient(regionStr);
        try {
            DescribeImagesRequest describeImagesRequest = DescribeImagesRequest.builder()
                .regionId(regionStr) // 区域    
                .imageId(imageId) // 镜像id
                .status("Available") // 镜像状态
                .build();
            CompletableFuture<DescribeImagesResponse> response = client.describeImages(describeImagesRequest);
            DescribeImagesResponse resp = response.get();
            if(resp.getStatusCode() != 200 || resp.getBody().getImages() == null){
                throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), resp.toString());
            }
            log.info("--------阿里云获取镜像信息 success! imageId: {}, totalCount: {} --------", imageId, resp.getBody().getTotalCount());
            return resp.getBody();
        } catch (AdminException e) {
            throw e;
        } catch (Exception e) {
            log.error("--------阿里云获取镜像信息 error! imageId: {}, error: {} --------", imageId, e.getMessage());
            throw e;
        } finally {
            client.close();
        }
    }

    /***
     * 拷贝镜像
     * @param fromImageRef 源镜像id
     * @param fromRegionStr 源镜像区域
     * @param toRegionStr 目标镜像区域
     * @param toImageName 目标镜像名称
     * @param toRegionShowName 目标镜像区域名称
     * @return 目标镜像id
     */
    public String fetchCopyImage(String fromImageRef, String fromRegionStr, String toRegionStr, String toImageName, String toRegionShowName){
        AsyncClient client = getClient(fromRegionStr);
        try {
            CopyImageRequest copyImageRequest = CopyImageRequest.builder()
                    .imageId(fromImageRef)   // 源镜像id
                    .regionId(fromRegionStr) // 源镜像区域
                    .destinationImageName(toImageName) // 目标镜像名称
                    .destinationRegionId(toRegionStr) // 目标镜像区域
                    .build();
            log.info("--------阿里云拷贝镜像 req: {} --------", new Gson().toJson(copyImageRequest));
            CompletableFuture<CopyImageResponse> response = client.copyImage(copyImageRequest);
            CopyImageResponse resp = response.get();
            
            if(resp.getStatusCode() != 200 || resp.getBody().getImageId() == null){
                throw new Exception(resp.toString());
            }
            String imageId = resp.getBody().getImageId();
            log.info("--------阿里云拷贝镜像 success!目标：{} fromImageRef: {}, fromRegionStr: {}, toRegionStr: {}, imageId: {} --------", toRegionShowName, fromImageRef, fromRegionStr, toRegionStr, imageId);
            return imageId;
        } catch (Exception e) {
            log.error("--------阿里云拷贝镜像 error!目标：{} fromImageRef: {}, fromRegionStr: {}, toRegionStr: {}, error: {} --------", toRegionShowName, fromImageRef, fromRegionStr, toRegionStr, e.getMessage());
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), e.getMessage());
        }
    }

    /***    
     * 删除镜像
     * @param imageId 镜像id
     * @param regionStr 镜像区域
     */
    public void fetchDeleteImage(String imageId, String regionStr){
        AsyncClient client = getClient(regionStr);
        try {   
            DeleteImageRequest deleteImageRequest = DeleteImageRequest.builder()    
                    .regionId(regionStr)
                    .imageId(imageId)
                    .force(true) // 强制删除    
                    .build();
            CompletableFuture<DeleteImageResponse> response = client.deleteImage(deleteImageRequest);
            DeleteImageResponse resp = response.get();  
            if(resp.getStatusCode() != 200){
                throw new Exception(resp.toString());
            }
            log.info("--------阿里云删除镜像 success! imageId: {} --------", imageId);
        } catch (Exception e) {
            log.error("--------阿里云删除镜像 error! imageId: {}, error: {} --------", imageId, e.getMessage());
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), e.getMessage());
        }   
    }


    /**
     * 修改实例计费类型
     * @param instanceIds
     * @param regionStr
     * @param chargeType
     */
    public void fetchModifyChargeType(String instanceIds, String regionStr, String chargeType) {
        AsyncClient client = getClient(regionStr);
        try {
            ModifyInstanceChargeTypeRequest modifyInstanceChargeTypeRequest = ModifyInstanceChargeTypeRequest.builder()
                    .instanceIds(instanceIds)
                    .period(1) // 按月购买时长 先买1个月来检查问题
                    .periodUnit("Month")
                    .regionId(regionStr)
                    .instanceChargeType(chargeType)
                    .build();
            CompletableFuture<ModifyInstanceChargeTypeResponse> response = client.modifyInstanceChargeType(modifyInstanceChargeTypeRequest);

            ModifyInstanceChargeTypeResponse resp = response.get();
            if(resp.getStatusCode() != 200){
                throw new Exception(resp.toString());
            }
            log.info("--------阿里云修改实例计费类型 success! instanceIds: {}, regionStr: {}, chargeType: {}-----------", instanceIds, regionStr, chargeType);
        } catch (Exception e) {
            log.error("--------阿里云修改实例计费类型 error! instanceIds: {}, regionStr: {}, chargeType: {}, error: {}-----------", instanceIds, regionStr, chargeType, e.getMessage());
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), e.getMessage());
        } finally {
            client.close();
        }
    }

    /**
     * 升级镜像
     * @param ecsServerId
     * @param regionStr
     * @param imageRef
     * @return
    */
    public boolean fetchUpgradeImage(String ecsServerId, String regionStr, String imageRef) {
        AsyncClient client = getClient(regionStr);
       try {
            // Parameter settings for API request
            ReplaceSystemDiskRequest replaceSystemDiskRequest = ReplaceSystemDiskRequest.builder()
            .instanceId(ecsServerId)
            .imageId(imageRef)
            .build();
            CompletableFuture<ReplaceSystemDiskResponse> response = client.replaceSystemDisk(replaceSystemDiskRequest);
            ReplaceSystemDiskResponse resp = response.get();
            if(resp.getStatusCode() != 200){
                throw new Exception(resp.toString());
            }
            log.info("--------阿里云升级镜像 success! instanceId: {}, imageId: {} --------", ecsServerId, imageRef);
            return true;
       } catch (Exception e) {
            log.error("--------阿里云ecs升级节点镜像 serverId:{}, version:{} 升级失败-----------", ecsServerId, imageRef, e);
            return false;
       }    
    }

    /** 
     * 查询磁盘状态
     * @param id
     * @param ecsServerId
     * @param regionStr
     * @return
     */
    public boolean fetchDescribeDisks(Integer id, String ecsServerId, String regionStr) {
        AsyncClient client = getClient(regionStr);
        try {
            DescribeDisksRequest describeDisksRequest = DescribeDisksRequest.builder()
                    .instanceId(ecsServerId)
                    .regionId(regionStr)
                    .build();
            CompletableFuture<DescribeDisksResponse> response = client.describeDisks(describeDisksRequest);
            DescribeDisksResponse resp = response.get();
            if(resp.getStatusCode() != 200){
                throw new Exception(resp.toString());
            }
            List<DescribeDisksResponseBody.Disk> disks = resp.getBody().getDisks().getDisk();
            if(disks == null || disks.isEmpty()){
                throw new Exception("磁盘为空");
            }
            log.info("--------阿里云ecs查询磁盘状态 success! id: {}, instanceId: {}, regionStr: {}, status: {}-----------", id, ecsServerId, regionStr, disks.get(0).getStatus());
            if (disks.get(0).getStatus().equals("In_use")) {
                return true;
            }else{
                return false;
            }
        } catch (Exception e) {
            log.error("--------阿里云ecs查询磁盘状态 error! id: {}, instanceId: {}, regionStr: {}, error: {}-----------", id, ecsServerId, regionStr, e.getMessage());
            return false;
        }finally {
            client.close();
        }
    }

    /**
     * 启动实例
     * @param id
     * @param ecsServerId
     * @param regionStr
     */
    public void fetchStartInstance(Integer id, String ecsServerId, String regionStr) {
        AsyncClient client = getClient(regionStr);
        try {
            StartInstanceRequest startInstanceRequest = StartInstanceRequest.builder()
                    .instanceId(ecsServerId)
                    .build();
            CompletableFuture<StartInstanceResponse> response = client.startInstance(startInstanceRequest);
            StartInstanceResponse resp = response.get();
            if(resp.getStatusCode() != 200){    
                throw new Exception(resp.toString());
            }
            log.info("--------阿里云ecs启动实例 success! id: {}, instanceId: {} --------", id, ecsServerId);
        } catch (Exception e) {
            log.error("--------阿里云ecs启动实例 error! id: {}, instanceId: {}, regionStr: {}, error: {}-----------", id, ecsServerId, regionStr, e.getMessage());
        } finally {
            client.close();
        }
    }

    /**
     * 修改自动续费属性
     * 目前阿里云没有提供修改自动续费的API接口
     * @param instanceId
     * @param regionStr
     */
    public void fetchModifyAutoRenewAttribute(String instanceId, String regionStr) {
        AsyncClient client = getClient(regionStr);
        try {
            ModifyInstanceAutoRenewAttributeRequest modifyAutoRenewAttributeRequest = ModifyInstanceAutoRenewAttributeRequest.builder()
                    .instanceId(instanceId)
                    .regionId(regionStr)
                    .duration(12) // 续费时长
                    .periodUnit("Month") // 续费时长单位
                    .autoRenew(true) // true 自动续费，false 不自动续费
                    .renewalStatus("AutoRenewal") // AutoRenewal 自动续费，NotAutoRenewal 不自动续费
                    .build();
            CompletableFuture<ModifyInstanceAutoRenewAttributeResponse> response = client.modifyInstanceAutoRenewAttribute(modifyAutoRenewAttributeRequest);
            ModifyInstanceAutoRenewAttributeResponse resp = response.get();
            if(resp.getStatusCode() != 200){
                throw new Exception(resp.toString());
            }
            log.info("--------阿里云修改实例自动续费属性 success! instanceId: {}, regionStr: {}-----------", instanceId, regionStr);
        } catch (Exception e) {
            log.error("--------阿里云修改实例自动续费属性 error! instanceId: {}, regionStr: {}, error: {}-----------", instanceId, regionStr, e.getMessage());
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), e.getMessage());
        } finally {
            client.close();
        }
    }

    /**
     * 新建镜像
     * @param server
     * @param imageName
     * @return
     */
    public String fetchCreateImage(AdminAliEcsInstanceDTO server, String imageName) {
        AsyncClient client = getClient(server.getRegionStr());
        try {
            // Parameter settings for API request
            CreateImageRequest createImageRequest = CreateImageRequest.builder()
                    .regionId(server.getRegionStr())
                    .instanceId(server.getInstanceId())
                    .imageName(imageName)
                    .build();
            CompletableFuture<CreateImageResponse> response = client.createImage(createImageRequest);
            CreateImageResponse resp = response.get();
            if(resp.getStatusCode() != 200){
                throw new Exception(resp.toString());
            }
            log.info("--------阿里云新建镜像 success! instanceId: {}, imageId: {} --------", server.getInstanceId(), resp.getBody().getImageId());
            String imageId =  resp.getBody().getImageId();
            if(!StringUtils.hasLength(imageId)){
                throw new Exception("镜像id为空");
            }
            return imageId;
        }catch (Exception e) {
            log.error("--------阿里云新建镜像 error! instanceId: {}, error: {} --------", server.getInstanceId(), e.getMessage());
            throw new AdminException(AdminErrorCode.ALI_ECS_SDK_ERROR.getCode(), e.getMessage());
        } finally {
            client.close();
        }
    }
}
