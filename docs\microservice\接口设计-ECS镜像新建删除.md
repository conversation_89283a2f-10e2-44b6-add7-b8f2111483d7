# 接口设计-ECS镜像新建删除


mega-cloud-admin模组的controller目录下已经存在了`AdminEcsController.java`文件，作为镜像相关接口的入口。
新建AdminEcsImageService.java，作为ECS镜像相关接口的业务处理类。
涉及到数据库操作，则新建`AdminEcsImageDao.java`和对应的`AdminEcsImageDao.xml`，作为ECS镜像相关的数据库操作类。

## 涉及的表
* ecs_server
* ecs_server_config
* ecs_server_region
* ecs_server_image
* ecs_services_command
* ecs_server_log
* `ecs_server_image_tag_relation` 
* ecs_server_tag_relation



提供如下接口：

## 1 查询所有镜像列表
### 1.1 接口 POST
`/admin/api/{projectId}/microservice/ecs/image/list`
`/admin/api/system/microservice/ecs/image/list`
### 1.2 入参 AdminEcsImageListReqVO 
```java
public class AdminEcsImageListReqVO {
    private String projectId; // 可能没有
    private String name;      // 名称（模糊查询）
    private String remark;    // 备注（模糊查询）
    private String adminUserId; // 创建的管理员
    private Integer ecsType;    // ECS类型(1 mysql, 2 redis, 3 services)
    private String tagList;        // ECS标签选择(多选)
    private String customTagList;  // ECS自定义标签选择(多选)
    private Integer status;     // 状态
    private String version;    // 版本
}
```

### 1.3 返回 AdminEcsImageListRespVO

```java
public class AdminEcsImageListRespVO {

    private List<EcsServerImageVO> ecsList; // ECS列表

    public static class EcsServerImageVO {
        private String id;          // ECS Server Image ID
        private String name;        // 名称
        private String projectId;   // 项目ID
        private String projectName; // 项目名称
        private Integer ecsType;    // ECS类型(1 mysql, 2 redis, 3 services)
        private String ecsTypeName; // ECS类型名称
        private String version;     // 版本
        private String tagList;       // ECS标签选择(多选)
        private String customTagList; // ECS自定义标签选择(多选)
        private Integer status;     // 状态
        private String statusName;  // 状态名称
        private String adminUserId; // 创建的管理员ID
        private String adminUserName; // 创建的管理员名称
        private String remark;      // 备注
        private Date createTime;    // 创建时间
        private Date updateTime;    // 更新时间
        private Long fromEcsServerId; // 镜像源的服务器id（阿里镜像新建模式非空
    }
}
```




## 2 创建镜像
### 2.1 接口
`/admin/api/{projectId}/microservice/ecs/image/create`
`/admin/api/system/microservice/ecs/image/create`

### 2.3 入参 AdminEcsImageCreateReqVO
```java
public class AdminEcsImageCreateReqVO {
    private String projectId;           // 项目ID（优先级: 低, Feign）
    private String name;                // 名称
    private Long fromEcsServerId;       // 基于哪个ECS服务器创建镜像
    private String tagList;       // ECS标签选择(多选)
    private String customTagList; // ECS自定义标签选择(多选)
    private String remark;              // 备注
    private Float version;              // 版本 像版本, 小数形式, 如 1.0 2.1 3.14
    
}
```

返回无

## 3 删除镜像
`/admin/api/{projectId}/microservice/ecs/image/delete`
`/admin/api/system/microservice/ecs/image/delete`
### 3.2 入参 AdminEcsImageDeleteReqVO
```java
public class AdminEcsImageDeleteReqVO {
    private String ecsServerImageId;      // 镜像id
    private String projectId;  // 项目ID（优先级: 低, Feign）
}
```

