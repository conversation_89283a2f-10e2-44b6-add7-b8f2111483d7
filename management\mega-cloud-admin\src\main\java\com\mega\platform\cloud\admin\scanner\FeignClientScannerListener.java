package com.mega.platform.cloud.admin.scanner;

import com.mega.platform.cloud.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

import com.mega.platform.cloud.admin.dto.AdminUrlPatternDTO;
import com.mega.platform.cloud.admin.dao.AdminScannerDao;

import io.swagger.annotations.Api;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
 
/**
 * FeignClient接口扫描器
 * 程序启动时扫描所有@FeignClient注解的接口，提取@RequestMapping的URL和@Api的tags信息
 */
@Component
@Slf4j
public class FeignClientScannerListener implements ApplicationListener<ApplicationReadyEvent> {

    private final ApplicationContext applicationContext;
    private final AdminScannerDao adminScannerDao;

    @Autowired
    public FeignClientScannerListener(ApplicationContext applicationContext, AdminScannerDao adminScannerDao) {
        this.applicationContext = applicationContext;
        this.adminScannerDao = adminScannerDao;
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("【ScanFeign】开始扫描FeignClient接口...");
        scanFeignClients();
        log.info("【ScanFeign】FeignClient接口扫描完成");
    }

    /**
     * 扫描所有FeignClient接口
     */
    private void scanFeignClients() {
        // 使用ClassPathScanningCandidateComponentProvider扫描FeignClient接口
        // 注意：需要设置为true以包含接口扫描
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false) {
            @Override
            protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {
                // 允许扫描接口
                return beanDefinition.getMetadata().isInterface() || super.isCandidateComponent(beanDefinition);
            }
        };
        scanner.addIncludeFilter(new AnnotationTypeFilter(FeignClient.class));
        
        // 扫描client包
        String basePackage = "com.mega.platform.cloud.client";
        Set<BeanDefinition> candidateComponents = scanner.findCandidateComponents(basePackage);
        
        log.info("【ScanFeign】扫描包路径: {}, 找到候选组件数量: {}", basePackage, candidateComponents.size());

        List<AdminUrlPatternDTO> adminUrlPatternDTOList = new ArrayList<>();
        for (BeanDefinition beanDefinition : candidateComponents) {
            try {
                String className = beanDefinition.getBeanClassName();
                Class<?> clazz = Class.forName(className);
                
                if (clazz.isInterface() && clazz.isAnnotationPresent(FeignClient.class)) {
                    adminUrlPatternDTOList.addAll(processFeignClientInterface(clazz));
                }
            } catch (ClassNotFoundException e) {
                log.error("无法加载类: {}", beanDefinition.getBeanClassName(), e);
            }
        }

        // 存入数据库表`project_url_pattern`
        if (!adminUrlPatternDTOList.isEmpty()) {
            try {
                int affectedRows = adminScannerDao.batchInsertOrUpdateUrlPatterns(adminUrlPatternDTOList);
                log.info("成功写入URL模式到数据库，影响行数: {}", affectedRows);
            } catch (Exception e) {
                log.error("写入URL模式到数据库失败", e);
            }
        } else {
            log.info("未扫描到任何FeignClient接口");
        }

    }

    /**
     * 处理FeignClient接口
     */
    private List<AdminUrlPatternDTO> processFeignClientInterface(Class<?> interfaceClass) {
        log.info("【ScanFeign】发现FeignClient接口: {}", interfaceClass.getName());
        List<AdminUrlPatternDTO> adminUrlPatternDTOList = new ArrayList<>();

        // 获取@Api注解的tags值
        String name = "";
        String urlPattern = "";
        if (interfaceClass.isAnnotationPresent(Api.class)) {
            Api api = interfaceClass.getAnnotation(Api.class);
            String[] tags = api.tags();
            if (tags.length > 1) {
                name = tags[0];
                urlPattern =  tags[1];
            }
        }

        String moduleName = "";
        if (interfaceClass.isAnnotationPresent(FeignClient.class)) {
            FeignClient requestMapping = interfaceClass.getAnnotation(FeignClient.class);
            String value = requestMapping.value();
            if (!StringUtils.isEmpty(value)) {
                moduleName = StringUtils.capitalize(value.replace("mega-cloud-", "").replace("${spring.profiles.active}", ""));
            }
        }
        
        // 创建AdminUrlPatternDTO对象
        if (!StringUtils.isEmpty(urlPattern)) {
            AdminUrlPatternDTO adminUrlPatternDTO = new AdminUrlPatternDTO();
            adminUrlPatternDTO.setUrlPattern(urlPattern + "/**");
            adminUrlPatternDTO.setName(moduleName + name);
            adminUrlPatternDTOList.add(adminUrlPatternDTO);
        }
        
        log.info("【ScanFeign】FeignClient [{}] URL模式: {}, 名称: {}", interfaceClass.getSimpleName(), urlPattern, name);
        
        return adminUrlPatternDTOList;
    }

}