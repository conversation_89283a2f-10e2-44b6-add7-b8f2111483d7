<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminProjectTemplateDao">

    <!-- 查询所有有效的应用模板 -->
    <select id="selectAllAppTemplates" resultType="com.mega.platform.cloud.data.entity.ProjectAppTemplate">
        SELECT id, app_name, remark, create_time, update_time, delsign
        FROM project_app_template
        WHERE delsign = 0
        ORDER BY create_time ASC
    </select>

    <!-- 根据应用名称查询权限模板 -->
    <select id="selectPermissionTemplatesByAppName" resultType="com.mega.platform.cloud.data.entity.ProjectAppPermissionTemplate">
        SELECT id, app_name, url_pattern, create_time, update_time, delsign
        FROM project_app_permission_template
        WHERE app_name = #{appName} AND delsign = 0
        ORDER BY id ASC
    </select>

    <!-- 根据多个应用名称批量查询权限模板 -->
    <select id="selectPermissionTemplatesByAppNames" resultType="com.mega.platform.cloud.data.entity.ProjectAppPermissionTemplate">
        SELECT id, app_name, url_pattern, create_time, update_time, delsign
        FROM project_app_permission_template
        WHERE delsign = 0 AND app_name IN
        <foreach collection="appNames" item="appName" open="(" separator="," close=")">
            #{appName}
        </foreach>
        ORDER BY app_name ASC, id ASC
    </select>

    <!-- 批量插入应用权限 -->
    <insert id="batchInsertAppPermissions" parameterType="java.util.List">
        INSERT INTO project_app_permission (project_app_id, url_pattern, create_time, update_time, delsign)
        VALUES
        <foreach collection="permissions" item="permission" separator=",">
            (
                #{permission.projectAppId},
                #{permission.urlPattern},
                #{permission.createTime},
                #{permission.updateTime},
                #{permission.delsign}
            )
        </foreach>
    </insert>

</mapper>