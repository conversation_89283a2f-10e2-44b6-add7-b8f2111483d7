package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 查询Jenkins任务组列表响应参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "查询Jenkins任务组列表响应参数", description = "查询Jenkins任务组列表响应参数")
public class AdminJenkinsTaskGroupListRespVO {

    @ApiModelProperty("任务组ID")
    private Long id;

    @ApiModelProperty("操作用户ID")
    private Long adminUserId;

    @ApiModelProperty("操作用户名")
    private String adminUserName;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("微服务组ID")
    private Long servicesGroupId;

    @ApiModelProperty("操作类型")
    private Integer action;

    @ApiModelProperty("操作类型名称")
    private String actionName;

    @ApiModelProperty("操作参数或请求详情")
    private String requestData;

    @ApiModelProperty("任务数量")
    private Integer taskNum;

    @ApiModelProperty("是否成功")
    private Byte isSuccess;

    @ApiModelProperty("失败原因")
    private String failedReason;

    @ApiModelProperty("执行完成时间")
    private Date completeTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("逻辑删除标志")
    private Byte delsign;
}
