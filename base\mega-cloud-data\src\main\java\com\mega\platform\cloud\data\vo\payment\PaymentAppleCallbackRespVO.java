package com.mega.platform.cloud.data.vo.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@ApiModel("苹果交易回调响应")
@Data
@Accessors(chain = true)
public class PaymentAppleCallbackRespVO {
    @ApiModelProperty(value = "苹果交易id")
    private String transactionId;

    @ApiModelProperty(value = "苹果产品id")
    private String productId;

    @ApiModelProperty(value = "回调类型")
    private String type;


    @ApiModelProperty(value = "回调校验结果")
    private boolean success;
}
