package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("ECS镜像列表查询响应")
public class AdminEcsImageListRespVO {

    @ApiModelProperty("ECS镜像列表")
    private List<EcsServerImageVO> ecsList;

    @Data
    @Accessors(chain = true)
    @ApiModel("ECS镜像信息")
    public static class EcsServerImageVO {

        @ApiModelProperty("ECS Server Image ID")
        private Long id;

        @ApiModelProperty("名称")
        private String name;

        @ApiModelProperty("项目ID")
        private Long projectId;

        @ApiModelProperty("项目名称")
        private String projectName;

        @ApiModelProperty("ECS类型(1 mysql, 2 redis, 3 services)")
        private Integer ecsType;

        @ApiModelProperty("ECS类型名称")
        private String ecsTypeName;

        @ApiModelProperty("版本")
        private Float version;

        @ApiModelProperty("ECS标签选择(多选，逗号分隔)")
        private String tagList;

        @ApiModelProperty("ECS自定义标签选择(多选，逗号分隔)")
        private String customTagList;

        @ApiModelProperty("状态")
        private Integer status;

        @ApiModelProperty("状态名称")
        private String statusName;

        @ApiModelProperty("创建的管理员ID")
        private String adminUserId;

        @ApiModelProperty("创建的管理员名称")
        private String adminUserName;

        @ApiModelProperty("备注")
        private String remark;

        @ApiModelProperty("创建时间")
        private Date createTime;

        @ApiModelProperty("更新时间")
        private Date updateTime;

        @ApiModelProperty("镜像源的服务器id（阿里镜像新建模式非空）")
        private Long fromEcsServerId;

        @ApiModelProperty("镜像源的服务器名称（阿里镜像新建模式非空）")
        private String fromEcsServerName;

        @ApiModelProperty("云服务商regionId")
        private String regionStr;

        @ApiModelProperty("云服务商区域")
        private String regionZone;

        @ApiModelProperty("数据盘的参数")
        private String extra_data_disk;
    }
}
