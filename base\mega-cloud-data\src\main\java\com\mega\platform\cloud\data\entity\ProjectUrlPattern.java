package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "project_url_pattern")
public class ProjectUrlPattern {
    /**
     * Ant风格路径匹配，例如 /api/v1/user/**
     */
    @Id
    @Column(name = "url_pattern")
    private String urlPattern;

    /**
     * 路由名称 例：auth路由、access路由
     */
    @Column(name = "name")
    private String name;

    /**
     * 描述
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}