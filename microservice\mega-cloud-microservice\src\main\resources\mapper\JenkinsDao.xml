<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mega.platform.cloud.microservice.dao.JenkinsDao">
    <insert id="insertJenkinsSshServer" useGeneratedKeys="true" keyColumn="id" keyProperty="jenkinsSshServer.id">
        INSERT INTO jenkins_ssh_server (server_name, jenkins_service_id, ecs_server_id)
        VALUES (#{jenkinsSshServer.serverName}, #{jenkinsSshServer.jenkinsServiceId}, #{jenkinsSshServer.ecsServerId})
    </insert>
    <insert id="insertJenkinsView" useGeneratedKeys="true" keyColumn="id" keyProperty="jenkinsView.id">
        INSERT INTO jenkins_view (view_name, project_id, project_app_id, jenkins_services_id)
        VALUES (#{jenkinsView.viewName}, #{jenkinsView.projectId}, #{jenkinsView.projectAppId}, #{jenkinsView.jenkinsServicesId})
    </insert>
    <select id="getTemplateParamKeyValueListByDataType" resultType="com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO">
        SELECT t1.id AS jenkinsTemplateParamId,
               t1.param_key,
               t1.required,
               t1.default_value,
               t2.param_value
        FROM jenkins_job_template_param AS t1
                 LEFT JOIN jenkins_job_template_param_value AS t2 ON t1.id = t2.jenkins_job_templete_param_id
        WHERE t1.jenkins_template_id = #{jenkinsTemplateId}
          AND t1.services_data_type = #{servicesDataType}
          AND t2.services_data_type = #{servicesDataType}
          AND t2.services_data_id = #{servicesDataId}
          AND t1.delsign = 0
          AND t2.delsign = 0
    </select>
    <select id="getTemplateParamKeyListByDataType" resultType="com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO">
        SELECT id AS jenkinsTemplateParamId,
               param_key,
               required,
               default_value
        FROM jenkins_job_template_param
        WHERE jenkins_template_id = #{jenkinsTemplateId}
          AND services_data_type = #{servicesDataType}
          AND delsign = 0
    </select>
    <select id="getSameEcsServerJenkinsJob" resultType="com.mega.platform.cloud.data.entity.JenkinsJob">
        SELECT t2.*
        FROM services AS t1
                 LEFT JOIN jenkins_job AS t2 ON t1.jenkins_job_id = t2.id
        WHERE t1.ecs_server_id = #{ecsServerId}
          AND t1.services_group_id = #{serviceGroupId}
          AND t1.delsign = 0
          AND t1.id != #{servicesId}
        LIMIT 1
    </select>
    <insert id="insertJenkinsJob" useGeneratedKeys="true" keyColumn="id" keyProperty="jenkinsJob.id">
        INSERT INTO jenkins_job (jenkins_services_id, jenkins_view_id, jenkins_ssh_server_id, job_name)
        VALUES (#{jenkinsJob.jenkinsServicesId}, #{jenkinsJob.jenkinsViewId}, #{jenkinsJob.jenkinsSshServerId}, #{jenkinsJob.jobName})
    </insert>
    <select id="getServicesDataJenkinsParamValue" resultType="java.lang.String">
        SELECT t1.param_value
        FROM jenkins_job_template_param_value AS t1
                 LEFT JOIN jenkins_job_template_param AS t2 ON t1.jenkins_job_templete_param_id = t2.id
        WHERE t1.services_data_id = #{servicesDataId}
          AND t1.services_data_type = #{servicesDataType}
          AND t2.param_key = #{param}
    </select>
    <select id="getServicesDataJenkinsParamValues" resultType="com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO">
        SELECT t2.param_key, t1.param_value
        FROM jenkins_job_template_param_value AS t1
                 LEFT JOIN jenkins_job_template_param AS t2 ON t1.jenkins_job_templete_param_id = t2.id
        WHERE t1.services_data_id = #{servicesDataId}
          AND t1.services_data_type = #{servicesDataType}
    </select>
    <insert id="insertJenkinsTaskGroup" useGeneratedKeys="true" keyColumn="id" keyProperty="taskGroup.id">
        INSERT INTO jenkins_task_group (admin_user_id, services_group_id, action, request_data, task_num)
        VALUES (#{taskGroup.adminUserId}, #{taskGroup.servicesGroupId}, #{taskGroup.action}, #{taskGroup.requestData}, #{taskGroup.taskNum})
    </insert>
    <insert id="insertJenkinsTask" useGeneratedKeys="true" keyColumn="id" keyProperty="task.id">
        INSERT INTO jenkins_task (jenkins_task_group_id, jenkins_job_id, action, request_data)
        VALUES (#{task.jenkinsTaskGroupId}, #{task.jenkinsJobId}, #{task.action}, #{task.requestData})
    </insert>
    <insert id="insertJenkinsUser" useGeneratedKeys="true" keyColumn="id" keyProperty="jenkinsUser.id">
        INSERT INTO jenkins_user (jenkins_services_id, admin_user_id, jenkins_username, api_token)
        VALUES (#{jenkinsUser.jenkinsServicesId}, #{jenkinsUser.adminUserId}, #{jenkinsUser.jenkinsUsername}, #{jenkinsUser.apiToken})
    </insert>
    <select id="getLastJenkinsTaskGroupByServicesGroupId" resultType="com.mega.platform.cloud.data.entity.JenkinsTaskGroup">
        SELECT *
        FROM jenkins_task_group
        WHERE services_group_id = #{servicesGroupId}
        ORDER BY id DESC
        LIMIT 1
    </select>
    <update id="updateJenkinsTemplateParamValue">
        UPDATE jenkins_job_template_param_value AS t1 LEFT JOIN jenkins_job_template_param AS t2 ON t1.jenkins_job_templete_param_id = t2.id
        SET t1.param_value = #{paramValue}
        WHERE t1.services_data_id = #{servicesDataId}
          AND t1.services_data_type = #{servicesDataType}
          AND t1.jenkins_job_templete_param_id = #{templateId}
          AND t2.param_key = #{paramKey}
    </update>
    <select id="getServicesDataJenkinsParamKeys" resultType="com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO">
        SELECT param_key
        FROM jenkins_job_template_param
        WHERE services_data_type = #{servicesDataType}
          AND jenkins_template_id = #{templateId}
          AND delsign = 0
    </select>
    <select id="getServicesIdsByJenkinTemplateId" resultType="java.lang.Long">
        SELECT t1.id
        FROM services AS t1
                 LEFT JOIN services_group AS t2 ON t1.services_group_id = t2.id
        WHERE t2.jenkins_template_id = #{jenkinsTemplateId}
          AND t1.delsign = 0
    </select>
</mapper>