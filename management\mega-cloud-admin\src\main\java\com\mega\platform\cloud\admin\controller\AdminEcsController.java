package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.constant.AdminAuthConstant;
import com.mega.platform.cloud.admin.service.AdminEcsService;
 import com.mega.platform.cloud.admin.service.AdminEcsImageService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/admin/api")
@Api(tags = "ECS管理")
@Slf4j
@Validated
public class AdminEcsController {

    private final AdminEcsService adminEcsService;
    private final AdminEcsImageService adminEcsImageService;

    @Autowired
    public AdminEcsController(AdminEcsService adminEcsService, AdminEcsImageService adminEcsImageService) {
        this.adminEcsService = adminEcsService;
        this.adminEcsImageService = adminEcsImageService;
    }

    @ApiOperation("ECS列表查询")
    @PostMapping({"/system/microservice/ecs/list", "/{projectId}/microservice/ecs/list"})
    public Result<AdminEcsListRespVO> listEcs(
            @PathVariable(value = "projectId", required = false) String projectId,
            @Valid @RequestBody AdminEcsListReqVO reqVO) {
        
        AdminEcsListRespVO result = adminEcsService.getEcsList(projectId, reqVO);
        return Results.success(result);
    }


    @ApiOperation("ECS配置列表查询")
    @PostMapping({"/{projectId}/microservice/ecs/config/list", "/system/microservice/ecs/config/list"})
    public Result<AdminEcsConfigListRespVO> listEcsConfig(
            @PathVariable(value = "projectId", required = false) String projectId,
            @Valid @RequestBody AdminEcsConfigListReqVO reqVO) {
        
        
        AdminEcsConfigListRespVO result = adminEcsService.getEcsConfigList(projectId, reqVO);
        return Results.success(result);
    }

    @ApiOperation("创建ECS实例")
    @PostMapping({"/{projectId}/microservice/ecs/create", "/system/microservice/ecs/create"})
    public Result<?> createEcs(
            @PathVariable(value = "projectId", required = false) String projectId,
            @Valid @RequestBody AdminEcsCreateReqVO reqVO,
            HttpServletRequest request) {
                
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        if(projectId != null){
            reqVO.setProjectId(projectId);
        }
        log.info("创建ECS实例，projectId: {}, adminUserId: {}", reqVO.getProjectId(), adminUserId);
        adminEcsService.createEcs(reqVO, adminUserId);
        return Results.success();
    }

    @ApiOperation("删除ECS实例")
    @PostMapping({"/{projectId}/microservice/ecs/delete", "/system/microservice/ecs/delete"})
    public Result<?> deleteEcs(
            @PathVariable(value = "projectId", required = false) Long projectId,
            @Valid @RequestBody AdminEcsDeleteReqVO reqVO,
            HttpServletRequest request) {
                
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        if(projectId != null){
            reqVO.setProjectId(projectId);
        }
        log.info("删除ECS实例，projectId: {}, adminUserId: {}", reqVO.getProjectId(), adminUserId);
        adminEcsService.deleteEcs(reqVO, adminUserId);
        return Results.success();
    }

    @ApiOperation("ECS按需转按月")
    @PostMapping({"/{projectId}/microservice/ecs/period", "/system/microservice/ecs/period"})
    public Result<?> changeToPeriod(
            @PathVariable(value = "projectId", required = false) Long projectId,
            @Valid @RequestBody AdminEcsReqVO reqVO,
            HttpServletRequest request) {
                
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        if(projectId != null){
            reqVO.setProjectId(projectId);
        }
        log.info("ECS按需转按月，projectId: {}, ecsServerId: {}, adminUserId: {}", reqVO.getProjectId(), reqVO.getEcsServerId(), adminUserId);
        adminEcsService.changeToPeriod(reqVO, adminUserId);
        return Results.success();
    }

    @ApiOperation("ECS按月转自动续费")
    @PostMapping({"/{projectId}/microservice/ecs/autorenew", "/system/microservice/ecs/autorenew"})
    public Result<?> changeToAutoRenew(
            @PathVariable(value = "projectId", required = false) Long projectId,
            @Valid @RequestBody AdminEcsReqVO reqVO,
            HttpServletRequest request) {
                
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        if(projectId != null){
            reqVO.setProjectId(projectId);
        }
        log.info("ECS按月转自动续费，projectId: {}, ecsServerId: {}, adminUserId: {}", 
                reqVO.getProjectId(), reqVO.getEcsServerId(), adminUserId);
        adminEcsService.changeToAutoRenew(reqVO, adminUserId);
        return Results.success();
    }


    // ==================== ECS镜像管理接口 ====================

    @ApiOperation("ECS镜像列表查询")
    @PostMapping({"/system/microservice/ecs/image/list", "/{projectId}/microservice/ecs/image/list"})
    public Result<AdminEcsImageListRespVO> listEcsImage(
            @PathVariable(value = "projectId", required = false) Long projectId,
            @Valid @RequestBody AdminEcsImageListReqVO reqVO) {
        
        if (projectId != null) {
            reqVO.setProjectId(projectId);
        }

        AdminEcsImageListRespVO result = adminEcsImageService.getEcsImageList(reqVO);
        return Results.success(result);
    }

    @ApiOperation("创建ECS镜像")
    @PostMapping({"/{projectId}/microservice/ecs/image/create", "/system/microservice/ecs/image/create"})
    public Result<?> createEcsImage(
            @PathVariable(value = "projectId", required = false) Long projectId,
            @Valid @RequestBody AdminEcsImageCreateReqVO reqVO,
            HttpServletRequest request) {

        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        if(projectId != null){
            reqVO.setProjectId(projectId);
        }
        log.info("创建ECS镜像，projectId: {}, adminUserId: {}", reqVO.getProjectId(), adminUserId);
        adminEcsImageService.createEcsImage(reqVO, adminUserId);
        return Results.success();
    }

    @ApiOperation("删除ECS镜像")
    @PostMapping({"/{projectId}/microservice/ecs/image/delete", "/system/microservice/ecs/image/delete"})
    public Result<?> deleteEcsImage(
            @PathVariable(value = "projectId", required = false) Long projectId,
            @Valid @RequestBody AdminEcsImageDeleteReqVO reqVO,
            HttpServletRequest request) {

        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        if(projectId != null){
            reqVO.setProjectId(projectId);
        }
        log.info("删除ECS镜像，projectId: {}, adminUserId: {}", reqVO.getProjectId(), adminUserId);
        adminEcsImageService.deleteEcsImage(reqVO, adminUserId);
        return Results.success();
    }

}
