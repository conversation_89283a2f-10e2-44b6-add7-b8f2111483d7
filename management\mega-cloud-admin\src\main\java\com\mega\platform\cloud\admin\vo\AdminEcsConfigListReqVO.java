package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("ECS配置列表查询请求")
public class AdminEcsConfigListReqVO {
    
    @ApiModelProperty("项目ID（可能没有）")
    private String projectId;
    
    @ApiModelProperty("名称（模糊查询）")
    private String name;
    
    @ApiModelProperty("ECS类型(1 mysql, 2 redis, 3 services)")
    private Integer ecsType;

    @ApiModelProperty("ECS配置ID")
    private Long ecsConfigId;

    
    @ApiModelProperty(value = "ECS服务器ID", hidden = true)
    private Long ecsServerId;
}