package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 全部services列表查询响应参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "全部services列表查询响应参数")
public class AdminServicesListRespVO {

    // services基本信息
    @ApiModelProperty("服务ID")
    private Long servicesId;

    @ApiModelProperty("服务名称")
    private String servicesName;

    @ApiModelProperty("服务组ID")
    private Long servicesGroupId;

    @ApiModelProperty("服务器ID")
    private Long ecsServerId;

    @ApiModelProperty("Jenkins Job ID")
    private Long jenkinsJobId;

    @ApiModelProperty("程序路径")
    private String path;

    @ApiModelProperty("日志路径")
    private String logPath;

    @ApiModelProperty("产品版本")
    private String version;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("日志超时时间")
    private Integer logTimeoutSecond;

    @ApiModelProperty("服务状态 2001")
    private Integer servicesStatus;

    @ApiModelProperty("日志状态")
    private Integer logStatus;

    @ApiModelProperty("服务运行状态 2002")
    private Integer servicesRunningStatus;

    @ApiModelProperty("服务真实运行状态 2002")
    private Integer servicesRealRunningStatus;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("服务备注")
    private String servicesRemark;

    @ApiModelProperty("Jenkins 参数列表")
    private Map<String, String> jenkinsParams;

    @ApiModelProperty("公网IP")
    private String publicIp;

    @ApiModelProperty("内网IP")
    private String innerIp;

    /** 组的基本信息 */
    @ApiModelProperty(value = "服务组详细信息")
    private ServicesGroup servicesGroupDetail;

    @Data
    @ApiModel(value = "服务组信息")
    public static class ServicesGroup {
        @ApiModelProperty("新增 标签ID列表（逗号分隔）")
        private String tagIds;

        @ApiModelProperty("新增 自定义标签ID列表（逗号分隔）")
        private String customizeTagIds;
        

        @ApiModelProperty(value = "服务组ID")
        private Long id;

        @ApiModelProperty(value = "服务组名称")
        private String name;

        @ApiModelProperty(value = "产品ID")
        private Long projectId;

        @ApiModelProperty(value = "应用ID")
        private Long projectAppId;

        @ApiModelProperty(value = "服务更新方式")
        private Integer servicesUpdateType;

        @ApiModelProperty(value = "微服务环境 2005")
        private String servicesEnv;

        @ApiModelProperty(value = "微服务环境ID, 2005开头")
        private Long servicesEnvId;


        @ApiModelProperty(value = "微服务日志格式ID")
        private Long servicesLogFormatId;

        @ApiModelProperty(value = "滚服/导流重启时保活数量")
        private Integer servicesAliveNum;

        @ApiModelProperty(value = "Jenkins 服务ID")
        private Long jenkinsServicesId;

        @ApiModelProperty(value = "Jenkins 模板ID")
        private Long jenkinsTemplateId;

        @ApiModelProperty("是否自研（1-自研，3-三方）")
        private Integer isSelf;

        @ApiModelProperty(value = "负责人ID")
        private Long adminUserId;

        @ApiModelProperty(value = "备注")
        private String remark;

        @ApiModelProperty(value = "状态 2001")
        private Integer status;

        @ApiModelProperty(value = "运行状态 2003")
        private Integer runningStatus;

        @ApiModelProperty(value = "真实运行状态 2003")
        private Integer realRunningStatus;

        @ApiModelProperty(value = "检查运行方式 2008")
        private Integer checkAliveType;

        @ApiModelProperty("是否使用 Jenkins（0-否，1-是）")
        private Byte useJenkins;

        @ApiModelProperty(value = "创建时间")
        private Date createTime;

        @ApiModelProperty(value = "更新时间")
        private Date updateTime;

        @ApiModelProperty("是否删除（0-未删除，1-已删除）")
        private Integer delsign;

        @ApiModelProperty(value = "服务组参数列表")
        private Map<String, String> servicesGroupParams;

        @ApiModelProperty("新增 当前组service数量")
        private Integer serviceCount;

        @ApiModelProperty("新增 环境ID")
        private Long envId;

        @ApiModelProperty("新增 排序")
        private Integer sort;

        @ApiModelProperty("新增 管理员name")
        private String adminUserName;

        @ApiModelProperty("新增 app name")
        private String projectAppName;

        @ApiModelProperty("新增 project name")
        private String projectName;
    
    }

    @ApiModelProperty(value = "最后一次任务组信息")
    private TaskGroup lastTaskGroup;

    @Data
    @ApiModel(value = "任务组信息")
    public static class TaskGroup {
        @ApiModelProperty(value = "最后一次任务组ID")
        private Long lastTaskGroupId;

        @ApiModelProperty(value = "最后一次任务组操作类型")
        private Integer lastTaskGroupAction;

        @ApiModelProperty(value = "最后一次任务组是否成功")
        private Integer lastTaskGroupIsSuccess;

        @ApiModelProperty(value = "最后一次任务组完成时间")
        private Date lastTaskGroupCompleteTime;
    }

    @ApiModelProperty(value = "最后一次任务信息")
    private Task lastTask;

    @Data
    @ApiModel(value = "任务信息")
    public static class Task {
        @ApiModelProperty(value = "最后一次任务ID")
        private Long lastTaskId;

        @ApiModelProperty(value = "最后一次任务操作类型")
        private Integer lastTaskAction;

        @ApiModelProperty(value = "最后一次任务是否成功")
        private Integer lastTaskIsSuccess;

        @ApiModelProperty(value = "最后一次任务完成时间")
        private Date lastTaskCompleteTime;

        @ApiModelProperty(value = "最后一次任务Jenkins链接")
        private String lastTaskJenkinsJobUrl;
    }
}
