package com.mega.platform.cloud.push.client;
import com.douyin.openapi.client.Client;
import com.douyin.openapi.client.models.V1NotifyRequest;
import com.douyin.openapi.client.models.V1NotifyResponse;
import com.douyin.openapi.credential.models.Config;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.client.auth.AuthVerificationClient;
import com.mega.platform.cloud.client.push.PushClient;
import com.mega.platform.cloud.common.mapper.PushAppConfigMapper;
import com.mega.platform.cloud.data.entity.AuthDouyinConfig;
import com.mega.platform.cloud.data.entity.PushAppConfig;
import com.mega.platform.cloud.data.vo.auth.AuthMiniTokenReqVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
@Component
@RequiredArgsConstructor
@Slf4j
public class DouyinMiniPushClient {
    private final PushAppConfigMapper pushAppConfigMapper;
    private final ObjectMapper objectMapper;
    private final AuthVerificationClient authVerificationClient;
    /**
     * 推送订阅消息
     */
    public Boolean pushSubscribeMessage(Long appId, String openId, String tplId, Map<String, String> data, String page) throws Exception {
        PushAppConfig pushAppConfig = pushAppConfigMapper.selectByPrimaryKey(appId);
        AuthDouyinConfig config = objectMapper.convertValue(pushAppConfig.getConfig(), AuthDouyinConfig.class);

        Client client = getClientByAppId(appId, config);
        AuthMiniTokenReqVO authMiniTokenReqVO = new AuthMiniTokenReqVO();
        authMiniTokenReqVO.setTargetAppId(appId);
        V1NotifyRequest request = new V1NotifyRequest()
                .setAccessToken(authVerificationClient.douyinMiniToken(authMiniTokenReqVO).getData())
                .setTplId(tplId)
                .setAppId(config.getMiniAppId())
                .setData(data)
                .setOpenId(openId)
                .setPage(page);

        V1NotifyResponse response = client.V1Notify(request);
        log.info("抖音推送消息结果：{}", objectMapper.writeValueAsString(response));
        return response.getErrNo() == 0;
    }

    private Client getClientByAppId(Long appId, AuthDouyinConfig config) throws Exception {
        return new Client(new Config()
                .setClientKey(config.getMiniAppId())
                .setClientSecret(config.getMiniAppSecret()));
    }
}
