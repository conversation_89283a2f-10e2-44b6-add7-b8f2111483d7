package com.mega.platform.cloud.admin.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.vo.AdminServicesCreateReqVO;
import com.mega.platform.cloud.admin.vo.AdminServicesEditReqVO;
import com.mega.platform.cloud.admin.vo.AdminServicesStatusEditReqVO;
import com.mega.platform.cloud.admin.vo.AdminServicesHandleReqVO;
import com.mega.platform.cloud.client.microservice.ServicesClient;
import com.mega.platform.cloud.common.enums.ServiceGroupBuildActionEnum;
import com.mega.platform.cloud.common.mapper.*;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.ResultCode;
import com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO;
import com.mega.platform.cloud.data.entity.*;
import com.mega.platform.cloud.data.vo.microservice.BuildServicesReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesRespVO;
import com.mega.platform.cloud.data.vo.microservice.UpdateServicesJenkinsParamReqVO;
import com.mega.platform.cloud.microservice.service.ServicesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mega.platform.cloud.common.constant.MicroserviceConstants.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminServiceManageService {
    private final ServicesService servicesService;
    private final ServicesMapper servicesMapper;
    private final ServicesClient servicesClient;
    private final ServicesGroupMapper servicesGroupMapper;
    private final JenkinsJobTemplateParamValueMapper jenkinsJobTemplateParamValueMapper;
    private final JenkinsSshServerMapper jenkinsSshServerMapper;
    private final EcsServerMapper ecsServerMapper;
    /**
     * admin创建服务
     * @param reqVO
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void createService(AdminServicesCreateReqVO reqVO) throws Exception {
        CreateServicesReqVO createServicesReqVO =  new CreateServicesReqVO();
        BeanUtils.copyProperties(reqVO, createServicesReqVO);
        createServicesReqVO.setJenkinsParams(jenkinsParamsToMap(reqVO.getJenkinsParams()));
        CreateServicesRespVO createServicesRespVO = servicesService.createServices(createServicesReqVO);
        Services services = servicesMapper.selectByPrimaryKey(createServicesRespVO.getServicesId());
        // 更新部分字段
        services.setPath(reqVO.getPath());
        services.setLogPath(reqVO.getLogPath());
        services.setDescription(reqVO.getDescription());
        services.setLogTimeoutSecond(reqVO.getLogTimeoutSecond());
        services.setSort(reqVO.getSort());
        services.setRemark(reqVO.getRemark());
        servicesMapper.updateByPrimaryKeySelective(services);
    }

    /**
     * 将jenkinsParams JSON字符串转换为Map
     * @param jenkinsParams JSON字符串
     * @return Map<String, String>
     */
    private Map<String, String> jenkinsParamsToMap(String jenkinsParams) {
        if (!StringUtils.hasText(jenkinsParams)) {
            return new HashMap<>();
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jenkinsParams, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            log.warn("解析jenkinsParams JSON失败，使用空Map: {}", jenkinsParams, e);
            return new HashMap<>();
        }
    }

    /**
     * admin编辑服务
     * @param reqVO
     */
    @Transactional(rollbackFor = Exception.class)
    public void editService(AdminServicesEditReqVO reqVO, Long adminUserId) throws Exception {
        // 查询服务
        Services services = servicesMapper.selectOne(new Services().setId(reqVO.getServicesId()).setDelsign((byte) 0));
        if (services == null) {
            throw new AdminException(0, "服务不存在");
        }

        // 业务规则校验
        Map<String, String> jenkinsParams = null;
        if (StringUtils.hasText(reqVO.getJenkinsParams())) {
            jenkinsParams = new ObjectMapper().readValue(
                    reqVO.getJenkinsParams(), new TypeReference<>() {}
            );
        }
        if (isServicePublished(services) && hasRestrictedFieldChanges(reqVO, jenkinsParams)) {
            throw new AdminException(0, "请下线服务后重试");
        }

        // 更新服务基本信息
        BeanUtils.copyProperties(reqVO, services);
        servicesMapper.updateByPrimaryKeySelective(services);

        // 处理Jenkins参数
        if (jenkinsParams != null) {
            updateJenkinsParams(services, services.getServicesGroupId(), jenkinsParams, adminUserId);
        }
    }

    /**
     * 上线下线服务
     * @param reqVO
     */
    public void editServiceStatus(AdminServicesStatusEditReqVO reqVO) {
        Services services = servicesMapper.selectOne(new Services().setId(reqVO.getServicesId()).setDelsign((byte) 0));
        ServicesGroup servicesGroup = servicesGroupMapper.selectByPrimaryKey(services.getServicesGroupId());
        if (servicesGroup == null) {
            if (reqVO.getStatus() == 1 && servicesGroup.getStatus() == 0) {
                throw new AdminException(0, "服务组未上线");
            }
        }
        if (services != null) {
            if (reqVO.getStatus() == 0) {
                // 下线
                if (isServiceOnline(services)) {
                    throw new AdminException(0, "服务为在线状态");
                }
            } else if (reqVO.getStatus() == 1) {
                // 上线
                // TODO 参数检查
            }
            services.setStatus(reqVO.getStatus());
            servicesMapper.updateByPrimaryKeySelective(services);
        } else {
            throw new AdminException(0, "服务不存在");
        }
    }

    /**
     * 停止服务
     * @param reqVO
     * @param adminUserId
     * @throws Exception
     */
    public void stopService(AdminServicesHandleReqVO reqVO, Long adminUserId) throws Exception {
        Services services = servicesMapper.selectOne(new Services().setId(reqVO.getServicesId()).setDelsign((byte) 0));
        if (services != null) {
            BuildServicesReqVO buildServicesReqVO = new BuildServicesReqVO();
            buildServicesReqVO.setAdminUserId(adminUserId).setServicesId(reqVO.getServicesId()).setAction(ServiceGroupBuildActionEnum.STOP.getAction());
            Result result = servicesClient.buildServices(buildServicesReqVO);
            if (result.getCode() != ResultCode.SUCCESS.getCode()) {
                throw new AdminException(result.getCode() , result.getMessage());
            }
        } else {
            throw new AdminException(0, "服务不存在");
        }
    }

    /**
     * 删除服务
     * @param reqVO
     */
    public void deleteService(AdminServicesHandleReqVO reqVO, Long adminUserId) throws Exception {
        Services services = servicesMapper.selectOne(new Services().setId(reqVO.getServicesId()).setDelsign((byte) 0));
        if (services != null) {
            servicesService.deleteServicesJenkinsJob(services.getId(), adminUserId);
            services.setDelsign((byte) 1);
            servicesMapper.updateByPrimaryKeySelective(services);
        } else {
            throw new AdminException(0, "服务不存在");
        }
    }

    private boolean hasRestrictedFieldChanges(AdminServicesEditReqVO reqVO, Map<String, String> jenkinsParams) {
        return reqVO.getPath() != null
                || reqVO.getEcsServerId() != null
                || jenkinsParams != null
                || reqVO.getName() != null
                || reqVO.getDescription() != null;
    }

    private void updateJenkinsParams(Services services, Long serviceGroupId, Map<String, String> jenkinsParams, Long adminUserId) throws Exception {
        // 校验服务组存在性
        if (serviceGroupId == null) {
            throw new AdminException(0, "服务组ID不能为空");
        }

        ServicesGroup servicesGroup = servicesGroupMapper.selectByPrimaryKey(serviceGroupId);
        if (servicesGroup == null) {
            throw new AdminException(0, "服务组不存在");
        }
        Long ecsServerId = services.getEcsServerId();
        JenkinsSshServer record = new JenkinsSshServer().setJenkinsServiceId(servicesGroup.getJenkinsServicesId()).setEcsServerId(ecsServerId);
//        JenkinsSshServer jenkinsSshServer = jenkinsSshServerMapper.selectOne(record);
//        EcsServer ecsServer = ecsServerMapper.selectByPrimaryKey(ecsServerId);
        servicesService.fillInvisibleParam(jenkinsParams, services.getId(), SERVICES_DATA_TYPE_SERVICES, adminUserId);
//        jenkinsParams.put(JENKINS_TEMPLATE_KEY_SSH_SERVER_NAME, jenkinsSshServer.getServerName());
//        jenkinsParams.put(JENKINS_TEMPLATE_KEY_ECS_SERVER_IP, ecsServer.getPrivateIp());
        // 验证模板参数
        List<JenkinsTemplateParamDTO> templateServicesParams = servicesService
                .checkTemplateParamKey(servicesGroup.getJenkinsTemplateId(), SERVICES_DATA_TYPE_SERVICES, jenkinsParams);

        // 循环更新参数
        for (JenkinsTemplateParamDTO templateParam : templateServicesParams) {
            JenkinsJobTemplateParamValue paramValue = new JenkinsJobTemplateParamValue();
            paramValue.setJenkinsJobTempleteParamId(templateParam.getJenkinsTemplateParamId());
            paramValue.setServicesDataId(services.getId());
            paramValue.setServicesDataType(SERVICES_DATA_TYPE_SERVICES);
            paramValue.setParamValue(jenkinsParams.get(templateParam.getParamKey()));
            int count = jenkinsJobTemplateParamValueMapper.updateByPrimaryKeySelective(paramValue);
            if (count != 0) {
                jenkinsJobTemplateParamValueMapper.insertSelective(paramValue);
            }
        }

        // 更新ecs
        UpdateServicesJenkinsParamReqVO reqVO = new UpdateServicesJenkinsParamReqVO();
        reqVO.setServicesId(services.getId());
        reqVO.setAdminUserId(adminUserId);
        servicesService.updateServicesJenkinsInvisibleParam(reqVO);
    }

    /**
     * 检测服务是否上线
     * @param services
     * @return
     */
    private boolean isServicePublished(Services services) {
        // 业务逻辑判断服务是否上线
        return services.getStatus() == 1;
    }

    /**
     * 检测服务是否在线
     * @param services
     * @return
     */
    private boolean isServiceOnline(Services services) {
        return !(services.getRunningStatus() == 0 && services.getRealRunningStatus() == 0);
    }
}
