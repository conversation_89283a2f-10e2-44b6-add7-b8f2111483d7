package com.mega.platform.cloud.data.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("微信平台配置")
public class AuthWeChatConfig {
    @ApiModelProperty("微信 App ID")
    @JsonProperty("appId")
    private String appId;

    @ApiModelProperty("微信 App Secret")
    @JsonProperty("appSecret")
    private String appSecret;

    @ApiModelProperty("微信小程序 App ID")
    @JsonProperty("miniAppId")
    private String miniAppId;

    @ApiModelProperty("微信小程序 App Secret")
    @JsonProperty("miniAppSecret")
    private String miniAppSecret;
}
