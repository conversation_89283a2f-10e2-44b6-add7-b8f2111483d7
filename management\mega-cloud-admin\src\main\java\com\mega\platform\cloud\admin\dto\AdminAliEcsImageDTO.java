package com.mega.platform.cloud.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AdminAliEcsImageDTO {
    @ApiModelProperty(value = "ecs_server_image表id",example = "1")
    private Integer id;

    @ApiModelProperty(value = "镜像名称",example = "dev-ap-southeast-1-node-international-V2.0")
    private String name;

    @ApiModelProperty(value = "阿里云镜像唯一uid",example = "1")
    private String ref;

    @ApiModelProperty(value = "镜像版本",example = "2.0")
    private Float version;

    @ApiModelProperty(value = "1 创建完成  2 正在创建 ",example = "1")
    private Integer status;

    @ApiModelProperty(value = "阿里云服务器区域",example = "ap-southeast-1")
    private String regionStr;

    @ApiModelProperty(value = "ecs_server表id",example = "1")
    private Integer fromEcsServerId;

    @ApiModelProperty(value = "from_router_server_id从哪个服务器拷贝",example = "1")
    private Integer fromRouterServerId;

    @ApiModelProperty(value = "from_router_server_image_id从哪个镜像拷贝",example = "1")
    private Integer fromRouterServerImageId;

}
