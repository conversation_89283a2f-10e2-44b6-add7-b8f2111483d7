package com.mega.platform.cloud.auth.client;

import com.aliyun.tea.TeaException;
import com.douyin.openapi.client.Client;
import com.douyin.openapi.client.models.*;
import com.douyin.openapi.credential.models.Config;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.auth.cache.AuthAppConfigCache;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.data.entity.AuthDouyinConfig;
import com.mega.platform.cloud.data.entity.AuthDouyinMiniSessionResp;
import com.mega.platform.cloud.data.entity.DouyinTokenCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 抖音小程序接口客户端
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DouyinVerifierClient {

    private final AuthAppConfigCache configCache;
    private final ObjectMapper objectMapper;
    private final Map<Long, DouyinTokenCache> douyinTokenCacheMap = new ConcurrentHashMap<>();

    /**
     * 获取抖音小程序用户信息
     */
    public AuthDouyinMiniSessionResp getMiniProgramSession(Long appId, String code) throws Exception {
        Client client = getClientByAppId(appId);
        AuthDouyinConfig config = getAuthConfig(appId);

        AppsJscode2sessionRequest request = new AppsJscode2sessionRequest()
                .setAppid(config.getMiniAppId())
                .setCode(code)
                .setSecret(config.getMiniAppSecret());
        try {
            AppsJscode2sessionResponse response = client.AppsJscode2session(request);
            log.info("getMiniProgramSession response:{}", objectMapper.writeValueAsString(response));
            if (response != null && response.getError() == 0) {
                return new AuthDouyinMiniSessionResp()
                        .setSessionKey(response.getSessionKey())
                        .setUnionId(response.getUnionid())
                        .setOpenId(response.getOpenid());
            }
        } catch (TeaException e) {
            log.info("getMiniProgramSession error:{}", e.getCode());
        }

        throw new RuntimeException("解析抖音用户信息响应失败");
    }

    /**
     * 获取全局 access_token（多 appId 支持，带缓存）
     */
    public String getGeneralAccessToken(Long appId) throws Exception {
        DouyinTokenCache cache = douyinTokenCacheMap.get(appId);
        long now = System.currentTimeMillis();
        if (cache != null && now < cache.getExpireTime()) {
            return cache.getAccessToken();
        }
        return refreshAccessToken(appId);
    }

    /**
     * 强制刷新某个 appId 的 access_token
     */
    public synchronized String refreshAccessToken(Long appId) throws Exception {
        Client client = getClientByAppId(appId);
        AuthDouyinConfig config = getAuthConfig(appId);

        AppsV2TokenRequest request = new AppsV2TokenRequest()
                .setAppid(config.getMiniAppId())
                .setGrantType("client_credential")
                .setSecret(config.getMiniAppSecret());

        AppsV2TokenResponse response;
        try {
            response = client.AppsV2Token(request);
        } catch (Exception e) {
            throw new RuntimeException("请求抖音access_token失败", e);
        }

        if (response == null || response.getData() == null || response.getData().getAccessToken() == null) {
            throw new RuntimeException("获取抖音AccessToken失败：" + safeToJson(response));
        }

        String accessToken = response.getData().getAccessToken();
        Long expiresAt = response.getData().getExpiresAt(); // 抖音返回的是过期时间戳(秒)
        if (expiresAt == null) {
            throw new RuntimeException("抖音返回的expires_at为空: " + safeToJson(response));
        }

        // 转换为毫秒，稍微提前200秒过期
        long expireTime = (expiresAt * 1000L) - 200_000L;

        douyinTokenCacheMap.put(appId, new DouyinTokenCache(accessToken, expireTime));

        return accessToken;
    }

    /**
     * 安全序列化
     */
    private String safeToJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            return String.valueOf(obj);
        }
    }

    /**
     * 从缓存获取配置
     */
    private AuthDouyinConfig getAuthConfig(Long appId) {
        return configCache.getTypedConfig(appId, ThirdPlatformEnum.DOUYIN.getCode(), AuthDouyinConfig.class);
    }

    /**
     * 获取 Douyin Client
     */
    private Client getClientByAppId(Long appId) throws Exception {
        AuthDouyinConfig config = getAuthConfig(appId);
        return new Client(new Config()
                .setClientKey(config.getMiniAppId())
                .setClientSecret(config.getMiniAppSecret()));
    }
}
