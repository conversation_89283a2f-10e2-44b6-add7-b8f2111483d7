package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@ApiModel("运行中服务组及最近一次任务返回项")
public class AdminServicesGroupRunningJobRespVO {
    @ApiModelProperty("服务组ID")
    private Long servicesGroupId;

    @ApiModelProperty("服务组名称")
    private String servicesGroupName;

    @ApiModelProperty("产品ID")
    private Long projectId;

    @ApiModelProperty("AppID")
    private Long projectAppId;

    @ApiModelProperty("环境")
    private String servicesEnv;

    @ApiModelProperty("管理员ID")
    private Long adminUserId;

    @ApiModelProperty("最近一次任务ID")
    private Long jenkinsTaskGroupId;

    @ApiModelProperty("最近一次任务操作类型")
    private Integer action;

    @ApiModelProperty("最近一次任务是否成功")
    private Integer isSuccess;

    @ApiModelProperty("最近一次任务失败原因")
    private String failedReason;

    @ApiModelProperty("最近一次任务完成时间")
    private LocalDateTime completeTime;


    @ApiModelProperty("管理员名称")
    private String adminUserName;

    @ApiModelProperty("应用名称")
    private String projectAppName;

    @ApiModelProperty("项目名称")
    private String projectName;
}
