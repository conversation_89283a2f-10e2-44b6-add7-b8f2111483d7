package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.service.AdminAppPermissionService;
import com.mega.platform.cloud.admin.vo.AdminAppPermissionListReqVO;
import com.mega.platform.cloud.admin.vo.AdminAppPermissionListRespVO;
import com.mega.platform.cloud.admin.vo.AdminAppPermissionUpdateReqVO;
import com.mega.platform.cloud.admin.vo.AdminUrlPatternListReqVO;
import com.mega.platform.cloud.admin.vo.AdminUrlPatternListRespVO;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * App访问平台权限控制器
 */
@RestController
@RequestMapping("/admin/api")
@Api(tags = "App访问平台权限管理")
@Slf4j
public class AdminAppPermissionController {

    private final AdminAppPermissionService adminAppPermissionService;

    @Autowired
    public AdminAppPermissionController(AdminAppPermissionService adminAppPermissionService) {
        this.adminAppPermissionService = adminAppPermissionService;
    }

    /**
     * 获取路由配置列表
     *
     * @return 路由配置列表
     */
    @PostMapping("/public/app/permission/url-pattern-list")
    @ApiOperation("获app取路由列表")
    public Result<Set<String>> urlPatternList() {
        Set<String> result = adminAppPermissionService.urlPatternList();
        return Results.success(result);
    }


    /**
     * 获取App权限列表
     *
     * @param reqVO 请求参数
     * @return App权限列表
     */
    @PostMapping("/{projectId}/app/permission/list")
    @ApiOperation("获取App权限列表")
    public Result<AdminAppPermissionListRespVO> appPermissionList( @PathVariable("projectId") Long projectId,
            @Validated @RequestBody AdminAppPermissionListReqVO reqVO) {
        AdminAppPermissionListRespVO result = adminAppPermissionService.appPermissionList(projectId, reqVO);
        return Results.success(result);
    }

    /**
     * 更新单个App权限
     *
     * @param projectId 项目ID
     * @param reqVO 权限更新请求参数
     * @return 操作结果
     */
    @PostMapping("/{projectId}/app/permission/update")
    @ApiOperation("更新App权限")
    public Result<?> updateAppPermission(@PathVariable("projectId") Long projectId,
            @Validated @RequestBody AdminAppPermissionUpdateReqVO reqVO) {
        adminAppPermissionService.updateAppPermission(projectId, reqVO);
        return Results.success();
    }

}
