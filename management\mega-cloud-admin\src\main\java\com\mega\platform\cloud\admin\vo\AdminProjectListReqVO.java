package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 项目列表查询参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "项目列表查询参数", description = "项目列表查询参数")
public class AdminProjectListReqVO {

    @ApiModelProperty(value = "页码", required = false, example = "1")
    private Integer pageNum;
    
    @ApiModelProperty(value = "页大小", required = false, example = "20")
    private Integer pageSize;

    @ApiModelProperty(value = "项目名称（模糊查询）,可以为空", required = false, example = "")
    private String name;
    
    @ApiModelProperty(value = "状态 -1:查询全部  0：不可用 1：正常 2：挂起 3：审核中", required = false, example = "-1")
    private Integer status;
}
