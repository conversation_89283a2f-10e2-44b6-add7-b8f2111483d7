# 云服务配置参数说明 📋

## 一致性参数 (所有服务共用) 🔁

| 参数名称 | 参数值 | 描述 |
| :--- | :--- | :--- |
| `vpc_id` | vpc-usjxc10mk | 虚拟私有云ID，用于网络隔离 |
| `subnet_id` | vsw-bp1lw1longj7zoxy5txez | 子网ID，指定网络划分 |
| `region_id` | cn-hangzhou | 地域ID，资源所在的地理区域 |
| `region_zone` | cn-hangzhou-i | 可用区，地域内电力和网络独立的区域 |
| `charging_mode` | 按需 | 计费模式，按实际使用量付费 |
| `internet_charge_type` | 按量 | 公网流量计费类型，按实际流出流量计费 |

---

## 各服务特性参数

### 🌐 Nginx 服务参数

| 参数名称 | 参数值 | 描述 |
| :--- | :--- | :--- |
| `安全组` | sg-bp1atg4jn10vcvy39lch, sg-bp1fvp7j3mibt0kxcs0p | 安全组ID，控制实例的入出流量 |
| `flavor_ref` | ecs.t6-c1m1.large | 实例规格，2 核（vCPU）2 GiB|
| `credit_specification` | Unlimited | 积分类型，无性能约束模式 |
| `系统盘` | 40GiB ESSD AutoPL 云盘 突发型无限制 | 系统盘类型、大小及性能描述 |

### 🐬 MySQL 服务参数

| 参数名称 | 参数值 | 描述 |
| :--- | :--- | :--- |
| `安全组` | sg-bp1atg4jn10vcvy39lch, sg-bp1cijfyinwbr2fqdjoc | 安全组ID，控制实例的入出流量 |
| `flavor_ref` | ecs.e-c1m4.xlarge | 实例规格，4 核（vCPU）16 GiB |
| `credit_specification` | Standard | 积分类型，标准模式 |
| `系统盘` | 40GiB ESSD 云盘 PL0 | 系统盘类型、大小及性能等级 |
| `数据盘` | 500GiB ESSD AutoPL 云盘 突发型无限制 | 数据盘类型、大小及性能描述 |


数据盘json

```json
  {
    "size": 500,
    "category": "cloud_auto",
    "snapshotId": "s-bp1ewag6nesce62x7gr4",
    "performanceLevel": "",
    "device": "/dev/xvdb"
  }

```


### 🔴 Redis 服务参数

| 参数名称 | 参数值 | 描述 |
| :--- | :--- | :--- |
| `安全组` | sg-bp1atg4jn10vcvy39lch | 安全组ID，控制实例的入出流量 |
| `flavor_ref` | ecs.t6-c1m2.large | 实例规格，2 核（vCPU）4 GiB |
| `credit_specification` | Unlimited | 积分类型，无性能约束模式 |
| `系统盘` | 40GiB ESSD 云盘 PL0 | 系统盘类型、大小及性能等级 |
| `数据盘` | 100GiB ESSD AutoPL 云盘 突发型无限制 | 数据盘类型、大小及性能描述 |



数据盘json
```json

  {
    "size": 100,
    "category": "cloud_auto",
    "performanceLevel": "",
    "snapshotId": "s-bp11vjm1s4nfo1knuvuv",
    "device": "/dev/xvdb"
  }

```

### 🛠️ Services 服务参数

| 参数名称 | 参数值 | 描述 |
| :--- | :--- | :--- |
| `安全组` | sg-bp1atg4jn10vcvy39lch | 安全组ID，控制实例的入出流量 |
| `flavor_ref` | ecs.e-c1m2.2xlarge | 实例规格，8 核（vCPU）16 GiB |
| `credit_specification` | Standard | 积分类型，标准模式 |
| `系统盘` | 40GiB ESSD Entry 云盘 | 系统盘类型及大小 |
| `数据盘` | 100GiB ESSD AutoPL 云盘 突发型无限制 | 数据盘类型、大小及性能描述 |



数据盘json
```json

  {
    "size": 100,
    "category": "cloud_auto",
    "performanceLevel": "",
    "device": "/dev/vdb1",
    "diskName": "data"
  }

```