package com.mega.platform.cloud.data.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("苹果支付平台配置")
public class PaymentAppleConfig {
    @ApiModelProperty("你 App 的 Bundle ID，用于限定请求作用域")
    @JsonProperty("bundleId")
    private String bundleId;

    @ApiModelProperty("Apple 的 Issuer ID，标识你的 Apple Developer 帐号")
    @JsonProperty("issuerId")
    private String issuerId;

    @ApiModelProperty("App 在 App Store 上的唯一数字 ID")
    @JsonProperty("appleAppId")
    private Long appleAppId;

    @ApiModelProperty("Apple 后台提供的 Key ID，与你的私钥成对使用")
    @JsonProperty("keyId")
    private String keyId;

    @ApiModelProperty("私钥路径（.p8 文件）")
    @JsonProperty("privateKeyPath")
    private String privateKeyPath;
}
