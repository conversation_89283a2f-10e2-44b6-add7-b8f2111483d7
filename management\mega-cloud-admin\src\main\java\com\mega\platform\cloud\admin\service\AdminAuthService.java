package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.constant.AdminAuthConstant;
import com.mega.platform.cloud.admin.dao.AdminAuthDao;
import com.mega.platform.cloud.admin.vo.AdminAuthLoginReqVO;
import com.mega.platform.cloud.admin.vo.AdminAuthLoginRespVO;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO;
import com.mega.platform.cloud.data.entity.AdminRole;
import com.mega.platform.cloud.data.entity.AdminRouter;
import com.mega.platform.cloud.data.entity.AdminUser;
import com.mega.platform.cloud.data.entity.Project;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO.AdminRoleVO;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO.AdminSecondRouterVO;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO.AdminThirdRouterVO;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO.AdminProjectVO;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO.AdminFirstRouterVO;
import com.mega.platform.cloud.admin.dto.AdminTokenPayload;
import com.mega.platform.cloud.admin.util.AdminCryptUtils;

import java.util.stream.Collectors;
import java.util.concurrent.TimeUnit;

/**
 * 管理员认证服务层
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdminAuthService {

    private final AdminAuthDao adminAuthDao;
    private final AdminAccessCacheService adminAccessCacheService;
    

    @Autowired
    public AdminAuthService(AdminAuthDao adminAuthDao, AdminAccessCacheService adminAccessCacheService) {
        this.adminAuthDao = adminAuthDao;
        this.adminAccessCacheService = adminAccessCacheService;
    }

    /**
     * 角色权限+直接分配权限
     * 
     * @param adminUserId
     * @param roleIds
     * @return
     */
    private Set<AdminRouter> getALlRouters(Long adminUserId, Set<Long> roleIds) {
        // 1. 查询角色路由
        List<AdminRouter> roleRouters = CollectionUtils.isEmpty(roleIds) ? new ArrayList<>()
                : adminAuthDao.findRoutersByRoleIds(new ArrayList<>(roleIds));
        // 2. 查询用户路由
        List<AdminRouter> userRouters = adminAuthDao.findRoutersByAdminUserId(adminUserId);
        // 基于id合并权限并去重
        Map<Long, AdminRouter> routerMap = new HashMap<>();
        // 先添加角色路由
        roleRouters.forEach(router -> routerMap.put(router.getId(), router));
        // 再添加用户路由，如果id相同则覆盖
        userRouters.forEach(router -> routerMap.put(router.getId(), router));
        Set<AdminRouter> allRouters = new HashSet<>(routerMap.values());
        return allRouters;
    }

    /**
     * 获取管理员所有项目（角色项目 + 直接分配项目）
     * @param adminUserId
     * @param roleIds
     * @return
     */
    private Set<Project> getAllProjects(Long adminUserId, Set<Long> roleIds) {
        List<Project> roleProjects = CollectionUtils.isEmpty(roleIds) ? new ArrayList<>()
                : adminAuthDao.findProjectsByRoleIds(new ArrayList<>(roleIds));
        List<Project> userProjects = adminAuthDao.findProjectsByAdminUserId(adminUserId);
        // 基于id合并项目并去重
        Map<Long, Project> projectMap = new HashMap<>();
        // 先添加角色项目
        roleProjects.forEach(project -> projectMap.put(project.getId(), project));
        // 再添加用户项目，如果id相同则覆盖
        userProjects.forEach(project -> projectMap.put(project.getId(), project));
        Set<Project> allProjects = new HashSet<>(projectMap.values());
        return allProjects;
    }

    /**
     * 管理员登录
     *
     * @param reqVO 登录请求参数
     * @return 登录响应结果
     */
    @Transactional
    public AdminAuthLoginRespVO login(AdminAuthLoginReqVO reqVO) {

        // 1. 验证用户名和密码
        AdminUser adminUser = adminAuthDao.findByUsername(reqVO.getUsername());
        if (adminUser == null) {
            throw new AdminException(4000, "用户名或密码错误");
        }
        if (!adminUser.getPassword().equals(reqVO.getPassword())) {
            throw new AdminException(4000, "用户名或密码错误");
        }

        // 2. 查询用户角色
        List<AdminRole> roles = adminAuthDao.findRolesByAdminUserId(adminUser.getId());
        Set<Long> roleIds = roles.stream().map(AdminRole::getId).collect(Collectors.toSet());

        // 3. 查询用户权限（角色权限 + 直接分配权限）
        Set<AdminRouter> allRouters = getALlRouters(adminUser.getId(), roleIds);
        Set<String> permissions = allRouters.stream()
                .map(AdminRouter::getBackendPath)
                .filter(StringUtils::hasText)
                .collect(Collectors.toSet());

        // 4. 查询用户项目（角色项目 + 直接分配项目）
        Set<Project> allProjects = getAllProjects(adminUser.getId(), roleIds);
        Set<Long> projectIds = allProjects.stream().map(Project::getId).collect(Collectors.toSet());

        // 5. 生成新的token版本
        String token = adminAccessCacheService.refreshToken(adminUser.getId(), roleIds, projectIds, permissions);
        // 11. 更新最后登录时间
        adminAuthDao.updateLastLoginTime(adminUser.getId());

        // 12. 构建响应结果
        AdminAuthLoginRespVO respVO = new AdminAuthLoginRespVO();
        respVO.setToken(token);
        return respVO;
    }

    /**
     * 获取管理员用户详细信息
     *
     * @param adminUserId 管理员用户ID
     * @return 管理员用户详细信息
     */
    public AdminAuthProfileRespVO getProfile(Long adminUserId) {
        // 1. 查询用户信息
        AdminUser adminUser = adminAuthDao.findById(adminUserId);
        if (adminUser == null) {
            throw new AdminException(4000, "用户不存在");
        }

        // 2. 查询用户角色
        List<AdminRole> roles = adminAuthDao.findRolesByAdminUserId(adminUser.getId());
        Set<Long> roleIds = roles.stream().map(AdminRole::getId).collect(Collectors.toSet());

        // 构建角色VO列表
        List<AdminRoleVO> roleVOs = roles.stream().map(role -> {
            AdminRoleVO roleVO = new AdminRoleVO();
            roleVO.setId(role.getId());
            roleVO.setName(role.getName());
            roleVO.setDescription(role.getDescription());
            return roleVO;
        }).collect(Collectors.toList());

        // 3. 查询用户权限（角色权限 + 直接分配权限）
        Set<AdminRouter> allRouters = getALlRouters(adminUser.getId(), roleIds);
        // 4. 查询用户项目（角色项目 + 直接分配项目）
        Set<Project> allProjects = getAllProjects(adminUser.getId(), roleIds);
        // 5. 包装前端路由
        List<AdminFirstRouterVO> firstRouterVOs = boxFrontendRouters(allRouters, allProjects);

        // 构建项目VO列表
        List<AdminProjectVO> projectVOs = allProjects.stream()
            .sorted(Comparator.comparing(Project::getId))
            .map(project -> {
                AdminProjectVO projectVO = new AdminProjectVO();
                projectVO.setId(project.getId());
                projectVO.setName(project.getName());
                return projectVO;
            }).collect(Collectors.toList());

        // 5. 构建响应结果
        AdminAuthProfileRespVO respVO = new AdminAuthProfileRespVO();
        respVO.setAdminUserId(adminUser.getId());
        respVO.setAdminUserName(adminUser.getUsername());
        respVO.setRoles(roleVOs);
        respVO.setProjects(projectVOs);
        respVO.setRouters(firstRouterVOs);
        // log.info("获取管理员用户详细信息成功，用户ID: {}, 用户名: {}", adminUser.getId(),
        // adminUser.getUsername());
        return respVO;
    }

    private List<AdminFirstRouterVO> boxFrontendRouters(Set<AdminRouter> allRouters, Set<Project> allProjects) {
        // 处理前端路由 - 构建父子路由关系
        List<AdminFirstRouterVO> firstRouters = new ArrayList<>();
        // 系统级别路由
        Map<Long, List<AdminSecondRouterVO>> systemContentMap = new HashMap<>();
        // 项目级别路由
        List<AdminSecondRouterVO> projectRouters = new ArrayList<>();
        Map<Long, List<AdminThirdRouterVO>>  projectContentMap = new HashMap<>();


        // 使用一个循环处理所有路由
        for (AdminRouter router : allRouters) {
            // 跳过没有前端路径的路由
            if (!StringUtils.hasText(router.getFrontendPath())) {
                continue;
            }
            log.info("routerId: {}", router.getId());

            // system级别
            if (router.getRouterCate() == 1) {
                if (router.getParentAdminRouterId() == null || router.getParentAdminRouterId() == 0L) {
                    //根路由
                    AdminFirstRouterVO parentRouterVO = new AdminFirstRouterVO();
                    parentRouterVO.setId(router.getId());
                    parentRouterVO.setFrontendPath(router.getFrontendPath());
                    parentRouterVO.setFrontendName(router.getFrontendName());
                    parentRouterVO.setTapName(router.getFrontendName());
                    parentRouterVO.setSort(router.getSort());
                    parentRouterVO.setChildRouters(new ArrayList<>());
                    firstRouters.add(parentRouterVO);
                } else {
                    //子路由
                    AdminSecondRouterVO childRouterVO = new AdminSecondRouterVO();
                    childRouterVO.setId(router.getId());
                    childRouterVO.setFrontendPath(router.getFrontendPath());
                    childRouterVO.setFrontendName(router.getFrontendName());
                    childRouterVO.setTapName(router.getFrontendName());
                    childRouterVO.setSort(router.getSort());
                    systemContentMap.computeIfAbsent(router.getParentAdminRouterId(), k -> new ArrayList<>())
                        .add(childRouterVO);
                }
            }
            // project级别
            else if(router.getRouterCate() == 2){
                if (router.getParentAdminRouterId() == null || router.getParentAdminRouterId() == 0L) {
                    // 根路由
                    AdminSecondRouterVO secondRouterVO = new AdminSecondRouterVO();
                    secondRouterVO.setId(router.getId());
                    secondRouterVO.setFrontendPath(router.getFrontendPath());
                    secondRouterVO.setFrontendName(router.getFrontendName());
                    secondRouterVO.setTapName(router.getFrontendName());
                    secondRouterVO.setSort(router.getSort());
                    secondRouterVO.setChildRouters(new ArrayList<>());
                    projectRouters.add(secondRouterVO);
                } else {
                    // 子路由
                    AdminThirdRouterVO childRouterVO = new AdminThirdRouterVO();
                    childRouterVO.setId(router.getId());
                    childRouterVO.setFrontendPath(router.getFrontendPath());
                    childRouterVO.setFrontendName(router.getFrontendName());
                    childRouterVO.setTapName(router.getFrontendName());
                    childRouterVO.setSort(router.getSort());
                    projectContentMap.computeIfAbsent(router.getParentAdminRouterId(), k -> new ArrayList<>())
                        .add(childRouterVO);
                }
            }
        }

        // 系统级别子路由录入
        if(systemContentMap.size() > 0){
            firstRouters = firstRouters.stream()
                .peek(firstRouterVO -> {
                    List<AdminSecondRouterVO> childRouters = systemContentMap.get(firstRouterVO.getId());
                    if (childRouters != null && !childRouters.isEmpty()) {
                        childRouters = childRouters.stream()
                            .sorted(Comparator.comparing(AdminSecondRouterVO::getSort, Comparator.nullsLast(Comparator.naturalOrder()))
                                .thenComparing(AdminSecondRouterVO::getId))
                            .collect(Collectors.toList());
                        firstRouterVO.setChildRouters(childRouters);
                    }
                })
                .sorted(Comparator.comparing(AdminFirstRouterVO::getSort, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(AdminFirstRouterVO::getId))
                .collect(Collectors.toList());
        }


        // 项目路由录入
        if (projectContentMap.size() > 0) {
            for (AdminSecondRouterVO projectRouterVO : projectRouters) {
                List<AdminThirdRouterVO> childRouters = projectContentMap.get(projectRouterVO.getId());
                if(childRouters == null || childRouters.size() == 0){
                    continue;
                }
                
                childRouters.sort(Comparator.comparing(AdminThirdRouterVO::getSort, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(AdminThirdRouterVO::getId));
                
                projectRouterVO.setChildRouters(childRouters);
            }
        }
        // 对项目列表按ID升序排序
        List<Project> sortedProjects = allProjects.stream()
                .sorted(Comparator.comparing(Project::getId))
                .collect(Collectors.toList());

        for (int i = 0; i < sortedProjects.size(); i++) {
            Project project = sortedProjects.get(i);
            AdminFirstRouterVO firstRouterVO = new AdminFirstRouterVO();
            firstRouterVO.setId(project.getId() * 10000);
            firstRouterVO.setFrontendPath(String.format("/%s", project.getId()));
            firstRouterVO.setFrontendName(project.getName());
            firstRouterVO.setTapName(project.getName());

            // 对二级路由按ID排序 - 创建深拷贝避免修改原始数据
            List<AdminSecondRouterVO> sortedSecondRouters = projectRouters.stream()
                    .map(router -> {
                        AdminSecondRouterVO newRouter = new AdminSecondRouterVO();
                        newRouter.setId(router.getId());
                        newRouter.setFrontendPath(String.format("/%s%s", project.getId(), router.getFrontendPath()));
                        newRouter.setFrontendName(router.getFrontendName());
                        newRouter.setTapName(String.format("%s - %s", project.getName(), router.getFrontendName()));
                        newRouter.setSort(router.getSort());
                        
                        if (router.getChildRouters() != null) {
                            // 对三级路由按ID排序 - 创建深拷贝
                            List<AdminThirdRouterVO> sortedThirdRouters = router.getChildRouters().stream()
                                    .map(thirdRouter -> {
                                        AdminThirdRouterVO newThirdRouter = new AdminThirdRouterVO();
                                        newThirdRouter.setId(thirdRouter.getId());
                                        newThirdRouter.setFrontendPath(String.format("/%s%s", project.getId(), thirdRouter.getFrontendPath()));
                                        newThirdRouter.setFrontendName(thirdRouter.getFrontendName());
                                        newThirdRouter.setTapName(String.format("%s - %s", project.getName(), thirdRouter.getFrontendName()));
                                        newThirdRouter.setSort(thirdRouter.getSort());
                                        return newThirdRouter;
                                    })
                                    .sorted(Comparator.comparing(AdminThirdRouterVO::getSort, Comparator.nullsLast(Comparator.naturalOrder()))
                                        .thenComparing(AdminThirdRouterVO::getId))
                                    .collect(Collectors.toList());
                            newRouter.setChildRouters(sortedThirdRouters);
                        }
                        return newRouter;
                    })
                    .sorted(Comparator.comparing(AdminSecondRouterVO::getSort, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(AdminSecondRouterVO::getId))
                    .collect(Collectors.toList());

            firstRouterVO.setChildRouters(sortedSecondRouters);
            firstRouters.add(firstRouterVO);
        }
        return firstRouters;
    }

    /**
     * 管理员登出
     *
     * @param adminUserId 管理员用户ID
     */
    public void logout(Long adminUserId) {
        log.info("管理员登出成功，用户ID: {}", adminUserId);
    }

    /**
     * 管理员全部登出
     *
     * @param adminUserId 管理员用户ID
     */
    public void logoutAll(Long adminUserId) {
        adminAccessCacheService.deleteToken(adminUserId);
    }
}