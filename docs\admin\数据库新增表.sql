# 数据库新增表


-- 新建一个mysql表`project_app_template`，包含字段

CREATE TABLE `project_app_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新建项目时必须创建的app';



-- 模版数据
INSERT INTO `project_app_template` (`app_name`, `remark`) VALUES
('BASE', '用于挂在无固定分类服务'),
('ACCOUNT', '用于用户登录注册验证'),
('PUSH', '用于用户推送服务'),
('ADMIN', '应用后台服务'),
('MONITOR', '监控服务'),
('EXCHANGE', '交易服务');


-- 新建一个mysql表project_app_permission_template，包含字段
CREATE TABLE `project_app_permission_template` (
  id int(11) NOT NULL AUTO_INCREMENT,
  `app_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url_pattern` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目应用权限模板表';

-- 模版权限数据
INSERT INTO `project_app_permission_template` (`app_name`, `url_pattern`) VALUES
('BASE', '/access/api/app/**'),
('BASE', '/access/api/partner/**'),
('BASE', '/auth/api/verification/**'),
('BASE', '/microserviceapp/api/services/**'),
('BASE', '/microserviceapp/api/services/group/**'),
('BASE', '/monitor/api/metrics/**'),
('BASE', '/monitor/api/turnover/**'),
('BASE', '/payment/api/verification/**'),
('BASE', '/push/api/push/**'),
('ACCOUNT', '/access/api/app/**'),
('ACCOUNT', '/access/api/partner/**'),
('ACCOUNT', '/auth/api/verification/**'),
('ACCOUNT', '/monitor/api/metrics/**'),
('PUSH', '/access/api/app/**'),
('PUSH', '/access/api/partner/**'),
('PUSH', '/monitor/api/metrics/**'),
('PUSH', '/push/api/push/**'),
('ADMIN', '/access/api/app/**'),
('ADMIN', '/access/api/partner/**'),
('ADMIN', '/monitor/api/metrics/**'),
('ADMIN', '/push/api/push/**'),
('MONITOR', '/access/api/app/**'),
('MONITOR', '/access/api/partner/**'),
('MONITOR', '/monitor/api/metrics/**'),
('MONITOR', '/monitor/api/turnover/**'),
('EXCHANGE', '/access/api/app/**'),
('EXCHANGE', '/access/api/partner/**'),
('EXCHANGE', '/monitor/api/metrics/**'),
('EXCHANGE', '/monitor/api/turnover/**'),
('EXCHANGE', '/payment/api/verification/**');
