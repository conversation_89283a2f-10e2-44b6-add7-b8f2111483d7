package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.admin.dto.AdminAliEcsImageDTO;
import com.mega.platform.cloud.admin.vo.AdminEcsImageCreateReqVO;
import com.mega.platform.cloud.admin.vo.AdminEcsImageDeleteReqVO;
import com.mega.platform.cloud.admin.vo.AdminEcsImageListReqVO;
import com.mega.platform.cloud.admin.vo.AdminEcsImageListRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * ECS镜像管理数据访问层
 */
@Repository
public interface AdminEcsImageDao {

    /**
     * 根据条件查询ECS镜像列表
     * @param reqVO 查询条件
     * @return ECS镜像列表
     */
    List<AdminEcsImageListRespVO.EcsServerImageVO> selectEcsImageListByCondition(@Param("req") AdminEcsImageListReqVO reqVO);


    /**
     * 删除ECS镜像记录
     * @param reqVO 删除参数
     * @param adminUserId 管理员ID
     * @return 影响行数
     */
    int deleteEcsImage(@Param("req") AdminEcsImageDeleteReqVO reqVO, @Param("adminUserId") Long adminUserId);

    /**
     * 根据镜像ID查询镜像是否存在
     * @param imageId 镜像ID
     * @param projectId 项目ID
     * @return 镜像数量
     */
    int countEcsImageById(@Param("imageId") String imageId, @Param("projectId") String projectId);

    /**
     * 根据ECS服务器ID查询镜像是否存在
     * @param ecsServerId ECS服务器ID
     * @param projectId 项目ID
     * @return 镜像数量
     */
    int countEcsImageByEcsServerId(@Param("ecsServerId") String ecsServerId, @Param("projectId") String projectId);

    /**
     * 查询创建中的镜像列表
     * @param status 1 创建完成  2 正在创建
     * @return 镜像列表
     */
    List<AdminAliEcsImageDTO> getAliCreatingImageList(@Param("status") Integer status);

    /**
     * 根据镜像ID查询镜像
     * @param imageId 镜像ID
     * @return 镜像
     */
    AdminAliEcsImageDTO getAliImageById(@Param("imageId") Long imageId);

    /**
     * 更新ECS镜像状态
     * @param ecsServerImageId ECS镜像ID
     * @param status 状态 1 创建完成  2 正在创建
     * @param dataDiskInfo 数据盘信息
     */
    void updateEcsImageStatus(@Param("ecsServerImageId") Integer ecsServerImageId, @Param("status") int status, @Param("dataDiskInfo") String dataDiskInfo);
}
