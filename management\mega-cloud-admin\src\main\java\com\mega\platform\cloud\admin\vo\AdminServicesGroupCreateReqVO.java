package com.mega.platform.cloud.admin.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 服务组创建请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "服务组创建请求参数", description = "服务组创建请求参数")
public class AdminServicesGroupCreateReqVO {
    
    /**
     * 模板参数值（JSON字符串）
     */
    @ApiModelProperty(value = "模板参数值", required = false, example = "{\"appName\":\"test\",\"cloudRestartFileName\":\"测试\",\"gitBranch\":\"master\",\"gitUrl\":\"test\",\"groupId\":\"testGroupId\",\"moduleName\":\"testModuleName\"}")
    private String jenkinsParams;
    
    @ApiModelProperty(hidden = true)
    private Long adminUserId;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id", required = false, example = "1")
    private Long jenkinsTemplateId;
    
    /**
     * jenkins实例id
     */
    @NotNull(message = "jenkins实例id不能为空")
    @ApiModelProperty(value = "jenkins实例id", required = true, example = "1")
    private Long jenkinsServiceId;
    
    @NotNull(message = "appId不能为空")
    @ApiModelProperty(value = "appId", required = true, example = "1")
    private Long projectAppId;

    /**
     * 服务组名
     */
    @NotBlank(message = "服务组名不能为空")
    @ApiModelProperty(value = "服务组名", required = true, example = "test-service-group")
    private String servicesGroupName;
    
    /**
     * 启动方式
     */
    @NotNull(message = "启动方式不能为空")
    @ApiModelProperty(value = "启动方式", required = true, example = "1", notes = "1-普通重启 2-滚服重启 3-导流重启 4-脚本运行")
    private Integer servicesUpdateId;
    
    /**
     * 环境
     */
    @NotBlank(message = "环境不能为空")
    @ApiModelProperty(value = "环境", required = true, example = "dev", notes = "dev | test | beta | prod")
    private String servicesEnv;
    
    /**
     * 保活数量
     */
    @ApiModelProperty(value = "保活数量", required = false, example = "2", notes = "如果是滚服/导流重启配置需要保活的数量")
    private Integer servicesAliveNum = 0;
    
    /**
     * 保活检测方式
     */
    @ApiModelProperty(value = "保活检测方式", required = false, example = "1", notes = "保活状态检查方式")
    private Integer checkAliveType = 1;
    
 
    /**
     * 微服务日志格式id
     */
    @ApiModelProperty(value = "微服务日志格式id(ext)", required = false)
    private Long servicesLogFormatId;

    /**
     * 1自研 3三方
     */
    @ApiModelProperty(value = "1自研 3三方（ext）", required = false, example = "1", notes = "1自研 3三方")
    private Integer isSelf;

    
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注(ext)", required = false, example = "用户服务组")
    private String remark;
    
    /**
     * 组标签
     */
    @ApiModelProperty(value = "组标签(ext)", required = false, example = "[1,2,3]")
    private List<BigInteger> tags;

    /**
     * 是否使用jenkins
     */
    @ApiModelProperty(value = "是否使用jenkins", required = false, example = "1", notes = "0-不用 1-用")
    private Integer useJenkins = 1;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", required = false, example = "0")
    private Integer sort = 0;


}
