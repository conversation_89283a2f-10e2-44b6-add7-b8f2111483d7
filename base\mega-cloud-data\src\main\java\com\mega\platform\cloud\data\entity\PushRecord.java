package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "push_record")
public class PushRecord {
    /**
     * 推送唯一id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name = "app_id")
    private Long appId;

    /**
     * 推送业务id
     */
    @Column(name = "push_id")
    private Long pushId;

    /**
     * 推送渠道id
     */
    @Column(name = "device_push_channel_id")
    private Integer devicePushChannelId;

    /**
     * 推送数量
     */
    @Column(name = "push_count")
    private Integer pushCount;

    /**
     * 0=未完成, 1=成功 2=失败
     */
    @Column(name = "status")
    private Byte status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 推送完成时间
     */
    @Column(name = "complete_time")
    private Date completeTime;

    /**
     * 失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;
}