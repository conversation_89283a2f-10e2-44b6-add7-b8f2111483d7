<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminEcsImageDao">

    <!-- 根据条件查询ECS镜像列表 -->
    <!-- 查询条件SQL片段 -->
    <sql id="ecsImageQueryConditions">
        <where>
            t.delsign = 0
            <if test="req.projectId != null and req.projectId != ''">
                AND t.project_id = #{req.projectId}
            </if>
            <if test="req.name != null and req.name != ''">
                AND t.name LIKE CONCAT('%', #{req.name}, '%')
            </if>
            <if test="req.remark != null and req.remark != ''">
                AND t.remark LIKE CONCAT('%', #{req.remark}, '%')
            </if>
            <if test="req.adminUserId != null and req.adminUserId != ''">
                AND t.admin_user_id = #{req.adminUserId}
            </if>
            <if test="req.ecsType != null">
                AND t.ecs_type = #{req.ecsType}
            </if>
            <if test="req.status != null">
                AND t.status = #{req.status}
            </if>
            <if test="req.version != null and req.version != ''">
                AND t.version = #{req.version}
            </if>
            <if test="req.tagList != null and req.tagList != ''">
                AND EXISTS (
                    SELECT 1 
                    FROM ecs_server_image_tag_relation tr
                    WHERE tr.ecs_server_image_id = t.id 
                    AND tr.delsign = 0
                    AND tr.ecs_tag_id IN 
                    <foreach collection="req.tagList.split(',')" item="tagId" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                )
            </if>
            <if test="req.customTagList != null and req.customTagList != ''">
               AND EXISTS (
                    SELECT 1 
                    FROM ecs_server_image_tag_relation tr
                    WHERE tr.ecs_server_image_id = t.id 
                    AND tr.delsign = 0
                    AND tr.ecs_tag_id IN 
                    <foreach collection="req.customTagList.split(',')" item="tagId" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                )
            </if>
        </where>
    </sql>

    <!-- 根据条件查询ECS镜像列表 -->
    <select id="selectEcsImageListByCondition" resultType="com.mega.platform.cloud.admin.vo.AdminEcsImageListRespVO$EcsServerImageVO">
        SELECT 
            t.id,
            t.name,
            t.project_id AS projectId,
            p.name AS projectName,
            es.ecs_type AS ecsType,
            dic.`name` AS ecsTypeName,
						er.`name` AS regionStr, 
						er.zone   AS regionZone,
            t.version,
            a.tag_list as tag_list,
            b.tag_list as custom_tag_list,
            t.status,
            CASE 
                WHEN t.status = 1 THEN '创建完成'
                WHEN t.status = 2 THEN '正在创建'
                ELSE '未知状态'
            END AS statusName,
            t.admin_user_id AS adminUserId,
            u.username AS adminUserName,
            t.remark,
            t.create_time AS createTime,
            t.update_time AS updateTime,
            t.from_ecs_server_id AS fromEcsServerId,
            t.extra_data_disk,
			es.`name` AS fromEcsServerMame
        FROM ecs_server_image t
		LEFT JOIN ecs_server AS es ON t.from_ecs_server_id = es.id
		LEFT JOIN ecs_server_config AS ec ON ec.id = es.ecs_server_config_id
		LEFT JOIN ecs_server_region AS er ON er.id = ec.ecs_server_region_id
        LEFT JOIN project p ON t.project_id = p.id
        LEFT JOIN admin_user u ON t.admin_user_id = u.id
        LEFT JOIN dic ON dic.dic_cate_id = 2013 AND dic.`value` = es.ecs_type
		LEFT JOIN (
            SELECT esrr.ecs_server_image_id, GROUP_CONCAT(DISTINCT dic.id) as tag_list FROM ecs_server_image_tag_relation AS esrr
            INNER JOIN dic ON esrr.ecs_tag_id = dic.id AND dic.dic_cate_id = 2011
        ) as a ON a.ecs_server_image_id = t.id
        LEFT JOIN (
            SELECT esrr.ecs_server_image_id, GROUP_CONCAT(DISTINCT dic.id) as tag_list FROM ecs_server_image_tag_relation AS esrr
            INNER JOIN dic ON esrr.ecs_tag_id = dic.id AND dic.dic_cate_id = 2012
        ) as b ON b.ecs_server_image_id = t.id

        <include refid="ecsImageQueryConditions"/>
        ORDER BY t.id DESC
    </select>


    <!-- 删除ECS镜像记录 -->
    <update id="deleteEcsImage">
        UPDATE ecs_server_image
        SET 
            delsign = 1,
            update_time = NOW()
        WHERE id = #{req.imageId}
        AND delsign = 0
    </update>

    <!-- 更新镜像状态 -->
    <update id="updateEcsImageStatus">
        UPDATE ecs_server_image
        SET
            status = #{status},
            update_time = NOW()
            <if test="dataDiskInfo != null and dataDiskInfo != ''">
            , extra_data_disk = #{dataDiskInfo}
            </if>
        WHERE id = #{ecsServerImageId}
        AND delsign = 0
    </update>

    <!-- 根据镜像ID查询镜像是否存在 -->
    <select id="countEcsImageById" resultType="int">
        SELECT COUNT(*)
        FROM ecs_server_image
        WHERE id = #{imageId}
        AND project_id = #{projectId}
        AND delsign = 0
    </select>

    <!-- 根据ECS服务器ID查询镜像是否存在 -->
    <select id="countEcsImageByEcsServerId" resultType="int">
        SELECT COUNT(*)
        FROM ecs_server_image
        WHERE from_ecs_server_id = #{ecsServerId}
        AND project_id = #{projectId}
        AND delsign = 0
    </select>

    <!-- 查询创建中的镜像列表 -->
    <select id="getAliCreatingImageList" resultType="com.mega.platform.cloud.admin.dto.AdminAliEcsImageDTO">
        SELECT
            ei.id,
            ei.name,
            ei.project_id AS projectId,
            from_ecs_server_id AS fromEcsServerId,
            ei.status,
            ei.create_time AS createTime,
            ei.update_time AS updateTime,
			es.`name` AS fromEcsServerName,
			ec.`name` AS fromEcsServerConfigName,
			er.`name` AS regionStr,
			ei.ref,
			er.zone   AS regionZone
        FROM ecs_server_image AS ei
        LEFT JOIN ecs_server AS es ON ei.from_ecs_server_id = es.id
        LEFT JOIN ecs_server_config AS ec ON ec.id = es.ecs_server_config_id
		LEFT JOIN ecs_server_region AS er ON er.id = ec.ecs_server_region_id
        WHERE ei.status = #{status}
        AND ei.delsign = 0
    </select>

    <!-- 根据镜像ID查询镜像 -->
    <select id="getAliImageById" resultType="com.mega.platform.cloud.admin.dto.AdminAliEcsImageDTO">
        SELECT
            ei.id,
            ei.name,
            ei.project_id AS projectId,
            from_ecs_server_id AS fromEcsServerId,
            ei.status,
            ei.create_time AS createTime,
            ei.update_time AS updateTime,
			es.`name` AS fromEcsServerName,
			ec.`name` AS fromEcsServerConfigName,
			er.`name` AS regionStr,
			ei.ref,
			er.zone   AS regionZone
        FROM ecs_server_image AS ei
        LEFT JOIN ecs_server AS es ON ei.from_ecs_server_id = es.id
        LEFT JOIN ecs_server_config AS ec ON ec.id = es.ecs_server_config_id
		LEFT JOIN ecs_server_region AS er ON er.id = ec.ecs_server_region_id
        WHERE ei.id = #{imageId}
        AND ei.delsign = 0
    </select>


</mapper>