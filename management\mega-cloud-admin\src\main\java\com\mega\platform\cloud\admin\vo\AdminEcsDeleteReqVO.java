package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@ApiModel("ECS删除请求")
public class AdminEcsDeleteReqVO {
    
    @ApiModelProperty("ECS ID")
    @NotNull(message = "ECS ID不能为空")
    private Long ecsServerId;
    
    @ApiModelProperty("项目ID（优先级: 低, Feign）")
    private Long projectId;
}