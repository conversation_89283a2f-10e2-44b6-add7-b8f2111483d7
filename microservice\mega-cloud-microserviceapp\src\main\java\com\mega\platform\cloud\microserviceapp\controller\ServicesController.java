package com.mega.platform.cloud.microserviceapp.controller;

import com.mega.platform.cloud.client.microservice.ServicesClient;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.dto.microservice.BuildServicesDTO;
import com.mega.platform.cloud.data.vo.microservice.BuildServicesReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesRespVO;
import com.mega.platform.cloud.microservice.service.ServicesService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "test")
@Slf4j
@RestController
public class ServicesController implements ServicesClient {

    private final ServicesService servicesService;

    @Autowired
    public ServicesController(ServicesService servicesService) {
        this.servicesService = servicesService;
    }

    @Override
    public Result<CreateServicesRespVO> createServices(CreateServicesReqVO vo) throws Exception {
        CreateServicesRespVO respVO =servicesService.createServices(vo);
        return Results.success(respVO);
    }

    @Override
    public Result<?> buildServices(BuildServicesReqVO vo) throws Exception {
        BuildServicesDTO dto = new BuildServicesDTO();
        BeanUtils.copyProperties(vo, dto);
        servicesService.buildServices(dto);
        return Results.success();
    }
}
