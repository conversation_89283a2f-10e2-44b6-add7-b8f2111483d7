package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@ApiModel("ECS创建请求")
public class AdminEcsCreateReqVO {
    
    @ApiModelProperty("项目ID")
    private String projectId;
    
    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空")
    private String name;


    @ApiModelProperty("项目应用ID")
    private String projectAppId;

    @ApiModelProperty("ECS配置ID")
    @NotNull(message = "ECS配置ID不能为空")
    private Long ecsConfigId;

    @ApiModelProperty("ECS标签选择(多选，逗号分隔)")
    private String tagList;
    
    @ApiModelProperty("ECS自定义标签选择(多选，逗号分隔)")
    private String customTagList;
    
    @ApiModelProperty("备注")
    private String remark;
}