package com.mega.platform.cloud.client.access;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.access.AccessPartnerLicenseReqVO;
import com.mega.platform.cloud.data.vo.access.AccessPartnerTelemeringReqVO;
import com.mega.platform.cloud.data.vo.access.AccessPartnerVerifyReqVO;
import com.mega.platform.cloud.data.vo.access.AccessPartnerVerifyRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = "mega-cloud-access-" + "${spring.profiles.active}", contextId = "mega-cloud-access-partner-client")
@RequestMapping("/access/api/partner")
@Api(tags = {"合作方接口", "/access/api/partner"})
public interface AccessPartnerClient {

    @ApiOperation("获取AES密钥")
    @PostMapping("/verify")
    Result<AccessPartnerVerifyRespVO> verify(@Validated @RequestBody AccessPartnerVerifyReqVO vo);

    @ApiOperation("License验证")
    @PostMapping("/license")
    Result<?> license(@Validated @RequestBody AccessPartnerLicenseReqVO vo);

    @ApiOperation("操作日志上报")
    @PostMapping("/telemetering")
    Result<?> telemetering(@Validated @RequestBody AccessPartnerTelemeringReqVO vo);
}
